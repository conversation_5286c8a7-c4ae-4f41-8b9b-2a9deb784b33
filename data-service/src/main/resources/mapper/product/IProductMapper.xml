<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.synqnc.opengw.dataservice.product.mapper.IProductMapper">
  <resultMap id="BaseResultMap" type="com.synqnc.opengw.dataservice.product.model.IProduct">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="opgw_product_key" jdbcType="VARCHAR" property="opgwProductKey" />
    <result column="product_id" jdbcType="VARCHAR" property="productId" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="product_name_en" jdbcType="VARCHAR" property="productNameEn" />
    <result column="product_desc" jdbcType="VARCHAR" property="productDesc" />
    <result column="product_desc_en" jdbcType="VARCHAR" property="productDescEn" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="classify" jdbcType="INTEGER" property="classify" />
    <result column="classify_two" jdbcType="INTEGER" property="classifyTwo" />
    <result column="product_label" jdbcType="VARCHAR" property="productLabel" />
    <result column="product_setting" jdbcType="VARCHAR" property="productSetting" />
    <result column="open_range" jdbcType="VARCHAR" property="openRange" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="online_time" jdbcType="TIMESTAMP" property="onlineTime" />
    <result column="product_score" jdbcType="DOUBLE" property="productScore" />
    <result column="userid" jdbcType="INTEGER" property="userid" />
    <result column="username" jdbcType="VARCHAR" property="username" />
    <result column="realname" jdbcType="VARCHAR" property="realname" />
    <result column="orgid" jdbcType="INTEGER" property="orgid" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="is_oneclick" jdbcType="INTEGER" property="isOneclick" />
    <result column="attempt_type" jdbcType="INTEGER" property="attemptType" />
    <result column="attempt_time" jdbcType="INTEGER" property="attemptTime" />
    <result column="auditor_type" jdbcType="INTEGER" property="auditorType" />
    <result column="is_need_specification" jdbcType="INTEGER" property="isNeedSpecification" />
    <result column="is_gateway_check" jdbcType="INTEGER" property="isGatewayCheck" />
    <result column="protocol_type" jdbcType="VARCHAR" property="protocolType" />
    <result column="call_num30" jdbcType="BIGINT" property="callNum30" />
    <result column="is_need_south_control_platform_audit" jdbcType="INTEGER" property="isNeedSouthControlPlatformAudit" />
    <result column="south_control_platform_id" jdbcType="VARCHAR" property="southControlPlatformId" />
    <result column="call_num6_month" jdbcType="BIGINT" property="callNum6Month" />
    <result column="disorder_task_id" jdbcType="VARCHAR" property="disorderTaskId" />
    <result column="is_test_data" jdbcType="INTEGER" property="isTestData" />
    <result column="need_read_use_notice" jdbcType="INTEGER" property="needReadUseNotice" />
    <result column="read_use_notice_time" jdbcType="INTEGER" property="readUseNoticeTime" />
    <result column="is_data_up_report" jdbcType="INTEGER" property="isDataUpReport" />
    <result column="last_call_time" jdbcType="TIMESTAMP" property="lastCallTime" />
    <result column="is_inalive" jdbcType="INTEGER" property="isInalive" />
    <result column="version" jdbcType="BIGINT" property="version" />
    <result column="doc_id" jdbcType="VARCHAR" property="docId" />
    <result column="en_doc_id" jdbcType="VARCHAR" property="enDocId" />
    <result column="is_home_show" jdbcType="INTEGER" property="isHomeShow" />
    <result column="home_show_order" jdbcType="INTEGER" property="homeShowOrder" />
    <result column="outer_id" jdbcType="VARCHAR" property="outerId" />
    <result column="call_num" jdbcType="BIGINT" property="callNum" />
    <result column="white_list_config" jdbcType="INTEGER" property="whiteListConfig" />
    <result column="recommend_index" jdbcType="INTEGER" property="recommendIndex" />
    <result column="is_mock" jdbcType="INTEGER" property="isMock" />
    <result column="auth_processes" jdbcType="VARCHAR" property="authProcesses" />
    <result column="auth_methods" jdbcType="VARCHAR" property="authMethods" />
    <result column="classify_new" jdbcType="INTEGER" property="classifyNew" />
    <result column="operate_id_list" jdbcType="VARCHAR" property="operateIdList" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.synqnc.opengw.dataservice.product.model.IProduct">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <result column="product_score_sub_item_json" jdbcType="LONGVARCHAR" property="productScoreSubItemJson" />
    <result column="specification" jdbcType="LONGVARCHAR" property="specification" />
    <result column="specification_param" jdbcType="LONGVARCHAR" property="specificationParam" />
    <result column="db_specification_param" jdbcType="LONGVARCHAR" property="dbSpecificationParam" />
    <result column="order_success_notify_users" jdbcType="LONGVARCHAR" property="orderSuccessNotifyUsers" />
    <result column="self_define_auditors" jdbcType="LONGVARCHAR" property="selfDefineAuditors" />
    <result column="outer_info" jdbcType="LONGVARCHAR" property="outerInfo" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    opgw_product_key, product_id, product_name, product_name_en, product_desc, product_desc_en, 
    status, classify, classify_two, product_label, product_setting, open_range, create_time, 
    update_time, online_time, product_score, userid, username, realname, orgid, org_name, 
    is_oneclick, attempt_type, attempt_time, auditor_type, is_need_specification, is_gateway_check, 
    protocol_type, call_num30, is_need_south_control_platform_audit, south_control_platform_id, 
    call_num6_month, disorder_task_id, is_test_data, need_read_use_notice, read_use_notice_time, 
    is_data_up_report, last_call_time, is_inalive, version, doc_id, en_doc_id, is_home_show, 
    home_show_order, outer_id, call_num, white_list_config, recommend_index, is_mock, 
    auth_processes, auth_methods, classify_new, operate_id_list
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    product_score_sub_item_json, specification, specification_param, db_specification_param, 
    order_success_notify_users, self_define_auditors, outer_info
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.synqnc.opengw.dataservice.product.model.IProductExample" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from i_product
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.synqnc.opengw.dataservice.product.model.IProductExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from i_product
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from i_product
    where opgw_product_key = #{opgwProductKey,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from i_product
    where opgw_product_key = #{opgwProductKey,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.synqnc.opengw.dataservice.product.model.IProductExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from i_product
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.synqnc.opengw.dataservice.product.model.IProduct">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into i_product (opgw_product_key, product_id, product_name, 
      product_name_en, product_desc, product_desc_en, 
      status, classify, classify_two, 
      product_label, product_setting, open_range, 
      create_time, update_time, online_time, 
      product_score, userid, username, 
      realname, orgid, org_name, 
      is_oneclick, attempt_type, attempt_time, 
      auditor_type, is_need_specification, is_gateway_check, 
      protocol_type, call_num30, is_need_south_control_platform_audit, 
      south_control_platform_id, call_num6_month, disorder_task_id, 
      is_test_data, need_read_use_notice, read_use_notice_time, 
      is_data_up_report, last_call_time, is_inalive, 
      version, doc_id, en_doc_id, 
      is_home_show, home_show_order, outer_id, 
      call_num, white_list_config, recommend_index, 
      is_mock, auth_processes, auth_methods, 
      classify_new, operate_id_list, product_score_sub_item_json, 
      specification, specification_param, 
      db_specification_param, order_success_notify_users, 
      self_define_auditors, outer_info)
    values (#{opgwProductKey,jdbcType=VARCHAR}, #{productId,jdbcType=VARCHAR}, #{productName,jdbcType=VARCHAR}, 
      #{productNameEn,jdbcType=VARCHAR}, #{productDesc,jdbcType=VARCHAR}, #{productDescEn,jdbcType=VARCHAR}, 
      #{status,jdbcType=VARCHAR}, #{classify,jdbcType=INTEGER}, #{classifyTwo,jdbcType=INTEGER}, 
      #{productLabel,jdbcType=VARCHAR}, #{productSetting,jdbcType=VARCHAR}, #{openRange,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{onlineTime,jdbcType=TIMESTAMP}, 
      #{productScore,jdbcType=DOUBLE}, #{userid,jdbcType=INTEGER}, #{username,jdbcType=VARCHAR}, 
      #{realname,jdbcType=VARCHAR}, #{orgid,jdbcType=INTEGER}, #{orgName,jdbcType=VARCHAR}, 
      #{isOneclick,jdbcType=INTEGER}, #{attemptType,jdbcType=INTEGER}, #{attemptTime,jdbcType=INTEGER}, 
      #{auditorType,jdbcType=INTEGER}, #{isNeedSpecification,jdbcType=INTEGER}, #{isGatewayCheck,jdbcType=INTEGER}, 
      #{protocolType,jdbcType=VARCHAR}, #{callNum30,jdbcType=BIGINT}, #{isNeedSouthControlPlatformAudit,jdbcType=INTEGER}, 
      #{southControlPlatformId,jdbcType=VARCHAR}, #{callNum6Month,jdbcType=BIGINT}, #{disorderTaskId,jdbcType=VARCHAR}, 
      #{isTestData,jdbcType=INTEGER}, #{needReadUseNotice,jdbcType=INTEGER}, #{readUseNoticeTime,jdbcType=INTEGER}, 
      #{isDataUpReport,jdbcType=INTEGER}, #{lastCallTime,jdbcType=TIMESTAMP}, #{isInalive,jdbcType=INTEGER}, 
      #{version,jdbcType=BIGINT}, #{docId,jdbcType=VARCHAR}, #{enDocId,jdbcType=VARCHAR}, 
      #{isHomeShow,jdbcType=INTEGER}, #{homeShowOrder,jdbcType=INTEGER}, #{outerId,jdbcType=VARCHAR}, 
      #{callNum,jdbcType=BIGINT}, #{whiteListConfig,jdbcType=INTEGER}, #{recommendIndex,jdbcType=INTEGER}, 
      #{isMock,jdbcType=INTEGER}, #{authProcesses,jdbcType=VARCHAR}, #{authMethods,jdbcType=VARCHAR}, 
      #{classifyNew,jdbcType=INTEGER}, #{operateIdList,jdbcType=VARCHAR}, #{productScoreSubItemJson,jdbcType=LONGVARCHAR}, 
      #{specification,jdbcType=LONGVARCHAR}, #{specificationParam,jdbcType=LONGVARCHAR}, 
      #{dbSpecificationParam,jdbcType=LONGVARCHAR}, #{orderSuccessNotifyUsers,jdbcType=LONGVARCHAR}, 
      #{selfDefineAuditors,jdbcType=LONGVARCHAR}, #{outerInfo,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.synqnc.opengw.dataservice.product.model.IProduct">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into i_product
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="opgwProductKey != null">
        opgw_product_key,
      </if>
      <if test="productId != null">
        product_id,
      </if>
      <if test="productName != null">
        product_name,
      </if>
      <if test="productNameEn != null">
        product_name_en,
      </if>
      <if test="productDesc != null">
        product_desc,
      </if>
      <if test="productDescEn != null">
        product_desc_en,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="classify != null">
        classify,
      </if>
      <if test="classifyTwo != null">
        classify_two,
      </if>
      <if test="productLabel != null">
        product_label,
      </if>
      <if test="productSetting != null">
        product_setting,
      </if>
      <if test="openRange != null">
        open_range,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="onlineTime != null">
        online_time,
      </if>
      <if test="productScore != null">
        product_score,
      </if>
      <if test="userid != null">
        userid,
      </if>
      <if test="username != null">
        username,
      </if>
      <if test="realname != null">
        realname,
      </if>
      <if test="orgid != null">
        orgid,
      </if>
      <if test="orgName != null">
        org_name,
      </if>
      <if test="isOneclick != null">
        is_oneclick,
      </if>
      <if test="attemptType != null">
        attempt_type,
      </if>
      <if test="attemptTime != null">
        attempt_time,
      </if>
      <if test="auditorType != null">
        auditor_type,
      </if>
      <if test="isNeedSpecification != null">
        is_need_specification,
      </if>
      <if test="isGatewayCheck != null">
        is_gateway_check,
      </if>
      <if test="protocolType != null">
        protocol_type,
      </if>
      <if test="callNum30 != null">
        call_num30,
      </if>
      <if test="isNeedSouthControlPlatformAudit != null">
        is_need_south_control_platform_audit,
      </if>
      <if test="southControlPlatformId != null">
        south_control_platform_id,
      </if>
      <if test="callNum6Month != null">
        call_num6_month,
      </if>
      <if test="disorderTaskId != null">
        disorder_task_id,
      </if>
      <if test="isTestData != null">
        is_test_data,
      </if>
      <if test="needReadUseNotice != null">
        need_read_use_notice,
      </if>
      <if test="readUseNoticeTime != null">
        read_use_notice_time,
      </if>
      <if test="isDataUpReport != null">
        is_data_up_report,
      </if>
      <if test="lastCallTime != null">
        last_call_time,
      </if>
      <if test="isInalive != null">
        is_inalive,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="docId != null">
        doc_id,
      </if>
      <if test="enDocId != null">
        en_doc_id,
      </if>
      <if test="isHomeShow != null">
        is_home_show,
      </if>
      <if test="homeShowOrder != null">
        home_show_order,
      </if>
      <if test="outerId != null">
        outer_id,
      </if>
      <if test="callNum != null">
        call_num,
      </if>
      <if test="whiteListConfig != null">
        white_list_config,
      </if>
      <if test="recommendIndex != null">
        recommend_index,
      </if>
      <if test="isMock != null">
        is_mock,
      </if>
      <if test="authProcesses != null">
        auth_processes,
      </if>
      <if test="authMethods != null">
        auth_methods,
      </if>
      <if test="classifyNew != null">
        classify_new,
      </if>
      <if test="operateIdList != null">
        operate_id_list,
      </if>
      <if test="productScoreSubItemJson != null">
        product_score_sub_item_json,
      </if>
      <if test="specification != null">
        specification,
      </if>
      <if test="specificationParam != null">
        specification_param,
      </if>
      <if test="dbSpecificationParam != null">
        db_specification_param,
      </if>
      <if test="orderSuccessNotifyUsers != null">
        order_success_notify_users,
      </if>
      <if test="selfDefineAuditors != null">
        self_define_auditors,
      </if>
      <if test="outerInfo != null">
        outer_info,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="opgwProductKey != null">
        #{opgwProductKey,jdbcType=VARCHAR},
      </if>
      <if test="productId != null">
        #{productId,jdbcType=VARCHAR},
      </if>
      <if test="productName != null">
        #{productName,jdbcType=VARCHAR},
      </if>
      <if test="productNameEn != null">
        #{productNameEn,jdbcType=VARCHAR},
      </if>
      <if test="productDesc != null">
        #{productDesc,jdbcType=VARCHAR},
      </if>
      <if test="productDescEn != null">
        #{productDescEn,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="classify != null">
        #{classify,jdbcType=INTEGER},
      </if>
      <if test="classifyTwo != null">
        #{classifyTwo,jdbcType=INTEGER},
      </if>
      <if test="productLabel != null">
        #{productLabel,jdbcType=VARCHAR},
      </if>
      <if test="productSetting != null">
        #{productSetting,jdbcType=VARCHAR},
      </if>
      <if test="openRange != null">
        #{openRange,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="onlineTime != null">
        #{onlineTime,jdbcType=TIMESTAMP},
      </if>
      <if test="productScore != null">
        #{productScore,jdbcType=DOUBLE},
      </if>
      <if test="userid != null">
        #{userid,jdbcType=INTEGER},
      </if>
      <if test="username != null">
        #{username,jdbcType=VARCHAR},
      </if>
      <if test="realname != null">
        #{realname,jdbcType=VARCHAR},
      </if>
      <if test="orgid != null">
        #{orgid,jdbcType=INTEGER},
      </if>
      <if test="orgName != null">
        #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="isOneclick != null">
        #{isOneclick,jdbcType=INTEGER},
      </if>
      <if test="attemptType != null">
        #{attemptType,jdbcType=INTEGER},
      </if>
      <if test="attemptTime != null">
        #{attemptTime,jdbcType=INTEGER},
      </if>
      <if test="auditorType != null">
        #{auditorType,jdbcType=INTEGER},
      </if>
      <if test="isNeedSpecification != null">
        #{isNeedSpecification,jdbcType=INTEGER},
      </if>
      <if test="isGatewayCheck != null">
        #{isGatewayCheck,jdbcType=INTEGER},
      </if>
      <if test="protocolType != null">
        #{protocolType,jdbcType=VARCHAR},
      </if>
      <if test="callNum30 != null">
        #{callNum30,jdbcType=BIGINT},
      </if>
      <if test="isNeedSouthControlPlatformAudit != null">
        #{isNeedSouthControlPlatformAudit,jdbcType=INTEGER},
      </if>
      <if test="southControlPlatformId != null">
        #{southControlPlatformId,jdbcType=VARCHAR},
      </if>
      <if test="callNum6Month != null">
        #{callNum6Month,jdbcType=BIGINT},
      </if>
      <if test="disorderTaskId != null">
        #{disorderTaskId,jdbcType=VARCHAR},
      </if>
      <if test="isTestData != null">
        #{isTestData,jdbcType=INTEGER},
      </if>
      <if test="needReadUseNotice != null">
        #{needReadUseNotice,jdbcType=INTEGER},
      </if>
      <if test="readUseNoticeTime != null">
        #{readUseNoticeTime,jdbcType=INTEGER},
      </if>
      <if test="isDataUpReport != null">
        #{isDataUpReport,jdbcType=INTEGER},
      </if>
      <if test="lastCallTime != null">
        #{lastCallTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isInalive != null">
        #{isInalive,jdbcType=INTEGER},
      </if>
      <if test="version != null">
        #{version,jdbcType=BIGINT},
      </if>
      <if test="docId != null">
        #{docId,jdbcType=VARCHAR},
      </if>
      <if test="enDocId != null">
        #{enDocId,jdbcType=VARCHAR},
      </if>
      <if test="isHomeShow != null">
        #{isHomeShow,jdbcType=INTEGER},
      </if>
      <if test="homeShowOrder != null">
        #{homeShowOrder,jdbcType=INTEGER},
      </if>
      <if test="outerId != null">
        #{outerId,jdbcType=VARCHAR},
      </if>
      <if test="callNum != null">
        #{callNum,jdbcType=BIGINT},
      </if>
      <if test="whiteListConfig != null">
        #{whiteListConfig,jdbcType=INTEGER},
      </if>
      <if test="recommendIndex != null">
        #{recommendIndex,jdbcType=INTEGER},
      </if>
      <if test="isMock != null">
        #{isMock,jdbcType=INTEGER},
      </if>
      <if test="authProcesses != null">
        #{authProcesses,jdbcType=VARCHAR},
      </if>
      <if test="authMethods != null">
        #{authMethods,jdbcType=VARCHAR},
      </if>
      <if test="classifyNew != null">
        #{classifyNew,jdbcType=INTEGER},
      </if>
      <if test="operateIdList != null">
        #{operateIdList,jdbcType=VARCHAR},
      </if>
      <if test="productScoreSubItemJson != null">
        #{productScoreSubItemJson,jdbcType=LONGVARCHAR},
      </if>
      <if test="specification != null">
        #{specification,jdbcType=LONGVARCHAR},
      </if>
      <if test="specificationParam != null">
        #{specificationParam,jdbcType=LONGVARCHAR},
      </if>
      <if test="dbSpecificationParam != null">
        #{dbSpecificationParam,jdbcType=LONGVARCHAR},
      </if>
      <if test="orderSuccessNotifyUsers != null">
        #{orderSuccessNotifyUsers,jdbcType=LONGVARCHAR},
      </if>
      <if test="selfDefineAuditors != null">
        #{selfDefineAuditors,jdbcType=LONGVARCHAR},
      </if>
      <if test="outerInfo != null">
        #{outerInfo,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.synqnc.opengw.dataservice.product.model.IProductExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from i_product
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update i_product
    <set>
      <if test="record.opgwProductKey != null">
        opgw_product_key = #{record.opgwProductKey,jdbcType=VARCHAR},
      </if>
      <if test="record.productId != null">
        product_id = #{record.productId,jdbcType=VARCHAR},
      </if>
      <if test="record.productName != null">
        product_name = #{record.productName,jdbcType=VARCHAR},
      </if>
      <if test="record.productNameEn != null">
        product_name_en = #{record.productNameEn,jdbcType=VARCHAR},
      </if>
      <if test="record.productDesc != null">
        product_desc = #{record.productDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.productDescEn != null">
        product_desc_en = #{record.productDescEn,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.classify != null">
        classify = #{record.classify,jdbcType=INTEGER},
      </if>
      <if test="record.classifyTwo != null">
        classify_two = #{record.classifyTwo,jdbcType=INTEGER},
      </if>
      <if test="record.productLabel != null">
        product_label = #{record.productLabel,jdbcType=VARCHAR},
      </if>
      <if test="record.productSetting != null">
        product_setting = #{record.productSetting,jdbcType=VARCHAR},
      </if>
      <if test="record.openRange != null">
        open_range = #{record.openRange,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.onlineTime != null">
        online_time = #{record.onlineTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.productScore != null">
        product_score = #{record.productScore,jdbcType=DOUBLE},
      </if>
      <if test="record.userid != null">
        userid = #{record.userid,jdbcType=INTEGER},
      </if>
      <if test="record.username != null">
        username = #{record.username,jdbcType=VARCHAR},
      </if>
      <if test="record.realname != null">
        realname = #{record.realname,jdbcType=VARCHAR},
      </if>
      <if test="record.orgid != null">
        orgid = #{record.orgid,jdbcType=INTEGER},
      </if>
      <if test="record.orgName != null">
        org_name = #{record.orgName,jdbcType=VARCHAR},
      </if>
      <if test="record.isOneclick != null">
        is_oneclick = #{record.isOneclick,jdbcType=INTEGER},
      </if>
      <if test="record.attemptType != null">
        attempt_type = #{record.attemptType,jdbcType=INTEGER},
      </if>
      <if test="record.attemptTime != null">
        attempt_time = #{record.attemptTime,jdbcType=INTEGER},
      </if>
      <if test="record.auditorType != null">
        auditor_type = #{record.auditorType,jdbcType=INTEGER},
      </if>
      <if test="record.isNeedSpecification != null">
        is_need_specification = #{record.isNeedSpecification,jdbcType=INTEGER},
      </if>
      <if test="record.isGatewayCheck != null">
        is_gateway_check = #{record.isGatewayCheck,jdbcType=INTEGER},
      </if>
      <if test="record.protocolType != null">
        protocol_type = #{record.protocolType,jdbcType=VARCHAR},
      </if>
      <if test="record.callNum30 != null">
        call_num30 = #{record.callNum30,jdbcType=BIGINT},
      </if>
      <if test="record.isNeedSouthControlPlatformAudit != null">
        is_need_south_control_platform_audit = #{record.isNeedSouthControlPlatformAudit,jdbcType=INTEGER},
      </if>
      <if test="record.southControlPlatformId != null">
        south_control_platform_id = #{record.southControlPlatformId,jdbcType=VARCHAR},
      </if>
      <if test="record.callNum6Month != null">
        call_num6_month = #{record.callNum6Month,jdbcType=BIGINT},
      </if>
      <if test="record.disorderTaskId != null">
        disorder_task_id = #{record.disorderTaskId,jdbcType=VARCHAR},
      </if>
      <if test="record.isTestData != null">
        is_test_data = #{record.isTestData,jdbcType=INTEGER},
      </if>
      <if test="record.needReadUseNotice != null">
        need_read_use_notice = #{record.needReadUseNotice,jdbcType=INTEGER},
      </if>
      <if test="record.readUseNoticeTime != null">
        read_use_notice_time = #{record.readUseNoticeTime,jdbcType=INTEGER},
      </if>
      <if test="record.isDataUpReport != null">
        is_data_up_report = #{record.isDataUpReport,jdbcType=INTEGER},
      </if>
      <if test="record.lastCallTime != null">
        last_call_time = #{record.lastCallTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isInalive != null">
        is_inalive = #{record.isInalive,jdbcType=INTEGER},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=BIGINT},
      </if>
      <if test="record.docId != null">
        doc_id = #{record.docId,jdbcType=VARCHAR},
      </if>
      <if test="record.enDocId != null">
        en_doc_id = #{record.enDocId,jdbcType=VARCHAR},
      </if>
      <if test="record.isHomeShow != null">
        is_home_show = #{record.isHomeShow,jdbcType=INTEGER},
      </if>
      <if test="record.homeShowOrder != null">
        home_show_order = #{record.homeShowOrder,jdbcType=INTEGER},
      </if>
      <if test="record.outerId != null">
        outer_id = #{record.outerId,jdbcType=VARCHAR},
      </if>
      <if test="record.callNum != null">
        call_num = #{record.callNum,jdbcType=BIGINT},
      </if>
      <if test="record.whiteListConfig != null">
        white_list_config = #{record.whiteListConfig,jdbcType=INTEGER},
      </if>
      <if test="record.recommendIndex != null">
        recommend_index = #{record.recommendIndex,jdbcType=INTEGER},
      </if>
      <if test="record.isMock != null">
        is_mock = #{record.isMock,jdbcType=INTEGER},
      </if>
      <if test="record.authProcesses != null">
        auth_processes = #{record.authProcesses,jdbcType=VARCHAR},
      </if>
      <if test="record.authMethods != null">
        auth_methods = #{record.authMethods,jdbcType=VARCHAR},
      </if>
      <if test="record.classifyNew != null">
        classify_new = #{record.classifyNew,jdbcType=INTEGER},
      </if>
      <if test="record.operateIdList != null">
        operate_id_list = #{record.operateIdList,jdbcType=VARCHAR},
      </if>
      <if test="record.productScoreSubItemJson != null">
        product_score_sub_item_json = #{record.productScoreSubItemJson,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.specification != null">
        specification = #{record.specification,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.specificationParam != null">
        specification_param = #{record.specificationParam,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.dbSpecificationParam != null">
        db_specification_param = #{record.dbSpecificationParam,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.orderSuccessNotifyUsers != null">
        order_success_notify_users = #{record.orderSuccessNotifyUsers,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.selfDefineAuditors != null">
        self_define_auditors = #{record.selfDefineAuditors,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.outerInfo != null">
        outer_info = #{record.outerInfo,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update i_product
    set opgw_product_key = #{record.opgwProductKey,jdbcType=VARCHAR},
      product_id = #{record.productId,jdbcType=VARCHAR},
      product_name = #{record.productName,jdbcType=VARCHAR},
      product_name_en = #{record.productNameEn,jdbcType=VARCHAR},
      product_desc = #{record.productDesc,jdbcType=VARCHAR},
      product_desc_en = #{record.productDescEn,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=VARCHAR},
      classify = #{record.classify,jdbcType=INTEGER},
      classify_two = #{record.classifyTwo,jdbcType=INTEGER},
      product_label = #{record.productLabel,jdbcType=VARCHAR},
      product_setting = #{record.productSetting,jdbcType=VARCHAR},
      open_range = #{record.openRange,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      online_time = #{record.onlineTime,jdbcType=TIMESTAMP},
      product_score = #{record.productScore,jdbcType=DOUBLE},
      userid = #{record.userid,jdbcType=INTEGER},
      username = #{record.username,jdbcType=VARCHAR},
      realname = #{record.realname,jdbcType=VARCHAR},
      orgid = #{record.orgid,jdbcType=INTEGER},
      org_name = #{record.orgName,jdbcType=VARCHAR},
      is_oneclick = #{record.isOneclick,jdbcType=INTEGER},
      attempt_type = #{record.attemptType,jdbcType=INTEGER},
      attempt_time = #{record.attemptTime,jdbcType=INTEGER},
      auditor_type = #{record.auditorType,jdbcType=INTEGER},
      is_need_specification = #{record.isNeedSpecification,jdbcType=INTEGER},
      is_gateway_check = #{record.isGatewayCheck,jdbcType=INTEGER},
      protocol_type = #{record.protocolType,jdbcType=VARCHAR},
      call_num30 = #{record.callNum30,jdbcType=BIGINT},
      is_need_south_control_platform_audit = #{record.isNeedSouthControlPlatformAudit,jdbcType=INTEGER},
      south_control_platform_id = #{record.southControlPlatformId,jdbcType=VARCHAR},
      call_num6_month = #{record.callNum6Month,jdbcType=BIGINT},
      disorder_task_id = #{record.disorderTaskId,jdbcType=VARCHAR},
      is_test_data = #{record.isTestData,jdbcType=INTEGER},
      need_read_use_notice = #{record.needReadUseNotice,jdbcType=INTEGER},
      read_use_notice_time = #{record.readUseNoticeTime,jdbcType=INTEGER},
      is_data_up_report = #{record.isDataUpReport,jdbcType=INTEGER},
      last_call_time = #{record.lastCallTime,jdbcType=TIMESTAMP},
      is_inalive = #{record.isInalive,jdbcType=INTEGER},
      version = #{record.version,jdbcType=BIGINT},
      doc_id = #{record.docId,jdbcType=VARCHAR},
      en_doc_id = #{record.enDocId,jdbcType=VARCHAR},
      is_home_show = #{record.isHomeShow,jdbcType=INTEGER},
      home_show_order = #{record.homeShowOrder,jdbcType=INTEGER},
      outer_id = #{record.outerId,jdbcType=VARCHAR},
      call_num = #{record.callNum,jdbcType=BIGINT},
      white_list_config = #{record.whiteListConfig,jdbcType=INTEGER},
      recommend_index = #{record.recommendIndex,jdbcType=INTEGER},
      is_mock = #{record.isMock,jdbcType=INTEGER},
      auth_processes = #{record.authProcesses,jdbcType=VARCHAR},
      auth_methods = #{record.authMethods,jdbcType=VARCHAR},
      classify_new = #{record.classifyNew,jdbcType=INTEGER},
      operate_id_list = #{record.operateIdList,jdbcType=VARCHAR},
      product_score_sub_item_json = #{record.productScoreSubItemJson,jdbcType=LONGVARCHAR},
      specification = #{record.specification,jdbcType=LONGVARCHAR},
      specification_param = #{record.specificationParam,jdbcType=LONGVARCHAR},
      db_specification_param = #{record.dbSpecificationParam,jdbcType=LONGVARCHAR},
      order_success_notify_users = #{record.orderSuccessNotifyUsers,jdbcType=LONGVARCHAR},
      self_define_auditors = #{record.selfDefineAuditors,jdbcType=LONGVARCHAR},
      outer_info = #{record.outerInfo,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update i_product
    set opgw_product_key = #{record.opgwProductKey,jdbcType=VARCHAR},
      product_id = #{record.productId,jdbcType=VARCHAR},
      product_name = #{record.productName,jdbcType=VARCHAR},
      product_name_en = #{record.productNameEn,jdbcType=VARCHAR},
      product_desc = #{record.productDesc,jdbcType=VARCHAR},
      product_desc_en = #{record.productDescEn,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=VARCHAR},
      classify = #{record.classify,jdbcType=INTEGER},
      classify_two = #{record.classifyTwo,jdbcType=INTEGER},
      product_label = #{record.productLabel,jdbcType=VARCHAR},
      product_setting = #{record.productSetting,jdbcType=VARCHAR},
      open_range = #{record.openRange,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      online_time = #{record.onlineTime,jdbcType=TIMESTAMP},
      product_score = #{record.productScore,jdbcType=DOUBLE},
      userid = #{record.userid,jdbcType=INTEGER},
      username = #{record.username,jdbcType=VARCHAR},
      realname = #{record.realname,jdbcType=VARCHAR},
      orgid = #{record.orgid,jdbcType=INTEGER},
      org_name = #{record.orgName,jdbcType=VARCHAR},
      is_oneclick = #{record.isOneclick,jdbcType=INTEGER},
      attempt_type = #{record.attemptType,jdbcType=INTEGER},
      attempt_time = #{record.attemptTime,jdbcType=INTEGER},
      auditor_type = #{record.auditorType,jdbcType=INTEGER},
      is_need_specification = #{record.isNeedSpecification,jdbcType=INTEGER},
      is_gateway_check = #{record.isGatewayCheck,jdbcType=INTEGER},
      protocol_type = #{record.protocolType,jdbcType=VARCHAR},
      call_num30 = #{record.callNum30,jdbcType=BIGINT},
      is_need_south_control_platform_audit = #{record.isNeedSouthControlPlatformAudit,jdbcType=INTEGER},
      south_control_platform_id = #{record.southControlPlatformId,jdbcType=VARCHAR},
      call_num6_month = #{record.callNum6Month,jdbcType=BIGINT},
      disorder_task_id = #{record.disorderTaskId,jdbcType=VARCHAR},
      is_test_data = #{record.isTestData,jdbcType=INTEGER},
      need_read_use_notice = #{record.needReadUseNotice,jdbcType=INTEGER},
      read_use_notice_time = #{record.readUseNoticeTime,jdbcType=INTEGER},
      is_data_up_report = #{record.isDataUpReport,jdbcType=INTEGER},
      last_call_time = #{record.lastCallTime,jdbcType=TIMESTAMP},
      is_inalive = #{record.isInalive,jdbcType=INTEGER},
      version = #{record.version,jdbcType=BIGINT},
      doc_id = #{record.docId,jdbcType=VARCHAR},
      en_doc_id = #{record.enDocId,jdbcType=VARCHAR},
      is_home_show = #{record.isHomeShow,jdbcType=INTEGER},
      home_show_order = #{record.homeShowOrder,jdbcType=INTEGER},
      outer_id = #{record.outerId,jdbcType=VARCHAR},
      call_num = #{record.callNum,jdbcType=BIGINT},
      white_list_config = #{record.whiteListConfig,jdbcType=INTEGER},
      recommend_index = #{record.recommendIndex,jdbcType=INTEGER},
      is_mock = #{record.isMock,jdbcType=INTEGER},
      auth_processes = #{record.authProcesses,jdbcType=VARCHAR},
      auth_methods = #{record.authMethods,jdbcType=VARCHAR},
      classify_new = #{record.classifyNew,jdbcType=INTEGER},
      operate_id_list = #{record.operateIdList,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.synqnc.opengw.dataservice.product.model.IProduct">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update i_product
    <set>
      <if test="productId != null">
        product_id = #{productId,jdbcType=VARCHAR},
      </if>
      <if test="productName != null">
        product_name = #{productName,jdbcType=VARCHAR},
      </if>
      <if test="productNameEn != null">
        product_name_en = #{productNameEn,jdbcType=VARCHAR},
      </if>
      <if test="productDesc != null">
        product_desc = #{productDesc,jdbcType=VARCHAR},
      </if>
      <if test="productDescEn != null">
        product_desc_en = #{productDescEn,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="classify != null">
        classify = #{classify,jdbcType=INTEGER},
      </if>
      <if test="classifyTwo != null">
        classify_two = #{classifyTwo,jdbcType=INTEGER},
      </if>
      <if test="productLabel != null">
        product_label = #{productLabel,jdbcType=VARCHAR},
      </if>
      <if test="productSetting != null">
        product_setting = #{productSetting,jdbcType=VARCHAR},
      </if>
      <if test="openRange != null">
        open_range = #{openRange,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="onlineTime != null">
        online_time = #{onlineTime,jdbcType=TIMESTAMP},
      </if>
      <if test="productScore != null">
        product_score = #{productScore,jdbcType=DOUBLE},
      </if>
      <if test="userid != null">
        userid = #{userid,jdbcType=INTEGER},
      </if>
      <if test="username != null">
        username = #{username,jdbcType=VARCHAR},
      </if>
      <if test="realname != null">
        realname = #{realname,jdbcType=VARCHAR},
      </if>
      <if test="orgid != null">
        orgid = #{orgid,jdbcType=INTEGER},
      </if>
      <if test="orgName != null">
        org_name = #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="isOneclick != null">
        is_oneclick = #{isOneclick,jdbcType=INTEGER},
      </if>
      <if test="attemptType != null">
        attempt_type = #{attemptType,jdbcType=INTEGER},
      </if>
      <if test="attemptTime != null">
        attempt_time = #{attemptTime,jdbcType=INTEGER},
      </if>
      <if test="auditorType != null">
        auditor_type = #{auditorType,jdbcType=INTEGER},
      </if>
      <if test="isNeedSpecification != null">
        is_need_specification = #{isNeedSpecification,jdbcType=INTEGER},
      </if>
      <if test="isGatewayCheck != null">
        is_gateway_check = #{isGatewayCheck,jdbcType=INTEGER},
      </if>
      <if test="protocolType != null">
        protocol_type = #{protocolType,jdbcType=VARCHAR},
      </if>
      <if test="callNum30 != null">
        call_num30 = #{callNum30,jdbcType=BIGINT},
      </if>
      <if test="isNeedSouthControlPlatformAudit != null">
        is_need_south_control_platform_audit = #{isNeedSouthControlPlatformAudit,jdbcType=INTEGER},
      </if>
      <if test="southControlPlatformId != null">
        south_control_platform_id = #{southControlPlatformId,jdbcType=VARCHAR},
      </if>
      <if test="callNum6Month != null">
        call_num6_month = #{callNum6Month,jdbcType=BIGINT},
      </if>
      <if test="disorderTaskId != null">
        disorder_task_id = #{disorderTaskId,jdbcType=VARCHAR},
      </if>
      <if test="isTestData != null">
        is_test_data = #{isTestData,jdbcType=INTEGER},
      </if>
      <if test="needReadUseNotice != null">
        need_read_use_notice = #{needReadUseNotice,jdbcType=INTEGER},
      </if>
      <if test="readUseNoticeTime != null">
        read_use_notice_time = #{readUseNoticeTime,jdbcType=INTEGER},
      </if>
      <if test="isDataUpReport != null">
        is_data_up_report = #{isDataUpReport,jdbcType=INTEGER},
      </if>
      <if test="lastCallTime != null">
        last_call_time = #{lastCallTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isInalive != null">
        is_inalive = #{isInalive,jdbcType=INTEGER},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=BIGINT},
      </if>
      <if test="docId != null">
        doc_id = #{docId,jdbcType=VARCHAR},
      </if>
      <if test="enDocId != null">
        en_doc_id = #{enDocId,jdbcType=VARCHAR},
      </if>
      <if test="isHomeShow != null">
        is_home_show = #{isHomeShow,jdbcType=INTEGER},
      </if>
      <if test="homeShowOrder != null">
        home_show_order = #{homeShowOrder,jdbcType=INTEGER},
      </if>
      <if test="outerId != null">
        outer_id = #{outerId,jdbcType=VARCHAR},
      </if>
      <if test="callNum != null">
        call_num = #{callNum,jdbcType=BIGINT},
      </if>
      <if test="whiteListConfig != null">
        white_list_config = #{whiteListConfig,jdbcType=INTEGER},
      </if>
      <if test="recommendIndex != null">
        recommend_index = #{recommendIndex,jdbcType=INTEGER},
      </if>
      <if test="isMock != null">
        is_mock = #{isMock,jdbcType=INTEGER},
      </if>
      <if test="authProcesses != null">
        auth_processes = #{authProcesses,jdbcType=VARCHAR},
      </if>
      <if test="authMethods != null">
        auth_methods = #{authMethods,jdbcType=VARCHAR},
      </if>
      <if test="classifyNew != null">
        classify_new = #{classifyNew,jdbcType=INTEGER},
      </if>
      <if test="operateIdList != null">
        operate_id_list = #{operateIdList,jdbcType=VARCHAR},
      </if>
      <if test="productScoreSubItemJson != null">
        product_score_sub_item_json = #{productScoreSubItemJson,jdbcType=LONGVARCHAR},
      </if>
      <if test="specification != null">
        specification = #{specification,jdbcType=LONGVARCHAR},
      </if>
      <if test="specificationParam != null">
        specification_param = #{specificationParam,jdbcType=LONGVARCHAR},
      </if>
      <if test="dbSpecificationParam != null">
        db_specification_param = #{dbSpecificationParam,jdbcType=LONGVARCHAR},
      </if>
      <if test="orderSuccessNotifyUsers != null">
        order_success_notify_users = #{orderSuccessNotifyUsers,jdbcType=LONGVARCHAR},
      </if>
      <if test="selfDefineAuditors != null">
        self_define_auditors = #{selfDefineAuditors,jdbcType=LONGVARCHAR},
      </if>
      <if test="outerInfo != null">
        outer_info = #{outerInfo,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where opgw_product_key = #{opgwProductKey,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.synqnc.opengw.dataservice.product.model.IProduct">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update i_product
    set product_id = #{productId,jdbcType=VARCHAR},
      product_name = #{productName,jdbcType=VARCHAR},
      product_name_en = #{productNameEn,jdbcType=VARCHAR},
      product_desc = #{productDesc,jdbcType=VARCHAR},
      product_desc_en = #{productDescEn,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR},
      classify = #{classify,jdbcType=INTEGER},
      classify_two = #{classifyTwo,jdbcType=INTEGER},
      product_label = #{productLabel,jdbcType=VARCHAR},
      product_setting = #{productSetting,jdbcType=VARCHAR},
      open_range = #{openRange,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      online_time = #{onlineTime,jdbcType=TIMESTAMP},
      product_score = #{productScore,jdbcType=DOUBLE},
      userid = #{userid,jdbcType=INTEGER},
      username = #{username,jdbcType=VARCHAR},
      realname = #{realname,jdbcType=VARCHAR},
      orgid = #{orgid,jdbcType=INTEGER},
      org_name = #{orgName,jdbcType=VARCHAR},
      is_oneclick = #{isOneclick,jdbcType=INTEGER},
      attempt_type = #{attemptType,jdbcType=INTEGER},
      attempt_time = #{attemptTime,jdbcType=INTEGER},
      auditor_type = #{auditorType,jdbcType=INTEGER},
      is_need_specification = #{isNeedSpecification,jdbcType=INTEGER},
      is_gateway_check = #{isGatewayCheck,jdbcType=INTEGER},
      protocol_type = #{protocolType,jdbcType=VARCHAR},
      call_num30 = #{callNum30,jdbcType=BIGINT},
      is_need_south_control_platform_audit = #{isNeedSouthControlPlatformAudit,jdbcType=INTEGER},
      south_control_platform_id = #{southControlPlatformId,jdbcType=VARCHAR},
      call_num6_month = #{callNum6Month,jdbcType=BIGINT},
      disorder_task_id = #{disorderTaskId,jdbcType=VARCHAR},
      is_test_data = #{isTestData,jdbcType=INTEGER},
      need_read_use_notice = #{needReadUseNotice,jdbcType=INTEGER},
      read_use_notice_time = #{readUseNoticeTime,jdbcType=INTEGER},
      is_data_up_report = #{isDataUpReport,jdbcType=INTEGER},
      last_call_time = #{lastCallTime,jdbcType=TIMESTAMP},
      is_inalive = #{isInalive,jdbcType=INTEGER},
      version = #{version,jdbcType=BIGINT},
      doc_id = #{docId,jdbcType=VARCHAR},
      en_doc_id = #{enDocId,jdbcType=VARCHAR},
      is_home_show = #{isHomeShow,jdbcType=INTEGER},
      home_show_order = #{homeShowOrder,jdbcType=INTEGER},
      outer_id = #{outerId,jdbcType=VARCHAR},
      call_num = #{callNum,jdbcType=BIGINT},
      white_list_config = #{whiteListConfig,jdbcType=INTEGER},
      recommend_index = #{recommendIndex,jdbcType=INTEGER},
      is_mock = #{isMock,jdbcType=INTEGER},
      auth_processes = #{authProcesses,jdbcType=VARCHAR},
      auth_methods = #{authMethods,jdbcType=VARCHAR},
      classify_new = #{classifyNew,jdbcType=INTEGER},
      operate_id_list = #{operateIdList,jdbcType=VARCHAR},
      product_score_sub_item_json = #{productScoreSubItemJson,jdbcType=LONGVARCHAR},
      specification = #{specification,jdbcType=LONGVARCHAR},
      specification_param = #{specificationParam,jdbcType=LONGVARCHAR},
      db_specification_param = #{dbSpecificationParam,jdbcType=LONGVARCHAR},
      order_success_notify_users = #{orderSuccessNotifyUsers,jdbcType=LONGVARCHAR},
      self_define_auditors = #{selfDefineAuditors,jdbcType=LONGVARCHAR},
      outer_info = #{outerInfo,jdbcType=LONGVARCHAR}
    where opgw_product_key = #{opgwProductKey,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.synqnc.opengw.dataservice.product.model.IProduct">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update i_product
    set product_id = #{productId,jdbcType=VARCHAR},
      product_name = #{productName,jdbcType=VARCHAR},
      product_name_en = #{productNameEn,jdbcType=VARCHAR},
      product_desc = #{productDesc,jdbcType=VARCHAR},
      product_desc_en = #{productDescEn,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR},
      classify = #{classify,jdbcType=INTEGER},
      classify_two = #{classifyTwo,jdbcType=INTEGER},
      product_label = #{productLabel,jdbcType=VARCHAR},
      product_setting = #{productSetting,jdbcType=VARCHAR},
      open_range = #{openRange,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      online_time = #{onlineTime,jdbcType=TIMESTAMP},
      product_score = #{productScore,jdbcType=DOUBLE},
      userid = #{userid,jdbcType=INTEGER},
      username = #{username,jdbcType=VARCHAR},
      realname = #{realname,jdbcType=VARCHAR},
      orgid = #{orgid,jdbcType=INTEGER},
      org_name = #{orgName,jdbcType=VARCHAR},
      is_oneclick = #{isOneclick,jdbcType=INTEGER},
      attempt_type = #{attemptType,jdbcType=INTEGER},
      attempt_time = #{attemptTime,jdbcType=INTEGER},
      auditor_type = #{auditorType,jdbcType=INTEGER},
      is_need_specification = #{isNeedSpecification,jdbcType=INTEGER},
      is_gateway_check = #{isGatewayCheck,jdbcType=INTEGER},
      protocol_type = #{protocolType,jdbcType=VARCHAR},
      call_num30 = #{callNum30,jdbcType=BIGINT},
      is_need_south_control_platform_audit = #{isNeedSouthControlPlatformAudit,jdbcType=INTEGER},
      south_control_platform_id = #{southControlPlatformId,jdbcType=VARCHAR},
      call_num6_month = #{callNum6Month,jdbcType=BIGINT},
      disorder_task_id = #{disorderTaskId,jdbcType=VARCHAR},
      is_test_data = #{isTestData,jdbcType=INTEGER},
      need_read_use_notice = #{needReadUseNotice,jdbcType=INTEGER},
      read_use_notice_time = #{readUseNoticeTime,jdbcType=INTEGER},
      is_data_up_report = #{isDataUpReport,jdbcType=INTEGER},
      last_call_time = #{lastCallTime,jdbcType=TIMESTAMP},
      is_inalive = #{isInalive,jdbcType=INTEGER},
      version = #{version,jdbcType=BIGINT},
      doc_id = #{docId,jdbcType=VARCHAR},
      en_doc_id = #{enDocId,jdbcType=VARCHAR},
      is_home_show = #{isHomeShow,jdbcType=INTEGER},
      home_show_order = #{homeShowOrder,jdbcType=INTEGER},
      outer_id = #{outerId,jdbcType=VARCHAR},
      call_num = #{callNum,jdbcType=BIGINT},
      white_list_config = #{whiteListConfig,jdbcType=INTEGER},
      recommend_index = #{recommendIndex,jdbcType=INTEGER},
      is_mock = #{isMock,jdbcType=INTEGER},
      auth_processes = #{authProcesses,jdbcType=VARCHAR},
      auth_methods = #{authMethods,jdbcType=VARCHAR},
      classify_new = #{classifyNew,jdbcType=INTEGER},
      operate_id_list = #{operateIdList,jdbcType=VARCHAR}
    where opgw_product_key = #{opgwProductKey,jdbcType=VARCHAR}
  </update>

  <!-- 自定义查询：能力接口版本下拉数据 -->
  <resultMap id="CapabilityVersionDropdownResultMap" type="com.synqnc.opengw.dataservice.capability.modelvo.CapabilityVersionDropdownVO">
    <result column="product_name" property="productName"/>
    <result column="product_key" property="productKey"/>
    <result column="request_example" property="requestExample"/>
    <result column="response_example" property="responseExample"/>
    <result column="product_desc" property="productDesc"/>
    <result column="product_type" property="productType"/>
    <result column="product_status" property="productStatus"/>
  </resultMap>

  <select id="selectCapabilityVersionDropdownData" resultMap="CapabilityVersionDropdownResultMap">
    SELECT DISTINCT
      p.product_name,
      p.opgw_product_key as product_key,
      '' as request_example,
      '' as response_example,
      p.product_desc,
      '能力' as product_type,
      p.status as product_status
    FROM i_product p
    WHERE EXISTS (
      SELECT 1 FROM i_product_capability pc
      INNER JOIN i_capability c ON pc.opgw_capability_key = c.opgw_capability_key
      INNER JOIN i_version v ON c.opgw_capability_key = v.opgw_capability_key
      WHERE pc.opgw_product_key = p.opgw_product_key
      AND c.status != 'deleted'
      AND v.status != 'deleted'
    )
    <if test="capabilityName != null and capabilityName != ''">
      AND p.product_name LIKE CONCAT('%', #{capabilityName}, '%')
    </if>
    AND p.status != 'deleted'
    ORDER BY p.product_name
    <if test="offset != null and pageSize != null">
      LIMIT #{offset}, #{pageSize}
    </if>
  </select>

  <select id="countCapabilityVersionDropdownData" resultType="long">
    SELECT COUNT(DISTINCT p.opgw_product_key)
    FROM i_product p
    WHERE EXISTS (
      SELECT 1 FROM i_product_capability pc
      INNER JOIN i_capability c ON pc.opgw_capability_key = c.opgw_capability_key
      INNER JOIN i_version v ON c.opgw_capability_key = v.opgw_capability_key
      WHERE pc.opgw_product_key = p.opgw_product_key
      AND c.status != 'deleted'
      AND v.status != 'deleted'
    )
    <if test="capabilityName != null and capabilityName != ''">
      AND p.product_name LIKE CONCAT('%', #{capabilityName}, '%')
    </if>
    AND p.status != 'deleted'
  </select>
</mapper>