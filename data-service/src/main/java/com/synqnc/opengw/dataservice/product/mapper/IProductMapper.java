package com.synqnc.opengw.dataservice.product.mapper;

import com.synqnc.opengw.dataservice.product.model.IProduct;
import com.synqnc.opengw.dataservice.product.model.IProductExample;
import com.synqnc.opengw.dataservice.capability.modelvo.CapabilityVersionDropdownVO;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface IProductMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table i_product
     *
     * @mbg.generated
     */
    long countByExample(IProductExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table i_product
     *
     * @mbg.generated
     */
    int deleteByExample(IProductExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table i_product
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(String opgwProductKey);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table i_product
     *
     * @mbg.generated
     */
    int insert(IProduct record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table i_product
     *
     * @mbg.generated
     */
    int insertSelective(IProduct record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table i_product
     *
     * @mbg.generated
     */
    List<IProduct> selectByExampleWithBLOBs(IProductExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table i_product
     *
     * @mbg.generated
     */
    List<IProduct> selectByExample(IProductExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table i_product
     *
     * @mbg.generated
     */
    IProduct selectByPrimaryKey(String opgwProductKey);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table i_product
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") IProduct record, @Param("example") IProductExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table i_product
     *
     * @mbg.generated
     */
    int updateByExampleWithBLOBs(@Param("record") IProduct record, @Param("example") IProductExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table i_product
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") IProduct record, @Param("example") IProductExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table i_product
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(IProduct record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table i_product
     *
     * @mbg.generated
     */
    int updateByPrimaryKeyWithBLOBs(IProduct record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table i_product
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(IProduct record);

    /**
     * 查询能力接口版本下拉数据
     * @param capabilityName 能力名称(支持模糊查询)
     * @param offset 偏移量
     * @param pageSize 每页大小
     * @return 能力接口版本下拉数据列表
     */
    List<CapabilityVersionDropdownVO> selectCapabilityVersionDropdownData(
            @Param("capabilityName") String capabilityName,
            @Param("offset") Long offset,
            @Param("pageSize") Integer pageSize);

    /**
     * 统计能力接口版本下拉数据总数
     * @param capabilityName 能力名称(支持模糊查询)
     * @return 总数
     */
    long countCapabilityVersionDropdownData(@Param("capabilityName") String capabilityName);
}