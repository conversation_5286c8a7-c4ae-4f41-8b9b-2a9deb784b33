package com.synqnc.opengw.dataservice.capability.modelvo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: AI Assistant
 * @Date: 2025/1/1
 * @Description: 能力接口版本下拉数据VO
 * @Version: 1.0
 */
@Data
@ApiModel(value = "能力接口版本下拉数据VO", description = "能力接口版本下拉数据VO")
public class CapabilityVersionDropdownVO {

    @ApiModelProperty(value = "能力名称", example = "OTP验证")
    private String productName;

    @ApiModelProperty(value = "能力标识", example = "otp_verification")
    private String productKey;

    @ApiModelProperty(value = "请求示例", example = "POST /api/otp/send")
    private String requestExample;

    @ApiModelProperty(value = "响应示例", example = "{\"code\":\"200\",\"message\":\"success\"}")
    private String responseExample;

    @ApiModelProperty(value = "能力描述", example = "提供OTP验证服务")
    private String productDesc;

    @ApiModelProperty(value = "能力类型", example = "验证服务")
    private String productType;

    @ApiModelProperty(value = "能力状态", example = "已上线")
    private String productStatus;

    @ApiModelProperty(value = "接口列表")
    private List<InterfaceInfo> children;

    /**
     * 接口信息
     */
    @Data
    @ApiModel(value = "接口信息", description = "接口信息")
    public static class InterfaceInfo {

        @ApiModelProperty(value = "接口名称", example = "发送OTP")
        private String interfaceName;

        @ApiModelProperty(value = "接口标识", example = "send_otp")
        private String interfaceKey;

        @ApiModelProperty(value = "接口类型", example = "HTTP")
        private String interfaceType;

        @ApiModelProperty(value = "接口描述", example = "发送OTP验证码")
        private String interfaceDesc;

        @ApiModelProperty(value = "接口状态", example = "已上线")
        private String interfaceStatus;

        @ApiModelProperty(value = "版本列表")
        private List<VersionInfo> children;
    }

    /**
     * 版本信息
     */
    @Data
    @ApiModel(value = "版本信息", description = "版本信息")
    public static class VersionInfo {

        @ApiModelProperty(value = "版本号", example = "v1.0.0")
        private String version;

        @ApiModelProperty(value = "版本标识", example = "v1_0_0")
        private String versionKey;

        @ApiModelProperty(value = "版本描述", example = "初始版本")
        private String versionDesc;

        @ApiModelProperty(value = "版本状态", example = "已上线")
        private String versionStatus;

        @ApiModelProperty(value = "请求示例")
        private Object requestExample;

        @ApiModelProperty(value = "响应示例", example = "响应示例")
        private String responseExample;

        @ApiModelProperty(value = "功能参数列表")
        private List<FeatureParam> featureParams;
    }

    /**
     * 功能参数
     */
    @Data
    @ApiModel(value = "功能参数", description = "功能参数")
    public static class FeatureParam {

        @ApiModelProperty(value = "参数名称", example = "RequestBody")
        private String name;

        @ApiModelProperty(value = "参数类型", example = "object")
        private String type;

        @ApiModelProperty(value = "子参数列表")
        private List<FeatureParam> children;
    }
}
