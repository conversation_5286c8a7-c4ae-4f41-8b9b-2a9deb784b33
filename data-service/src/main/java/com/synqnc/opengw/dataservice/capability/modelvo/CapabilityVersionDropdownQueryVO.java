package com.synqnc.opengw.dataservice.capability.modelvo;

import com.synqnc.opengw.dataservice.common.modelso.PageQuerySO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author: AI Assistant
 * @Date: 2025/1/1
 * @Description: 能力接口版本下拉数据查询VO
 * @Version: 1.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "能力接口版本下拉数据查询VO", description = "能力接口版本下拉数据查询VO")
public class CapabilityVersionDropdownQueryVO extends PageQuerySO {

    @ApiModelProperty(value = "能力名称(支持模糊搜索)", example = "OTP验证")
    private String capabilityName;
}
