---
type: "always_apply"
---

# 接口开发规则
## 新增接口
所有新增接口均需重新创建Controller层和Service层。
只能操作operate-backend和open-log-center-service两个项目，严格按照下方开发规范来

## 功能参数与表信息
1. 实现功能时，将提供所需的入参、出参，以及涉及的表及其类型。
2. 涉及数据库表的接口，需查阅根目录下`db-schemas/index.md`中的数据表字段类型及结构信息。每次提问前，所有相关数据表信息将置于该文件中。

## 功能实现项目
1. **基本功能**：在`operate-backend`项目中实现。
2. **CK数据库表功能**：在`open-log-center-service`项目中实现操作CK表的功能，供`operate-backend`调用。

## operate-backend开发规范
### Controller层
在`operate-backend/src/main/java/com/synqnc/opengw/operate/api/bill`路径下开发。

### Service层
1. 路径为`operate-backend/src/main/java/com/synqnc/opengw/operate/service/bill/service`。
2. `service`下包含两个文件夹：
    - `itf`：存放接口定义文件，定义服务类方法签名，无具体实现逻辑。
    - `impl`：存放接口具体实现类，实现接口方法，包含业务逻辑。

## open-log-center-service开发规范
### Controller层
在`open-log-center-service/open-log-center/src/main/java/com/unicom/open/logcenter/controller2`路径下开发。

### Service层
1. 路径为`open-log-center-service/open-log-center/src/main/java/com/unicom/open/logcenter/service`。
2. `service`下的`impl`文件夹存放接口具体实现类，实现接口方法并包含业务逻辑。接口定义文件直接存放在`service`下，与`impl`文件夹同级。

## 注释要求
除简单语法定义外，均需添加注释。