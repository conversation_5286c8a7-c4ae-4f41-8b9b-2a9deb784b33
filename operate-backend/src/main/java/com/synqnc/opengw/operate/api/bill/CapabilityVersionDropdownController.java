package com.synqnc.opengw.operate.api.bill;

import com.synqnc.opengw.common.service.uniauth.service.itf.LoginUserService;
import com.synqnc.opengw.common.utils.PageUtils;
import com.synqnc.opengw.dataservice.capability.modelvo.CapabilityVersionDropdownQueryVO;
import com.synqnc.opengw.dataservice.capability.modelvo.CapabilityVersionDropdownVO;
import com.synqnc.opengw.dataservice.common.modelui.ControllerReturnUI;
import com.synqnc.opengw.dataservice.uniauth.modelvo.LoginUserInfo;
import com.synqnc.opengw.operate.service.bill.service.itf.CapabilityVersionDropdownService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * @Author: AI Assistant
 * @Date: 2025/1/1
 * @Description: 能力接口版本下拉数据Controller
 * @Version: 1.0
 */
@Api(value = "能力接口版本", tags = {"能力接口版本"})
@RestController
@RequestMapping("/capability")
public class CapabilityVersionDropdownController {

    @Autowired
    private CapabilityVersionDropdownService capabilityVersionDropdownService;
    
    @Autowired
    private LoginUserService loginUserService;

    @ApiOperation(value = "能力接口版本下拉数据(分页)", notes = "根据能力名称获取能力接口版本的下拉框数据，支持分页和模糊搜索")
    @PostMapping("/getCapabilityVersionDropdownData")
    public ControllerReturnUI<PageUtils<CapabilityVersionDropdownVO>> getCapabilityVersionDropdownData(
            @RequestBody CapabilityVersionDropdownQueryVO queryVO, 
            HttpServletRequest httpRequest) {
        
        // 添加认证检查
        LoginUserInfo loginUserInfo = loginUserService.getLoginUserInfo(httpRequest);
        
        PageUtils<CapabilityVersionDropdownVO> result = capabilityVersionDropdownService.getCapabilityVersionDropdownData(queryVO);
        return ControllerReturnUI.generateByData(result);
    }
}
