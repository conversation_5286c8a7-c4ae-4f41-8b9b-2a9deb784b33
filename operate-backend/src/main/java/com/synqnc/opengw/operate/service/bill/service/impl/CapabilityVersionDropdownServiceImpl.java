package com.synqnc.opengw.operate.service.bill.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.synqnc.opengw.common.utils.PageUtils;
import com.synqnc.opengw.common.utils.LimtPageSizeUtils;
import com.synqnc.opengw.dataservice.capability.modelvo.CapabilityVersionDropdownQueryVO;
import com.synqnc.opengw.dataservice.capability.modelvo.CapabilityVersionDropdownVO;
import com.synqnc.opengw.dataservice.product.mapper.IProductMapper;
import com.synqnc.opengw.dataservice.capability.mapper.ICapabilityMapper;
import com.synqnc.opengw.dataservice.capability.mapper.IVersionMapper;
import com.synqnc.opengw.dataservice.product.mapper.IProductCapabilityMapper;
import com.synqnc.opengw.dataservice.capability.model.ICapability;
import com.synqnc.opengw.dataservice.capability.model.IVersion;
import com.synqnc.opengw.dataservice.capability.model.IVersionExample;
import com.synqnc.opengw.dataservice.product.model.IProductCapability;
import com.synqnc.opengw.dataservice.product.model.IProductCapabilityExample;
import com.synqnc.opengw.operate.service.bill.service.itf.CapabilityVersionDropdownService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @Author: AI Assistant
 * @Date: 2025/1/1
 * @Description: 能力接口版本下拉数据Service实现类
 * @Version: 1.0
 */
@Service
public class CapabilityVersionDropdownServiceImpl implements CapabilityVersionDropdownService {

    @Autowired
    private IProductMapper iProductMapper;

    @Autowired
    private ICapabilityMapper iCapabilityMapper;

    @Autowired
    private IVersionMapper iVersionMapper;

    @Autowired
    private IProductCapabilityMapper iProductCapabilityMapper;

    @Override
    public PageUtils<CapabilityVersionDropdownVO> getCapabilityVersionDropdownData(CapabilityVersionDropdownQueryVO queryVO) {
        // 处理分页参数
        Long page = queryVO.getPage() == null ? 1L : queryVO.getPage();
        Integer pageSize = LimtPageSizeUtils.processPageSize(queryVO.getPageSize());
        Long offset = (page - 1) * pageSize;

        // 处理查询参数
        String capabilityName = StringUtils.isBlank(queryVO.getCapabilityName()) ? null : queryVO.getCapabilityName().trim();

        // 查询总数
        long totalCount = iProductMapper.countCapabilityVersionDropdownData(capabilityName);

        // 查询数据列表
        List<CapabilityVersionDropdownVO> rawDataList = iProductMapper.selectCapabilityVersionDropdownData(
                capabilityName, offset, pageSize);

        // 为每个能力构建嵌套结构数据
        List<CapabilityVersionDropdownVO> resultList = buildNestedStructure(rawDataList);

        // 构建分页结果
        return new PageUtils<>(totalCount, page, pageSize, resultList);
    }

    /**
     * 构建嵌套结构数据（能力-接口-版本）
     * @param rawDataList 原始数据列表
     * @return 嵌套结构数据列表
     */
    private List<CapabilityVersionDropdownVO> buildNestedStructure(List<CapabilityVersionDropdownVO> rawDataList) {
        if (rawDataList == null || rawDataList.isEmpty()) {
            return new ArrayList<>();
        }

        // 为每个能力查询真实的接口和版本数据
        for (CapabilityVersionDropdownVO product : rawDataList) {
            String productKey = product.getProductKey();

            // 查询该能力关联的接口
            IProductCapabilityExample pcExample = new IProductCapabilityExample();
            pcExample.createCriteria().andOpgwProductKeyEqualTo(productKey);
            List<IProductCapability> productCapabilities = iProductCapabilityMapper.selectByExample(pcExample);

            List<CapabilityVersionDropdownVO.InterfaceInfo> interfaceList = new ArrayList<>();

            for (IProductCapability pc : productCapabilities) {
                String capabilityKey = pc.getOpgwCapabilityKey();

                // 查询接口信息
                ICapability capability = iCapabilityMapper.selectByPrimaryKey(capabilityKey);
                if (capability == null || "deleted".equals(capability.getStatus())) {
                    continue;
                }

                CapabilityVersionDropdownVO.InterfaceInfo interfaceInfo = new CapabilityVersionDropdownVO.InterfaceInfo();
                interfaceInfo.setInterfaceName(capability.getName());
                interfaceInfo.setInterfaceKey(capability.getOpgwCapabilityKey());
                interfaceInfo.setInterfaceType(capability.getProtocolType());
                interfaceInfo.setInterfaceDesc(capability.getSummary());
                interfaceInfo.setInterfaceStatus(capability.getStatus());

                // 查询该接口的版本信息
                IVersionExample versionExample = new IVersionExample();
                versionExample.createCriteria()
                    .andOpgwCapabilityKeyEqualTo(capabilityKey)
                    .andStatusNotEqualTo("deleted");
                List<IVersion> versions = iVersionMapper.selectByExample(versionExample);

                List<CapabilityVersionDropdownVO.VersionInfo> versionList = new ArrayList<>();

                for (IVersion version : versions) {
                    CapabilityVersionDropdownVO.VersionInfo versionInfo = new CapabilityVersionDropdownVO.VersionInfo();
                    versionInfo.setVersion(version.getVersionNum());
                    versionInfo.setVersionKey(version.getOldVersionId());
                    versionInfo.setVersionDesc(version.getDescription());
                    versionInfo.setVersionStatus(version.getStatus());
                    versionInfo.setRequestExample(parseJsonExample(version.getReqExampleParam()));
                    versionInfo.setResponseExample(version.getRespExampleParam());

                    // 构建featureParams
                    List<CapabilityVersionDropdownVO.FeatureParam> featureParams = buildFeatureParams(
                        version.getReqExampleParam(), version.getRespExampleParam());
                    versionInfo.setFeatureParams(featureParams);

                    versionList.add(versionInfo);
                }

                interfaceInfo.setChildren(versionList);
                interfaceList.add(interfaceInfo);
            }

            product.setChildren(interfaceList);
        }

        return rawDataList;
    }

    /**
     * 解析JSON示例参数
     * @param jsonStr JSON字符串
     * @return 解析后的对象，如果解析失败则返回原字符串
     */
    private Object parseJsonExample(String jsonStr) {
        if (StringUtils.isBlank(jsonStr)) {
            return null;
        }

        try {
            return JSON.parse(jsonStr);
        } catch (Exception e) {
            // 如果解析失败，返回原字符串
            return jsonStr;
        }
    }

    /**
     * 构建featureParams结构
     * @param reqExampleParam 请求示例参数JSON字符串
     * @param respExampleParam 响应示例参数JSON字符串
     * @return featureParams列表
     */
    private List<CapabilityVersionDropdownVO.FeatureParam> buildFeatureParams(String reqExampleParam, String respExampleParam) {
        List<CapabilityVersionDropdownVO.FeatureParam> featureParams = new ArrayList<>();

        // 构建RequestBody参数
        CapabilityVersionDropdownVO.FeatureParam requestBodyParam = new CapabilityVersionDropdownVO.FeatureParam();
        requestBodyParam.setName("RequestBody");
        requestBodyParam.setType("object");
        requestBodyParam.setChildren(parseJsonToFeatureParams(reqExampleParam));
        featureParams.add(requestBodyParam);

        // 构建ResponseBody参数
        CapabilityVersionDropdownVO.FeatureParam responseBodyParam = new CapabilityVersionDropdownVO.FeatureParam();
        responseBodyParam.setName("ResponseBody");
        responseBodyParam.setType("object");
        responseBodyParam.setChildren(parseJsonToFeatureParams(respExampleParam));
        featureParams.add(responseBodyParam);

        return featureParams;
    }

    /**
     * 将JSON字符串解析为FeatureParam列表
     * @param jsonStr JSON字符串
     * @return FeatureParam列表
     */
    private List<CapabilityVersionDropdownVO.FeatureParam> parseJsonToFeatureParams(String jsonStr) {
        List<CapabilityVersionDropdownVO.FeatureParam> children = new ArrayList<>();

        if (StringUtils.isBlank(jsonStr) || "{}".equals(jsonStr.trim())) {
            return children;
        }

        try {
            JSONObject jsonObject = JSON.parseObject(jsonStr);
            if (jsonObject != null) {
                for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
                    CapabilityVersionDropdownVO.FeatureParam param = new CapabilityVersionDropdownVO.FeatureParam();
                    param.setName(entry.getKey());
                    param.setType(getJsonValueType(entry.getValue()));

                    // 如果值是对象，递归解析
                    if (entry.getValue() instanceof JSONObject) {
                        param.setChildren(parseJsonToFeatureParams(entry.getValue().toString()));
                    } else {
                        param.setChildren(new ArrayList<>());
                    }

                    children.add(param);
                }
            }
        } catch (Exception e) {
            // 解析失败时返回空列表
        }

        return children;
    }

    /**
     * 获取JSON值的类型
     * @param value JSON值
     * @return 类型字符串
     */
    private String getJsonValueType(Object value) {
        if (value == null) {
            return "null";
        } else if (value instanceof String) {
            return "String";
        } else if (value instanceof Integer || value instanceof Long) {
            return "Integer";
        } else if (value instanceof Double || value instanceof Float) {
            return "Double";
        } else if (value instanceof Boolean) {
            return "Boolean";
        } else if (value instanceof JSONObject) {
            return "object";
        } else if (value instanceof List) {
            return "array";
        } else {
            return "String";
        }
    }
}
