package com.synqnc.opengw.operate.service.bill.service.impl;

import com.synqnc.opengw.common.utils.PageUtils;
import com.synqnc.opengw.common.utils.LimtPageSizeUtils;
import com.synqnc.opengw.dataservice.capability.modelvo.CapabilityVersionDropdownQueryVO;
import com.synqnc.opengw.dataservice.capability.modelvo.CapabilityVersionDropdownVO;
import com.synqnc.opengw.dataservice.product.mapper.IProductMapper;
import com.synqnc.opengw.operate.service.bill.service.itf.CapabilityVersionDropdownService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: AI Assistant
 * @Date: 2025/1/1
 * @Description: 能力接口版本下拉数据Service实现类
 * @Version: 1.0
 */
@Service
public class CapabilityVersionDropdownServiceImpl implements CapabilityVersionDropdownService {

    @Autowired
    private IProductMapper iProductMapper;

    @Override
    public PageUtils<CapabilityVersionDropdownVO> getCapabilityVersionDropdownData(CapabilityVersionDropdownQueryVO queryVO) {
        // 处理分页参数
        Long page = queryVO.getPage() == null ? 1L : queryVO.getPage();
        Integer pageSize = LimtPageSizeUtils.processPageSize(queryVO.getPageSize());
        Long offset = (page - 1) * pageSize;

        // 处理查询参数
        String capabilityName = StringUtils.isBlank(queryVO.getCapabilityName()) ? null : queryVO.getCapabilityName().trim();

        // 查询总数
        long totalCount = iProductMapper.countCapabilityVersionDropdownData(capabilityName);

        // 查询数据列表
        List<CapabilityVersionDropdownVO> rawDataList = iProductMapper.selectCapabilityVersionDropdownData(
                capabilityName, offset, pageSize);

        // 为每个能力构建嵌套结构数据
        List<CapabilityVersionDropdownVO> resultList = buildNestedStructure(rawDataList);

        // 构建分页结果
        return new PageUtils<>(totalCount, page, pageSize, resultList);
    }

    /**
     * 构建嵌套结构数据（能力-接口-版本）
     * @param rawDataList 原始数据列表
     * @return 嵌套结构数据列表
     */
    private List<CapabilityVersionDropdownVO> buildNestedStructure(List<CapabilityVersionDropdownVO> rawDataList) {
        if (rawDataList == null || rawDataList.isEmpty()) {
            return new ArrayList<>();
        }

        // 为每个能力构建示例的嵌套结构
        for (CapabilityVersionDropdownVO product : rawDataList) {
            // 创建示例接口数据
            List<CapabilityVersionDropdownVO.InterfaceInfo> interfaceList = new ArrayList<>();

            CapabilityVersionDropdownVO.InterfaceInfo interfaceInfo = new CapabilityVersionDropdownVO.InterfaceInfo();
            interfaceInfo.setInterfaceName("发送验证码");
            interfaceInfo.setInterfaceKey("send_verification_code");
            interfaceInfo.setInterfaceType("HTTP");
            interfaceInfo.setInterfaceDesc("发送短信验证码");
            interfaceInfo.setInterfaceStatus("已上线");

            // 创建示例版本数据
            List<CapabilityVersionDropdownVO.VersionInfo> versionList = new ArrayList<>();

            CapabilityVersionDropdownVO.VersionInfo versionInfo = new CapabilityVersionDropdownVO.VersionInfo();
            versionInfo.setVersion("v1.0.0");
            versionInfo.setVersionKey("v1_0_0");
            versionInfo.setVersionDesc("初始版本");
            versionInfo.setVersionStatus("已上线");
            versionInfo.setRequestExample("{\n  \"phoneNumber\": \"+123456789\"\n}");
            versionInfo.setResponseExample("{\n  \"code\": \"200\",\n  \"message\": \"success\"\n}");
            versionInfo.setFeatureParams(new ArrayList<>());

            versionList.add(versionInfo);
            interfaceInfo.setChildren(versionList);
            interfaceList.add(interfaceInfo);

            product.setChildren(interfaceList);
        }

        return rawDataList;
    }
}
