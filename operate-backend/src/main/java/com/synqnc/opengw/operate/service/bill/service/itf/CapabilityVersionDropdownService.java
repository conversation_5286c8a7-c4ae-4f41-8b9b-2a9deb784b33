package com.synqnc.opengw.operate.service.bill.service.itf;

import com.synqnc.opengw.common.utils.PageUtils;
import com.synqnc.opengw.dataservice.capability.modelvo.CapabilityVersionDropdownQueryVO;
import com.synqnc.opengw.dataservice.capability.modelvo.CapabilityVersionDropdownVO;

/**
 * @Author: AI Assistant
 * @Date: 2025/1/1
 * @Description: 能力接口版本下拉数据Service接口
 * @Version: 1.0
 */
public interface CapabilityVersionDropdownService {

    /**
     * 分页获取能力接口版本下拉数据
     * @param queryVO 查询条件
     * @return 分页能力接口版本数据
     */
    PageUtils<CapabilityVersionDropdownVO> getCapabilityVersionDropdownData(CapabilityVersionDropdownQueryVO queryVO);
}
