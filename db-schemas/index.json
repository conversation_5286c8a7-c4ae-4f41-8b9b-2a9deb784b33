{
    "code": "200",
    "message": "查询成功",
    "messageEn": "Query successful",
    "data": {
        "totalCount": 100,
        "totalPage": 10,
        "pageSize": 10,
        "currentPage": 1,
        "rows": [
            {
                "productName": "能力名称",
                "productKey": "能力key",
                "requestExample": "请求示例",
                "responseExample": "响应示例",
                "productDesc": "能力描述",
                "productType": "能力类型",
                "productStatus": "能力状态",
                "children": [
                    {
                        "interfaceName": "接口名称",
                        "interfaceKey": "接口标识",
                        "interfaceType": "接口类型",
                        "interfaceDesc": "接口描述",
                        "interfaceStatus": "接口状态",
                        "children": [
                            {
                                "version": "版本",
                                "versionKey": "版本key",
                                "versionDesc": "版本描述",
                                "versionStatus": "版本状态",
                                // json类型 string
                                "requestExample": {
                                    "device": {
                                        "phoneNumber": "+123456789",
                                        "networkAccessIdentifier": "<EMAIL>",
                                        "ipv4Address": {
                                            "publicAddress": "***********",
                                            "publicPort": 59765
                                        },
                                        "ipv6Address": "2001:db8:85a3:8d3:1319:8a2e:370:7344"
                                    },
                                    "applicationServer": {
                                        "ipv4Address": "************/24",
                                        "ipv6Address": "2001:db8:85a3:8d3:1319:8a2e:370:7344"
                                    },
                                    "devicePorts": {
                                        "ranges": [
                                            {
                                                "from": 5010,
                                                "to": 5020
                                            }
                                        ],
                                        "ports": [
                                            5060,
                                            5070
                                        ]
                                    },
                                    "applicationServerPorts": {
                                        "ranges": [
                                            {
                                                "from": 5010,
                                                "to": 5020
                                            }
                                        ],
                                        "ports": [
                                            5060,
                                            5070
                                        ]
                                    },
                                    "qosProfile": "voice",
                                    "sink": "https://endpoint.example.com/sink",
                                    "sinkCredential": {
                                        "credentialType": "PLAIN"
                                    },
                                    "duration": 3600
                                },
                                // json类型 string
                                "responseExample": "响应示例",
                                // 只有RequestBody和ResponseBody两个字段 其中的children是requestExample和responseExample转换后的数组
                                "featureParams": [
                                    {
                                        "name": "RequestBody",
                                        "children": [
                                            {
                                                "name": "device",
                                                "children": [
                                                    {
                                                        "name": "phoneNumber",
                                                        "type": "String"
                                                    },
                                                    {
                                                        "name": "networkAccessIdentifier",
                                                        "type": "String"
                                                    },
                                                    {
                                                        "name": "ipv4Address",
                                                        "type": "object",
                                                        "children": [
                                                            {
                                                                "name": "publicAddress",
                                                                "type": "String"
                                                            },
                                                            {
                                                                "name": "publicPort",
                                                                "type": "Integer"
                                                            }
                                                        ]
                                                    },
                                                    {
                                                        "name": "ipv6Address",
                                                        "type": "String"
                                                    }
                                                ],
                                                "type": "object"
                                            },
                                            {
                                                "name": "applicationServer",
                                                "children": [
                                                    {
                                                        "name": "ipv4Address",
                                                        "type": "String"
                                                    },
                                                    {
                                                        "name": "ipv6Address",
                                                        "type": "String"
                                                    }
                                                ],
                                                "type": "object"
                                            },
                                            {
                                                "name": "devicePorts",
                                                "children": [
                                                    {
                                                        "name": "ranges",
                                                        "type": "array",
                                                        "children": [
                                                            {
                                                                "name": "from",
                                                                "type": "Integer"
                                                            },
                                                            {
                                                                "name": "to",
                                                                "type": "Integer"
                                                            }
                                                        ]
                                                    },
                                                    {
                                                        "name": "ports",
                                                        "type": "array",
                                                        "children": [
                                                            {
                                                                "name": "port",
                                                                "type": "Integer"
                                                            }
                                                        ]
                                                    }
                                                ],
                                                "type": "object"
                                            },
                                            {
                                                "name": "applicationServerPorts",
                                                "children": [
                                                    {
                                                        "name": "ranges",
                                                        "type": "array",
                                                        "children": [
                                                            {
                                                                "name": "from",
                                                                "type": "Integer"
                                                            },
                                                            {
                                                                "name": "to",
                                                                "type": "Integer"
                                                            }
                                                        ]
                                                    },
                                                    {
                                                        "name": "ports",
                                                        "type": "array",
                                                        "children": [
                                                            {
                                                                "name": "port",
                                                                "type": "Integer"
                                                            }
                                                        ]
                                                    }
                                                ],
                                                "type": "object"
                                            },
                                            {
                                                "name": "qosProfile",
                                                "type": "String"
                                            },
                                            {
                                                "name": "sink",
                                                "type": "String"
                                            },
                                            {
                                                "name": "sinkCredential",
                                                "children": [
                                                    {
                                                        "name": "credentialType",
                                                        "type": "String"
                                                    }
                                                ],
                                                "type": "object"
                                            },
                                            {
                                                "name": "duration",
                                                "type": "Integer"
                                            }
                                        ],
                                        "type": "object"
                                    },
                                    {
                                        "name": "ResponseBody",
                                        "children": []
                                    }
                                ]
                            }
                        ]
                    }
                ]
            }
        ]
    }
}