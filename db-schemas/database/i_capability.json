[{"opgw_capability_key": "0108test1", "old_capability_key": null, "old_capability_id": "2501080944503409005", "status": "online", "name": "0108test1", "name_en": "0108test1", "summary": "0108test1", "summary_en": "0108test1", "create_time": "8/1/2025 09:44:50", "update_time": "8/1/2025 09:44:50", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "8/1/2025 09:44:50", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1736300690341, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": "0108test1", "is_mock": 0}, {"opgw_capability_key": "0108test11", "old_capability_key": null, "old_capability_id": "2501081108373919011", "status": "online", "name": "0108测试1（非CAMARA）", "name_en": "0108test1", "summary": "0108测试1（非CAMARA）", "summary_en": "0108test1", "create_time": "8/1/2025 11:08:37", "update_time": "8/1/2025 11:08:37", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "8/1/2025 11:08:37", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1736305717392, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": "0108测试1（非CAMARA）", "is_mock": 1}, {"opgw_capability_key": "0108test2", "old_capability_key": null, "old_capability_id": "2501080948536999008", "status": "online", "name": "0108test2", "name_en": "0108test2", "summary": "0108test2", "summary_en": "0108test2", "create_time": "8/1/2025 09:48:54", "update_time": "8/1/2025 09:48:54", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "8/1/2025 09:48:54", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1736300933699, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "0108test22", "old_capability_key": null, "old_capability_id": "2501081138418679019", "status": "online", "name": "0108测试2（CAMARA）", "name_en": "0108test2", "summary": "0108test2", "summary_en": "0108test2", "create_time": "8/1/2025 11:38:42", "update_time": "8/1/2025 11:38:42", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "8/1/2025 11:38:42", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1736307521867, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": 101, "note": "0108test2", "is_mock": 1}, {"opgw_capability_key": "0109test", "old_capability_key": null, "old_capability_id": "2501091614388169005", "status": "online", "name": "1-9测试（CAMARA）", "name_en": "1-9 test", "summary": "录入接口，进行测试", "summary_en": "add api,testing", "create_time": "9/1/2025 16:14:39", "update_time": "10/1/2025 17:55:28", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "9/1/2025 16:14:39", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1736502928445, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": 101, "note": "", "is_mock": 1}, {"opgw_capability_key": "0109test1", "old_capability_key": null, "old_capability_id": "2501091820138769001", "status": "online", "name": "1-9测试（非c）", "name_en": "aaaaaaaaa", "summary": "今夕计信系暗示", "summary_en": "aaaaaaaa", "create_time": "9/1/2025 18:20:14", "update_time": "9/1/2025 18:20:14", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "9/1/2025 18:20:14", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1736418013878, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "achenceshi1", "old_capability_key": null, "old_capability_id": "2502061731365159001", "status": "online", "name": "阿晨测试", "name_en": "testes", "summary": "阿晨测试的", "summary_en": "achenceshi1", "create_time": "6/2/2025 17:31:37", "update_time": "19/3/2025 22:48:23", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "6/2/2025 17:31:37", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": *************, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 1}, {"opgw_capability_key": "addFenceTask", "old_capability_key": "Ic6J34", "old_capability_id": "2405241621180269292", "status": "online", "name": "添加围栏事件订阅", "name_en": "addFenceTask", "summary": "进行发送短信功能的能力", "summary_en": "addFenceTask", "create_time": "27/5/2024 10:39:36", "update_time": "26/6/2024 11:34:28", "userid": 99, "username": "jiangsd3", "realname": "蒋少东", "orgid": 39, "org_name": "联通智网创新中心本部-网络数据室", "online_time": "27/5/2024 10:39:36", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1719390203047, "capability_function": "insert", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "addInterfacepost", "old_capability_key": null, "old_capability_id": "2507241131467729144", "status": "online", "name": "普通接口新增", "name_en": "addInterfacepost", "summary": "addInterfacepost", "summary_en": "addInterfacepost", "create_time": "24/7/2025 11:31:47", "update_time": "24/7/2025 11:31:47", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "24/7/2025 11:31:47", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1753327906777, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "addInterfaceSIM", "old_capability_key": null, "old_capability_id": "2503201545550309491", "status": "online", "name": "生产SIM检索接口多id前段流，后端流", "name_en": "addInterfaceSIM", "summary": "addInterfaceSIM", "summary_en": "addInterfaceSIM", "create_time": "20/3/2025 15:45:55", "update_time": "20/3/2025 15:45:55", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "20/3/2025 15:45:55", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1742456755030, "capability_function": "query", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "addLimitedRegionShape", "old_capability_key": "ZZFeIL", "old_capability_id": "2405241636488249159", "status": "online", "name": "新增自定义区域-按轮廓与行政区", "name_en": "addLimitedRegionShape", "summary": "进行发送短信功能的能力", "summary_en": "addLimitedRegionShape", "create_time": "27/5/2024 10:39:37", "update_time": "26/6/2024 11:34:29", "userid": 99, "username": "jiangsd3", "realname": "蒋少东", "orgid": 39, "org_name": "联通智网创新中心本部-网络数据室", "online_time": "27/5/2024 10:39:37", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1719372868844, "capability_function": "insert", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "addMsisdnsCardTask", "old_capability_key": "HWLPDb", "old_capability_id": "2405241656587809183", "status": "online", "name": "企业名片任务号码添加接口", "name_en": "addMsisdnsCardTask", "summary": "进行发送短信功能的能力", "summary_en": "addMsisdnsCardTask", "create_time": "27/5/2024 10:39:37", "update_time": "26/6/2024 11:34:30", "userid": 99, "username": "jiangsd3", "realname": "蒋少东", "orgid": 39, "org_name": "联通智网创新中心本部-网络数据室", "online_time": "27/5/2024 10:39:37", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1719372869642, "capability_function": "insert", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "add<PERSON><PERSON>", "old_capability_key": "xugxGS", "old_capability_id": "2405241714480809188", "status": "online", "name": "司法矫正产品_矫正人员录入", "name_en": "add<PERSON><PERSON>", "summary": "进行发送短信功能的能力", "summary_en": "add<PERSON><PERSON>", "create_time": "27/5/2024 10:39:38", "update_time": "26/6/2024 11:34:30", "userid": 99, "username": "jiangsd3", "realname": "蒋少东", "orgid": 39, "org_name": "联通智网创新中心本部-网络数据室", "online_time": "27/5/2024 10:39:38", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1719372869922, "capability_function": "insert", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "addRegionCell", "old_capability_key": "ruFih3", "old_capability_id": "2405241625031919141", "status": "online", "name": "新增自定义区域-基站范围", "name_en": "addRegionCell", "summary": "进行发送短信功能的能力", "summary_en": "addRegionCell", "create_time": "27/5/2024 10:39:36", "update_time": "26/6/2024 11:34:28", "userid": 99, "username": "jiangsd3", "realname": "蒋少东", "orgid": 39, "org_name": "联通智网创新中心本部-网络数据室", "online_time": "27/5/2024 10:39:36", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1719372868429, "capability_function": "insert", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "addRegionShape", "old_capability_key": "y28sac", "old_capability_id": "2405241609336039131", "status": "online", "name": "新增自定义区域-区域范围", "name_en": "addRegionShape", "summary": "进行发送短信功能的能力", "summary_en": "addRegionShape", "create_time": "27/5/2024 10:39:36", "update_time": "26/6/2024 11:34:28", "userid": 99, "username": "jiangsd3", "realname": "蒋少东", "orgid": 39, "org_name": "联通智网创新中心本部-网络数据室", "online_time": "27/5/2024 10:39:36", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1719372868011, "capability_function": "insert", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "Adiskca", "old_capability_key": null, "old_capability_id": "2411131146381189015", "status": "online", "name": "用户呼转签约查询（联合）", "name_en": "Adiskca", "summary": "用户呼转签约查询（联合）实现了第三方应用通过输入用户手机号码即可查询该用户是否签约业务", "summary_en": "Adiskca", "create_time": "13/11/2024 11:46:38", "update_time": "13/11/2024 11:46:38", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "13/11/2024 11:46:38", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1731469598119, "capability_function": "insert", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "AttachDisk", "old_capability_key": null, "old_capability_id": "2501101538039279003", "status": "online", "name": "为一台ECS实例挂载一块数据盘", "name_en": "Example Mount a data disk to an ECS instance", "summary": "为一台ECS实例挂载一块数据盘", "summary_en": "Example Mount a data disk to an ECS instance", "create_time": "10/1/2025 15:38:04", "update_time": "10/1/2025 15:38:04", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "10/1/2025 15:38:04", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1736494683928, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "batchGetResidentialUsers", "old_capability_key": "IMJaNs", "old_capability_id": "2405241534271529270", "status": "online", "name": "批量小区用户数查询", "name_en": "batchGetResidentialUsers", "summary": "进行发送短信功能的能力", "summary_en": "batchGetResidentialUsers", "create_time": "27/5/2024 10:39:35", "update_time": "26/6/2024 11:34:28", "userid": 99, "username": "jiangsd3", "realname": "蒋少东", "orgid": 39, "org_name": "联通智网创新中心本部-网络数据室", "online_time": "27/5/2024 10:39:35", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1719372867582, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "batchGetUserByCell", "old_capability_key": "SItA06", "old_capability_id": "2405241446360949240", "status": "online", "name": "基站(批量)实时用户详单查询", "name_en": "batchGetUserByCell", "summary": "进行发送短信功能的能力", "summary_en": "batchGetUserByCell", "create_time": "27/5/2024 10:39:34", "update_time": "26/6/2024 11:34:30", "userid": 99, "username": "jiangsd3", "realname": "蒋少东", "orgid": 39, "org_name": "联通智网创新中心本部-网络数据室", "online_time": "27/5/2024 10:39:34", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1719372870390, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "callForwardings", "old_capability_key": null, "old_capability_id": "2412131134499369001", "status": "online", "name": "活动呼转查询", "name_en": "Active Call", "summary": "此端点提供有关哪种类型的呼叫转移服务处于活动状态的信息。多个服务可以是活动的，例如有条件和无条件的。该端点超出了CFS API的主要范围，因此可以返回错误代码501。", "summary_en": "This endpoint provides information about wich type of call forwarding service is active. More than one service can be active, e.g. conditional and unconditional. This endpoint exceeds the main scope of the CFS API, for this reason an error code 501 can be returned.", "create_time": "13/12/2024 11:34:50", "update_time": "13/12/2024 11:34:50", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "13/12/2024 11:34:50", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1734060889973, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "checkNetworkQuality", "old_capability_key": null, "old_capability_id": "2412172210047479061", "status": "online", "name": "检查网络质量", "name_en": "Checking network quality", "summary": "检查网络质量。响应显示了网络当前的信心水平，即它可以满足给定终端用户设备的应用程序配置文件的质量阈值。", "summary_en": "Check the network quality. The response shows the network's current level of confidence that it can meet the quality threshold of the application profile for a given end-user device.", "create_time": "17/12/2024 22:10:05", "update_time": "17/12/2024 22:10:05", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "17/12/2024 22:10:05", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1734444604748, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 1}, {"opgw_capability_key": "checkSimSwap", "old_capability_key": null, "old_capability_id": "2412172210044689028", "status": "online", "name": "检查换卡操作", "name_en": "Check the card change operation", "summary": "检查在过去一段时间内是否进行过换卡操作", "summary_en": "Check whether the card has been changed in the past period of time", "create_time": "17/12/2024 22:10:04", "update_time": "17/12/2024 22:10:04", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "17/12/2024 22:10:04", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1734444604469, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 1}, {"opgw_capability_key": "cloudsoft", "old_capability_key": null, "old_capability_id": "2411041751199309048", "status": "online", "name": "查询云⽹管应⽤列表", "name_en": "cloudsoft", "summary": "查询云⽹管应⽤列表", "summary_en": "cloudsoft", "create_time": "4/11/2024 17:51:20", "update_time": "4/11/2024 17:51:20", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "4/11/2024 17:51:20", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1730713879940, "capability_function": "insert", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "connectivityInsightsSubscriptions", "old_capability_key": null, "old_capability_id": "2412172248428789171", "status": "online", "name": "添加网络质量检测订阅12", "name_en": "Add a network quality check subscription", "summary": "添加网络质量检测订阅2", "summary_en": "Add a network quality check subscription", "create_time": "17/12/2024 22:48:43", "update_time": "6/1/2025 18:32:22", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "17/12/2024 22:48:43", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1736159541953, "capability_function": "insert", "max_delay": 10, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 1}, {"opgw_capability_key": "connectivityInsightsSubscriptionsList", "old_capability_key": null, "old_capability_id": "2412172248429049174", "status": "online", "name": "检索网络质量检测订阅", "name_en": "Retrieve a network quality inspection subscription", "summary": "检索网络质量检测订阅", "summary_en": "Retrieve a network quality inspection subscription", "create_time": "17/12/2024 22:48:43", "update_time": "17/12/2024 22:48:43", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "17/12/2024 22:48:43", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1734446922905, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 1}, {"opgw_capability_key": "createApplicationProfiles", "old_capability_key": null, "old_capability_id": "2412172210046399049", "status": "online", "name": "创建网络检测模型", "name_en": "Define network monitoring intentions", "summary": "定义网络监控意图，以获得最佳的最终用户应用程序体验。", "summary_en": "Define network monitoring intentions for the best end-user application experience.", "create_time": "17/12/2024 22:10:05", "update_time": "17/12/2024 22:10:05", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "17/12/2024 22:10:05", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1734444604640, "capability_function": "insert", "max_delay": 10, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 1}, {"opgw_capability_key": "createCardTask", "old_capability_key": "04Kmpy", "old_capability_id": "2405241651028679307", "status": "online", "name": "企业名片任务创建", "name_en": "createCardTask", "summary": "进行发送短信功能的能力", "summary_en": "createCardTask", "create_time": "27/5/2024 10:39:37", "update_time": "26/6/2024 11:34:29", "userid": 99, "username": "jiangsd3", "realname": "蒋少东", "orgid": 39, "org_name": "联通智网创新中心本部-网络数据室", "online_time": "27/5/2024 10:39:37", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1719372869182, "capability_function": "insert", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "CreateD1111", "old_capability_key": null, "old_capability_id": "2501171420507969037", "status": "online", "name": "手动导入查询", "name_en": "AAAAAAAAAAAA", "summary": "手动添加的轨迹查询", "summary_en": "AAAAAAAAAAAAAAAAA", "create_time": "17/1/2025 14:20:51", "update_time": "17/1/2025 14:20:51", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "17/1/2025 14:20:51", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737094850796, "capability_function": "query", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "CreateDis299", "old_capability_key": null, "old_capability_id": "2501171144291449007", "status": "online", "name": "创建连接质量配置框架", "name_en": "Create one or more data disks", "summary": "创建连接质量配置框架", "summary_en": "Create one or more data disks", "create_time": "17/1/2025 11:44:29", "update_time": "17/1/2025 11:44:29", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "17/1/2025 11:44:29", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737085469146, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": 101, "note": "POST", "is_mock": 1}, {"opgw_capability_key": "CreateDis399", "old_capability_key": null, "old_capability_id": "2501171159065419017", "status": "online", "name": "批量导入查询", "name_en": "Create one or more data disks", "summary": "批量导入查询", "summary_en": "Create one or more data disks", "create_time": "17/1/2025 11:59:07", "update_time": "17/1/2025 11:59:07", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "17/1/2025 11:59:07", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737086346541, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": "POST", "is_mock": 1}, {"opgw_capability_key": "CreateDis499", "old_capability_key": null, "old_capability_id": "2501171234034139024", "status": "online", "name": "批量导入查询1", "name_en": "Create one or more data disks", "summary": "批量导入查询", "summary_en": "Create one or more data disks", "create_time": "17/1/2025 12:34:03", "update_time": "17/1/2025 12:34:03", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "17/1/2025 12:34:03", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737088443413, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": "POST", "is_mock": 1}, {"opgw_capability_key": "CreateDis599", "old_capability_key": null, "old_capability_id": "2501171346034229033", "status": "online", "name": "批量导入查询1", "name_en": "Create one or more data disks", "summary": "批量导入查询", "summary_en": "Create one or more data disks", "create_time": "17/1/2025 13:46:03", "update_time": "17/1/2025 13:46:03", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "17/1/2025 13:46:03", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737092763422, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": "POST", "is_mock": 0}, {"opgw_capability_key": "CreateDis600", "old_capability_key": null, "old_capability_id": "2501171426209969044", "status": "online", "name": "批量导入查询2", "name_en": "Create one or more data disks", "summary": "批量导入查询", "summary_en": "Create one or more data disks", "create_time": "17/1/2025 14:26:21", "update_time": "17/1/2025 18:11:44", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "17/1/2025 14:26:21", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1737108703759, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": "POST", "is_mock": 1}, {"opgw_capability_key": "CreateDis601", "old_capability_key": null, "old_capability_id": "2501171427563459048", "status": "online", "name": "批量导入查询2", "name_en": "Create one or more data disks", "summary": "批量导入查询", "summary_en": "Create one or more data disks", "create_time": "17/1/2025 14:27:56", "update_time": "17/1/2025 14:27:56", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "17/1/2025 14:27:56", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737095276345, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": "POST", "is_mock": 0}, {"opgw_capability_key": "CreateDis602", "old_capability_key": null, "old_capability_id": "2501171435168149052", "status": "online", "name": "批量导入查询2", "name_en": "Create one or more data disks", "summary": "批量导入查询", "summary_en": "Create one or more data disks", "create_time": "17/1/2025 14:35:17", "update_time": "10/3/2025 17:35:17", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "17/1/2025 14:35:17", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737095716814, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": "POST", "is_mock": 0}, {"opgw_capability_key": "CreateDisk1", "old_capability_key": null, "old_capability_id": "2501161638449849035", "status": "online", "name": "创建数据盘", "name_en": "Create one or more data disks", "summary": "创建一块或多块数据盘", "summary_en": "Create one or more data disks", "create_time": "16/1/2025 16:38:45", "update_time": "16/1/2025 16:38:45", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/1/2025 16:38:45", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737016724984, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": "POST", "is_mock": 0}, {"opgw_capability_key": "CreateDisk10", "old_capability_key": null, "old_capability_id": "2501161637392249021", "status": "online", "name": "删除云硬盘", "name_en": "Delete cloud disks", "summary": "删除云硬盘", "summary_en": "Delete cloud disks", "create_time": "16/1/2025 16:37:39", "update_time": "16/1/2025 16:37:39", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/1/2025 16:37:39", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737016659224, "capability_function": "delete", "max_delay": null, "capability_type": 1, "capability_label": null, "note": "DELETE", "is_mock": 0}, {"opgw_capability_key": "CreateDisk11", "old_capability_key": null, "old_capability_id": "2501161637392729024", "status": "online", "name": "查询存储", "name_en": "Query one or more block storages", "summary": "查询一块或多块块存储", "summary_en": "Query one or more block storages", "create_time": "16/1/2025 16:37:39", "update_time": "16/1/2025 16:37:39", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/1/2025 16:37:39", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737016659272, "capability_function": "query", "max_delay": null, "capability_type": 1, "capability_label": null, "note": "GET", "is_mock": 0}, {"opgw_capability_key": "CreateDisk111", "old_capability_key": null, "old_capability_id": "2501161639444279066", "status": "online", "name": "创建数据盘", "name_en": "Create one or more data disks", "summary": "创建一块或多块数据盘", "summary_en": "Create one or more data disks", "create_time": "16/1/2025 16:39:44", "update_time": "16/1/2025 16:39:44", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/1/2025 16:39:44", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737016784427, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": "POST", "is_mock": 0}, {"opgw_capability_key": "CreateDisk112", "old_capability_key": null, "old_capability_id": "2501161639444749069", "status": "online", "name": "删除云硬盘", "name_en": "Delete cloud disks", "summary": "删除云硬盘", "summary_en": "Delete cloud disks", "create_time": "16/1/2025 16:39:44", "update_time": "16/1/2025 16:39:44", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/1/2025 16:39:44", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737016784474, "capability_function": "delete", "max_delay": null, "capability_type": 1, "capability_label": null, "note": "DELETE", "is_mock": 0}, {"opgw_capability_key": "CreateDisk113", "old_capability_key": null, "old_capability_id": "2501161639445259072", "status": "online", "name": "查询存储", "name_en": "Query one or more block storages", "summary": "查询一块或多块块存储", "summary_en": "Query one or more block storages", "create_time": "16/1/2025 16:39:45", "update_time": "16/1/2025 16:39:45", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/1/2025 16:39:45", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737016784525, "capability_function": "query", "max_delay": null, "capability_type": 1, "capability_label": null, "note": "GET", "is_mock": 0}, {"opgw_capability_key": "CreateDisk114", "old_capability_key": null, "old_capability_id": "2501161639445729075", "status": "online", "name": "更新云硬盘信息", "name_en": "Update cloud disk information", "summary": "更新云硬盘信息", "summary_en": "Update cloud disk information", "create_time": "16/1/2025 16:39:45", "update_time": "16/1/2025 16:39:45", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/1/2025 16:39:45", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737016784572, "capability_function": "update", "max_delay": null, "capability_type": 1, "capability_label": null, "note": "PUT", "is_mock": 0}, {"opgw_capability_key": "CreateDisk115", "old_capability_key": null, "old_capability_id": "2501161639446169078", "status": "online", "name": "云硬盘恢复到某次快照", "name_en": "Restore a cloud disk to a specific snapshot", "summary": "云硬盘恢复到某次快照", "summary_en": "Restore a cloud disk to a specific snapshot", "create_time": "16/1/2025 16:39:45", "update_time": "16/1/2025 16:39:45", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/1/2025 16:39:45", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737016784616, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": "POST", "is_mock": 0}, {"opgw_capability_key": "CreateDisk116", "old_capability_key": null, "old_capability_id": "2501161639446589081", "status": "online", "name": "扩容云硬盘", "name_en": "Expand the capacity of a cloud disk", "summary": "扩容云硬盘", "summary_en": "Expand the capacity of a cloud disk", "create_time": "16/1/2025 16:39:45", "update_time": "16/1/2025 16:39:45", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/1/2025 16:39:45", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737016784658, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": "POST", "is_mock": 0}, {"opgw_capability_key": "CreateDisk117", "old_capability_key": null, "old_capability_id": "2501161639447019084", "status": "online", "name": "为ECS实例挂载数据盘", "name_en": "Attach a data disk to an ECS instance", "summary": "为一台ECS实例挂载一块数据盘", "summary_en": "Attach a data disk to an ECS instance", "create_time": "16/1/2025 16:39:45", "update_time": "16/1/2025 16:39:45", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/1/2025 16:39:45", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737016784701, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": "POST", "is_mock": 0}, {"opgw_capability_key": "CreateDisk118", "old_capability_key": null, "old_capability_id": "2501161639447439087", "status": "online", "name": "附加一个弹性网卡", "name_en": "Attach an Elastic Network Interface (ENI)", "summary": "附加一个弹性网卡", "summary_en": "Attach an Elastic Network Interface (ENI)", "create_time": "16/1/2025 16:39:45", "update_time": "16/1/2025 16:39:45", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/1/2025 16:39:45", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737016784743, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": "POST", "is_mock": 0}, {"opgw_capability_key": "CreateDisk119", "old_capability_key": null, "old_capability_id": "2501161639447869090", "status": "online", "name": "创建数据盘", "name_en": "Create one or more data disks", "summary": "创建一块或多块数据盘", "summary_en": "Create one or more data disks", "create_time": "16/1/2025 16:39:45", "update_time": "16/1/2025 16:39:45", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/1/2025 16:39:45", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737016784786, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": "POST", "is_mock": 0}, {"opgw_capability_key": "CreateDisk12", "old_capability_key": null, "old_capability_id": "2501161637393199027", "status": "online", "name": "更新云硬盘信息", "name_en": "Update cloud disk information", "summary": "更新云硬盘信息", "summary_en": "Update cloud disk information", "create_time": "16/1/2025 16:37:39", "update_time": "16/1/2025 16:37:39", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/1/2025 16:37:39", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737016659319, "capability_function": "update", "max_delay": null, "capability_type": 1, "capability_label": null, "note": "PUT", "is_mock": 0}, {"opgw_capability_key": "CreateDisk120", "old_capability_key": null, "old_capability_id": "2501161639448279093", "status": "online", "name": "删除云硬盘", "name_en": "Delete cloud disks", "summary": "删除云硬盘", "summary_en": "Delete cloud disks", "create_time": "16/1/2025 16:39:45", "update_time": "16/1/2025 16:39:45", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/1/2025 16:39:45", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737016784827, "capability_function": "delete", "max_delay": null, "capability_type": 1, "capability_label": null, "note": "DELETE", "is_mock": 0}, {"opgw_capability_key": "CreateDisk121", "old_capability_key": null, "old_capability_id": "2501161639448789096", "status": "online", "name": "查询存储", "name_en": "Query one or more block storages", "summary": "查询一块或多块块存储", "summary_en": "Query one or more block storages", "create_time": "16/1/2025 16:39:45", "update_time": "16/1/2025 16:39:45", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/1/2025 16:39:45", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737016784878, "capability_function": "query", "max_delay": null, "capability_type": 1, "capability_label": null, "note": "GET", "is_mock": 0}, {"opgw_capability_key": "CreateDisk122", "old_capability_key": null, "old_capability_id": "2501161639449289099", "status": "online", "name": "更新云硬盘信息", "name_en": "Update cloud disk information", "summary": "更新云硬盘信息", "summary_en": "Update cloud disk information", "create_time": "16/1/2025 16:39:45", "update_time": "16/1/2025 16:39:45", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/1/2025 16:39:45", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737016784928, "capability_function": "update", "max_delay": null, "capability_type": 1, "capability_label": null, "note": "PUT", "is_mock": 0}, {"opgw_capability_key": "CreateDisk123", "old_capability_key": null, "old_capability_id": "2501161639449859102", "status": "online", "name": "云硬盘恢复到某次快照", "name_en": "Restore a cloud disk to a specific snapshot", "summary": "云硬盘恢复到某次快照", "summary_en": "Restore a cloud disk to a specific snapshot", "create_time": "16/1/2025 16:39:45", "update_time": "16/1/2025 16:39:45", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/1/2025 16:39:45", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737016784985, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": "POST", "is_mock": 0}, {"opgw_capability_key": "CreateDisk124", "old_capability_key": null, "old_capability_id": "2501161639450279105", "status": "online", "name": "扩容云硬盘", "name_en": "Expand the capacity of a cloud disk", "summary": "扩容云硬盘", "summary_en": "Expand the capacity of a cloud disk", "create_time": "16/1/2025 16:39:45", "update_time": "16/1/2025 16:39:45", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/1/2025 16:39:45", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737016785027, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": "POST", "is_mock": 0}, {"opgw_capability_key": "CreateDisk125", "old_capability_key": null, "old_capability_id": "2501161639450699108", "status": "online", "name": "为ECS实例挂载数据盘", "name_en": "Attach a data disk to an ECS instance", "summary": "为一台ECS实例挂载一块数据盘", "summary_en": "Attach a data disk to an ECS instance", "create_time": "16/1/2025 16:39:45", "update_time": "16/1/2025 16:39:45", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/1/2025 16:39:45", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737016785069, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": "POST", "is_mock": 0}, {"opgw_capability_key": "CreateDisk126", "old_capability_key": null, "old_capability_id": "2501161639451149111", "status": "online", "name": "附加一个弹性网卡", "name_en": "Attach an Elastic Network Interface (ENI)", "summary": "附加一个弹性网卡", "summary_en": "Attach an Elastic Network Interface (ENI)", "create_time": "16/1/2025 16:39:45", "update_time": "16/1/2025 16:39:45", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/1/2025 16:39:45", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737016785114, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": "POST", "is_mock": 0}, {"opgw_capability_key": "CreateDisk13", "old_capability_key": null, "old_capability_id": "2501161638453159053", "status": "online", "name": "云硬盘恢复到某次快照", "name_en": "Restore a cloud disk to a specific snapshot", "summary": "云硬盘恢复到某次快照", "summary_en": "Restore a cloud disk to a specific snapshot", "create_time": "16/1/2025 16:38:45", "update_time": "16/1/2025 16:38:45", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/1/2025 16:38:45", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737016725315, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": "POST", "is_mock": 0}, {"opgw_capability_key": "CreateDisk14", "old_capability_key": null, "old_capability_id": "2501161638453569056", "status": "online", "name": "扩容云硬盘", "name_en": "Expand the capacity of a cloud disk", "summary": "扩容云硬盘", "summary_en": "Expand the capacity of a cloud disk", "create_time": "16/1/2025 16:38:45", "update_time": "16/1/2025 16:38:45", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/1/2025 16:38:45", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737016725356, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": "POST", "is_mock": 0}, {"opgw_capability_key": "CreateDisk15", "old_capability_key": null, "old_capability_id": "2501161638453989059", "status": "online", "name": "为ECS实例挂载数据盘", "name_en": "Attach a data disk to an ECS instance", "summary": "为一台ECS实例挂载一块数据盘", "summary_en": "Attach a data disk to an ECS instance", "create_time": "16/1/2025 16:38:45", "update_time": "16/1/2025 16:38:45", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/1/2025 16:38:45", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737016725398, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": "POST", "is_mock": 0}, {"opgw_capability_key": "CreateDisk16", "old_capability_key": null, "old_capability_id": "2501161638454399062", "status": "online", "name": "附加一个弹性网卡", "name_en": "Attach an Elastic Network Interface (ENI)", "summary": "附加一个弹性网卡", "summary_en": "Attach an Elastic Network Interface (ENI)", "create_time": "16/1/2025 16:38:45", "update_time": "16/1/2025 16:38:45", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/1/2025 16:38:45", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737016725439, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": "POST", "is_mock": 0}, {"opgw_capability_key": "CreateDisk2", "old_capability_key": null, "old_capability_id": "2501161637389599007", "status": "online", "name": "删除云硬盘", "name_en": "Delete cloud disks", "summary": "删除云硬盘", "summary_en": "Delete cloud disks", "create_time": "16/1/2025 16:37:39", "update_time": "16/1/2025 16:37:39", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/1/2025 16:37:39", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737016658959, "capability_function": "delete", "max_delay": null, "capability_type": 1, "capability_label": null, "note": "DELETE", "is_mock": 0}, {"opgw_capability_key": "CreateDisk211", "old_capability_key": null, "old_capability_id": "2501161650213599115", "status": "online", "name": "创建数据盘", "name_en": "Create one or more data disks", "summary": "创建一块或多块数据盘", "summary_en": "Create one or more data disks", "create_time": "16/1/2025 16:50:21", "update_time": "16/1/2025 16:50:21", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/1/2025 16:50:21", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737017421359, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": "POST", "is_mock": 0}, {"opgw_capability_key": "CreateDisk212", "old_capability_key": null, "old_capability_id": "2501161650214039118", "status": "online", "name": "删除云硬盘", "name_en": "Delete cloud disks", "summary": "删除云硬盘", "summary_en": "Delete cloud disks", "create_time": "16/1/2025 16:50:21", "update_time": "16/1/2025 16:50:21", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/1/2025 16:50:21", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737017421403, "capability_function": "delete", "max_delay": null, "capability_type": 1, "capability_label": null, "note": "DELETE", "is_mock": 0}, {"opgw_capability_key": "CreateDisk213", "old_capability_key": null, "old_capability_id": "2501161650214549121", "status": "online", "name": "查询存储", "name_en": "Query one or more block storages", "summary": "查询一块或多块块存储", "summary_en": "Query one or more block storages", "create_time": "16/1/2025 16:50:21", "update_time": "16/1/2025 16:50:21", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/1/2025 16:50:21", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737017421454, "capability_function": "query", "max_delay": null, "capability_type": 1, "capability_label": null, "note": "GET", "is_mock": 0}, {"opgw_capability_key": "CreateDisk214", "old_capability_key": null, "old_capability_id": "2501161650215019124", "status": "online", "name": "更新云硬盘信息", "name_en": "Update cloud disk information", "summary": "更新云硬盘信息", "summary_en": "Update cloud disk information", "create_time": "16/1/2025 16:50:22", "update_time": "16/1/2025 16:50:22", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/1/2025 16:50:22", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737017421501, "capability_function": "update", "max_delay": null, "capability_type": 1, "capability_label": null, "note": "PUT", "is_mock": 0}, {"opgw_capability_key": "CreateDisk215", "old_capability_key": null, "old_capability_id": "2501161650215449127", "status": "online", "name": "云硬盘恢复到某次快照", "name_en": "Restore a cloud disk to a specific snapshot", "summary": "云硬盘恢复到某次快照", "summary_en": "Restore a cloud disk to a specific snapshot", "create_time": "16/1/2025 16:50:22", "update_time": "16/1/2025 16:50:22", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/1/2025 16:50:22", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737017421544, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": "POST", "is_mock": 0}, {"opgw_capability_key": "CreateDisk216", "old_capability_key": null, "old_capability_id": "2501161650215859130", "status": "online", "name": "扩容云硬盘", "name_en": "Expand the capacity of a cloud disk", "summary": "扩容云硬盘", "summary_en": "Expand the capacity of a cloud disk", "create_time": "16/1/2025 16:50:22", "update_time": "16/1/2025 16:50:22", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/1/2025 16:50:22", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737017421585, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": "POST", "is_mock": 0}, {"opgw_capability_key": "CreateDisk217", "old_capability_key": null, "old_capability_id": "2501161650216289133", "status": "online", "name": "为ECS实例挂载数据盘", "name_en": "Attach a data disk to an ECS instance", "summary": "为一台ECS实例挂载一块数据盘", "summary_en": "Attach a data disk to an ECS instance", "create_time": "16/1/2025 16:50:22", "update_time": "16/1/2025 16:50:22", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/1/2025 16:50:22", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737017421628, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": "POST", "is_mock": 0}, {"opgw_capability_key": "CreateDisk218", "old_capability_key": null, "old_capability_id": "2501161650216709136", "status": "online", "name": "附加一个弹性网卡", "name_en": "Attach an Elastic Network Interface (ENI)", "summary": "附加一个弹性网卡", "summary_en": "Attach an Elastic Network Interface (ENI)", "create_time": "16/1/2025 16:50:22", "update_time": "16/1/2025 16:50:22", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/1/2025 16:50:22", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737017421670, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": "POST", "is_mock": 0}, {"opgw_capability_key": "CreateDisk219", "old_capability_key": null, "old_capability_id": "2501161650217139139", "status": "online", "name": "创建数据盘", "name_en": "Create one or more data disks", "summary": "创建一块或多块数据盘", "summary_en": "Create one or more data disks", "create_time": "16/1/2025 16:50:22", "update_time": "16/1/2025 16:50:22", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/1/2025 16:50:22", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737017421713, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": "POST", "is_mock": 0}, {"opgw_capability_key": "CreateDisk220", "old_capability_key": null, "old_capability_id": "2501161650217549142", "status": "online", "name": "删除云硬盘", "name_en": "Delete cloud disks", "summary": "删除云硬盘", "summary_en": "Delete cloud disks", "create_time": "16/1/2025 16:50:22", "update_time": "16/1/2025 16:50:22", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/1/2025 16:50:22", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737017421754, "capability_function": "delete", "max_delay": null, "capability_type": 1, "capability_label": null, "note": "DELETE", "is_mock": 0}, {"opgw_capability_key": "CreateDisk221", "old_capability_key": null, "old_capability_id": "2501161650217989145", "status": "online", "name": "查询存储", "name_en": "Query one or more block storages", "summary": "查询一块或多块块存储", "summary_en": "Query one or more block storages", "create_time": "16/1/2025 16:50:22", "update_time": "16/1/2025 16:50:22", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/1/2025 16:50:22", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737017421798, "capability_function": "query", "max_delay": null, "capability_type": 1, "capability_label": null, "note": "GET", "is_mock": 0}, {"opgw_capability_key": "CreateDisk222", "old_capability_key": null, "old_capability_id": "2501161650218449148", "status": "online", "name": "更新云硬盘信息", "name_en": "Update cloud disk information", "summary": "更新云硬盘信息", "summary_en": "Update cloud disk information", "create_time": "16/1/2025 16:50:22", "update_time": "16/1/2025 16:50:22", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/1/2025 16:50:22", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737017421844, "capability_function": "update", "max_delay": null, "capability_type": 1, "capability_label": null, "note": "PUT", "is_mock": 0}, {"opgw_capability_key": "CreateDisk223", "old_capability_key": null, "old_capability_id": "2501161650218869151", "status": "online", "name": "云硬盘恢复到某次快照", "name_en": "Restore a cloud disk to a specific snapshot", "summary": "云硬盘恢复到某次快照", "summary_en": "Restore a cloud disk to a specific snapshot", "create_time": "16/1/2025 16:50:22", "update_time": "16/1/2025 16:50:22", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/1/2025 16:50:22", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737017421886, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": "POST", "is_mock": 0}, {"opgw_capability_key": "CreateDisk224", "old_capability_key": null, "old_capability_id": "2501161650219299154", "status": "online", "name": "扩容云硬盘", "name_en": "Expand the capacity of a cloud disk", "summary": "扩容云硬盘", "summary_en": "Expand the capacity of a cloud disk", "create_time": "16/1/2025 16:50:22", "update_time": "16/1/2025 16:50:22", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/1/2025 16:50:22", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737017421929, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": "POST", "is_mock": 0}, {"opgw_capability_key": "CreateDisk225", "old_capability_key": null, "old_capability_id": "2501161650219729157", "status": "online", "name": "为ECS实例挂载数据盘", "name_en": "Attach a data disk to an ECS instance", "summary": "为一台ECS实例挂载一块数据盘", "summary_en": "Attach a data disk to an ECS instance", "create_time": "16/1/2025 16:50:22", "update_time": "16/1/2025 16:50:22", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/1/2025 16:50:22", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737017421972, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": "POST", "is_mock": 0}, {"opgw_capability_key": "CreateDisk226", "old_capability_key": null, "old_capability_id": "2501161650220139160", "status": "online", "name": "附加一个弹性网卡", "name_en": "Attach an Elastic Network Interface (ENI)", "summary": "附加一个弹性网卡", "summary_en": "Attach an Elastic Network Interface (ENI)", "create_time": "16/1/2025 16:50:22", "update_time": "16/1/2025 16:50:22", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/1/2025 16:50:22", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737017422013, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": "POST", "is_mock": 0}, {"opgw_capability_key": "CreateDisk299", "old_capability_key": null, "old_capability_id": "2501161711509109166", "status": "online", "name": "创建数据盘", "name_en": "Create one or more data disks", "summary": "创建一块或多块数据盘", "summary_en": "Create one or more data disks", "create_time": "16/1/2025 17:11:51", "update_time": "16/1/2025 17:11:51", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/1/2025 17:11:51", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737018710910, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": 101, "note": "POST", "is_mock": 0}, {"opgw_capability_key": "CreateDisk3", "old_capability_key": null, "old_capability_id": "2501161637390489010", "status": "online", "name": "查询存储", "name_en": "Query one or more block storages", "summary": "查询一块或多块块存储", "summary_en": "Query one or more block storages", "create_time": "16/1/2025 16:37:39", "update_time": "16/1/2025 16:37:39", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/1/2025 16:37:39", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737016659048, "capability_function": "query", "max_delay": null, "capability_type": 1, "capability_label": null, "note": "GET", "is_mock": 0}, {"opgw_capability_key": "CreateDisk300", "old_capability_key": null, "old_capability_id": "2501161711509539169", "status": "online", "name": "删除云硬盘", "name_en": "Delete cloud disks", "summary": "删除云硬盘", "summary_en": "Delete cloud disks", "create_time": "16/1/2025 17:11:51", "update_time": "16/1/2025 17:11:51", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/1/2025 17:11:51", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737018710953, "capability_function": "delete", "max_delay": null, "capability_type": 1, "capability_label": null, "note": "DELETE", "is_mock": 0}, {"opgw_capability_key": "CreateDisk302", "old_capability_key": null, "old_capability_id": "2501161711510169173", "status": "online", "name": "删除云硬盘", "name_en": "Delete cloud disks", "summary": "删除云硬盘", "summary_en": "Delete cloud disks", "create_time": "16/1/2025 17:11:51", "update_time": "16/1/2025 17:11:51", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/1/2025 17:11:51", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737018711016, "capability_function": "delete", "max_delay": null, "capability_type": 1, "capability_label": null, "note": "DELETE", "is_mock": 0}, {"opgw_capability_key": "CreateDisk4", "old_capability_key": null, "old_capability_id": "2501161637390999013", "status": "online", "name": "更新云硬盘信息", "name_en": "Update cloud disk information", "summary": "更新云硬盘信息", "summary_en": "Update cloud disk information", "create_time": "16/1/2025 16:37:39", "update_time": "16/1/2025 16:37:39", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/1/2025 16:37:39", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737016659099, "capability_function": "update", "max_delay": null, "capability_type": 1, "capability_label": null, "note": "PUT", "is_mock": 0}, {"opgw_capability_key": "CreateDisk5", "old_capability_key": null, "old_capability_id": "2501161638450699038", "status": "online", "name": "云硬盘恢复到某次快照", "name_en": "Restore a cloud disk to a specific snapshot", "summary": "云硬盘恢复到某次快照", "summary_en": "Restore a cloud disk to a specific snapshot", "create_time": "16/1/2025 16:38:45", "update_time": "16/1/2025 16:38:45", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/1/2025 16:38:45", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737016725069, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": "POST", "is_mock": 0}, {"opgw_capability_key": "CreateDisk6", "old_capability_key": null, "old_capability_id": "2501161638451069041", "status": "online", "name": "扩容云硬盘", "name_en": "Expand the capacity of a cloud disk", "summary": "扩容云硬盘", "summary_en": "Expand the capacity of a cloud disk", "create_time": "16/1/2025 16:38:45", "update_time": "16/1/2025 16:38:45", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/1/2025 16:38:45", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737016725106, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": "POST", "is_mock": 0}, {"opgw_capability_key": "CreateDisk7", "old_capability_key": null, "old_capability_id": "2501161638451479044", "status": "online", "name": "为ECS实例挂载数据盘", "name_en": "Attach a data disk to an ECS instance", "summary": "为一台ECS实例挂载一块数据盘", "summary_en": "Attach a data disk to an ECS instance", "create_time": "16/1/2025 16:38:45", "update_time": "16/1/2025 16:38:45", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/1/2025 16:38:45", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737016725147, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": "POST", "is_mock": 0}, {"opgw_capability_key": "CreateDisk8", "old_capability_key": null, "old_capability_id": "2501161638451879047", "status": "online", "name": "附加一个弹性网卡", "name_en": "Attach an Elastic Network Interface (ENI)", "summary": "附加一个弹性网卡", "summary_en": "Attach an Elastic Network Interface (ENI)", "create_time": "16/1/2025 16:38:45", "update_time": "16/1/2025 16:38:45", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/1/2025 16:38:45", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737016725187, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": "POST", "is_mock": 0}, {"opgw_capability_key": "CreateDisk9", "old_capability_key": null, "old_capability_id": "2501161638452289050", "status": "online", "name": "创建数据盘", "name_en": "Create one or more data disks", "summary": "创建一块或多块数据盘", "summary_en": "Create one or more data disks", "create_time": "16/1/2025 16:38:45", "update_time": "16/1/2025 16:38:45", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/1/2025 16:38:45", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737016725228, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": "POST", "is_mock": 0}, {"opgw_capability_key": "createFence", "old_capability_key": "OVYZ19", "old_capability_id": "2405241722419889195", "status": "online", "name": "司法矫正产品_围栏创建", "name_en": "createFence", "summary": "进行发送短信功能的能力", "summary_en": "createFence", "create_time": "27/5/2024 10:39:38", "update_time": "26/6/2024 11:34:30", "userid": 99, "username": "jiangsd3", "realname": "蒋少东", "orgid": 39, "org_name": "联通智网创新中心本部-网络数据室", "online_time": "27/5/2024 10:39:38", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1719372870190, "capability_function": "insert", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "createGeoFencing", "old_capability_key": null, "old_capability_id": "2412172248424589123", "status": "online", "name": "设备创建订阅", "name_en": "Device creation subscription", "summary": "为设备创建订阅，以便在设备进入或退出指定区域时接收通知", "summary_en": "Create a subscription for a device to receive notifications when the device enters or exits a specified area", "create_time": "17/12/2024 22:48:42", "update_time": "17/12/2024 22:48:42", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "17/12/2024 22:48:42", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1734446922458, "capability_function": "insert", "max_delay": 10, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 1}, {"opgw_capability_key": "CreateImage", "old_capability_key": null, "old_capability_id": "2501161536327929022", "status": "online", "name": "创建一份自定义镜像", "name_en": "Create a custom image", "summary": "创建一份自定义镜像", "summary_en": "Create a custom image", "create_time": "16/1/2025 15:36:33", "update_time": "16/1/2025 15:36:33", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/1/2025 15:36:33", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737012992792, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "createQodProvisioning", "old_capability_key": null, "old_capability_id": "2412172210045419037", "status": "online", "name": "创建QoD", "name_en": "Create a QoD", "summary": "在运营商中触发一个新的配置，将某个QoS Profile分配给某个设备。", "summary_en": "Triggers a new configuration in the carrier to assign a QoS Profile to a device.", "create_time": "17/12/2024 22:10:05", "update_time": "17/12/2024 22:10:05", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "17/12/2024 22:10:05", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1734444604542, "capability_function": "insert", "max_delay": 10, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 1}, {"opgw_capability_key": "createQosSessions", "old_capability_key": null, "old_capability_id": "2412172210049839084", "status": "online", "name": "创建QoS会话", "name_en": "Creating a QoS Session", "summary": "创建QoS会话来管理延迟/吞吐量优先级", "summary_en": "Create QoS sessions to manage latency/throughput priorities", "create_time": "17/12/2024 22:10:05", "update_time": "17/12/2024 22:10:05", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "17/12/2024 22:10:05", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1734444604984, "capability_function": "insert", "max_delay": 10, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 0}, {"opgw_capability_key": "CreateSecurityGroup", "old_capability_key": null, "old_capability_id": "2501161536329479037", "status": "online", "name": "调用CreateSecurityGroup新建一个安全组", "name_en": "Call CreateSecurityGroup to create a new security group", "summary": "调用CreateSecurityGroup新建一个安全组", "summary_en": "Call CreateSecurityGroup to create a new security group", "create_time": "16/1/2025 15:36:33", "update_time": "16/1/2025 15:36:33", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/1/2025 15:36:33", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737012992947, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "CreateSecurityGroupRule", "old_capability_key": null, "old_capability_id": "2501161536329759040", "status": "online", "name": "调用CreateSecurityGroupRule增加一条安全组规则。", "name_en": "Call CreateSecurityGroupRule to create a new Rule", "summary": "调用CreateSecurityGroupRule增加一条安全组规则。", "summary_en": "Call CreateSecurityGroupRule to create a new Rule", "create_time": "16/1/2025 15:36:33", "update_time": "16/1/2025 15:36:33", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/1/2025 15:36:33", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737012992975, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "CreateSnapshot", "old_capability_key": null, "old_capability_id": "2501161536326649010", "status": "online", "name": "创建云硬盘快照", "name_en": "Create a cloud disk snapshot", "summary": "创建云硬盘快照", "summary_en": "Create a cloud disk snapshot", "create_time": "16/1/2025 15:36:33", "update_time": "16/1/2025 15:36:33", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/1/2025 15:36:33", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737012992664, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "current1", "old_capability_key": null, "old_capability_id": "2501131844001259013", "status": "online", "name": "获取当前漫游状态和国家信息（手动新添加）", "name_en": "Get current roaming status and country information add manually", "summary": "获取当前漫游状态和国家信息-手动新添加", "summary_en": "Get current roaming status and country information - add manually", "create_time": "13/1/2025 18:44:00", "update_time": "13/1/2025 19:56:12", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "13/1/2025 18:44:00", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1736769371932, "capability_function": "query", "max_delay": null, "capability_type": 1, "capability_label": 101, "note": "进行备注", "is_mock": 1}, {"opgw_capability_key": "deleteApplicationProfiles", "old_capability_key": null, "old_capability_id": "2412172210047169058", "status": "online", "name": "删除网络检测模型", "name_en": "Delete an application", "summary": "删除", "summary_en": "delete", "create_time": "17/12/2024 22:10:05", "update_time": "17/12/2024 22:10:05", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "17/12/2024 22:10:05", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1734444604717, "capability_function": "delete", "max_delay": 10, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 1}, {"opgw_capability_key": "deleteCardTask", "old_capability_key": "6fxWd5", "old_capability_id": "2405241649483649171", "status": "online", "name": "企业名片任务删除", "name_en": "deleteCardTask", "summary": "进行发送短信功能的能力", "summary_en": "deleteCardTask", "create_time": "27/5/2024 10:39:37", "update_time": "26/6/2024 11:34:29", "userid": 99, "username": "jiangsd3", "realname": "蒋少东", "orgid": 39, "org_name": "联通智网创新中心本部-网络数据室", "online_time": "27/5/2024 10:39:37", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1719372869110, "capability_function": "delete", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "deleteConnectivityInsightsSubscriptions", "old_capability_key": null, "old_capability_id": "2412172248429539180", "status": "online", "name": "删除网络质量检测订阅", "name_en": "Delete a network quality check subscription", "summary": "删除网络质量检测订阅", "summary_en": "Delete a network quality check subscription", "create_time": "17/12/2024 22:48:43", "update_time": "17/12/2024 22:48:43", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "17/12/2024 22:48:43", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1734446922954, "capability_function": "delete", "max_delay": 10, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 1}, {"opgw_capability_key": "deleteDeviceRoamingSubscriptions", "old_capability_key": null, "old_capability_id": "2412172248427419156", "status": "online", "name": "删除设备漫游订阅", "name_en": "Delete a device roaming subscription", "summary": "删除设备漫游订阅", "summary_en": "Delete a device roaming subscription", "create_time": "17/12/2024 22:48:43", "update_time": "17/12/2024 22:48:43", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "17/12/2024 22:48:43", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1734446922741, "capability_function": "delete", "max_delay": 10, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 1}, {"opgw_capability_key": "deleteGeoFencing", "old_capability_key": null, "old_capability_id": "2412172248425399132", "status": "online", "name": "删除地理围栏订阅", "name_en": "Delete geofencing subscriptions", "summary": "删除给定的地理围栏订阅。", "summary_en": "Delete a given geofencing subscription.", "create_time": "17/12/2024 22:48:43", "update_time": "17/12/2024 22:48:43", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "17/12/2024 22:48:43", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1734446922540, "capability_function": "delete", "max_delay": 10, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 1}, {"opgw_capability_key": "DeleteImage", "old_capability_key": null, "old_capability_id": "2501161536328209025", "status": "online", "name": "删除自定义镜像", "name_en": "Delete custom image", "summary": "删除自定义镜像", "summary_en": "Delete custom image", "create_time": "16/1/2025 15:36:33", "update_time": "16/1/2025 15:36:33", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/1/2025 15:36:33", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737012992820, "capability_function": "delete", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "deleteMsisdnsCardTask", "old_capability_key": "p7iaEq", "old_capability_id": "2405241655357579179", "status": "online", "name": "企业名片任务号码删除接口", "name_en": "deleteMsisdnsCardTask", "summary": "进行发送短信功能的能力", "summary_en": "deleteMsisdnsCardTask", "create_time": "27/5/2024 10:39:37", "update_time": "26/6/2024 11:34:30", "userid": 99, "username": "jiangsd3", "realname": "蒋少东", "orgid": 39, "org_name": "联通智网创新中心本部-网络数据室", "online_time": "27/5/2024 10:39:37", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1719372869574, "capability_function": "delete", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "deletePerson", "old_capability_key": "STVv3o", "old_capability_id": "2405241709376069315", "status": "online", "name": "司法矫正产品_矫正人员删除", "name_en": "deletePerson", "summary": "进行发送短信功能的能力", "summary_en": "deletePerson", "create_time": "27/5/2024 10:39:38", "update_time": "26/6/2024 11:34:30", "userid": 99, "username": "jiangsd3", "realname": "蒋少东", "orgid": 39, "org_name": "联通智网创新中心本部-网络数据室", "online_time": "27/5/2024 10:39:38", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1719372869710, "capability_function": "delete", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "deleteQodProvisioning", "old_capability_key": null, "old_capability_id": "2412172210045929043", "status": "online", "name": "删除QoD", "name_en": "Deleting a QoD", "summary": "释放QoS发放相关资源。", "summary_en": "Release resources related to QoS provisioning.", "create_time": "17/12/2024 22:10:05", "update_time": "17/12/2024 22:10:05", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "17/12/2024 22:10:05", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1734444604593, "capability_function": "delete", "max_delay": 10, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 1}, {"opgw_capability_key": "deleteQOS", "old_capability_key": null, "old_capability_id": "2502261054295049069", "status": "online", "name": "QOD节电资源信息系统删除qos", "name_en": "deleteQOS", "summary": "删除qos", "summary_en": "deleteQOS", "create_time": "26/2/2025 10:54:30", "update_time": "19/3/2025 22:48:23", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "26/2/2025 10:54:30", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": *************, "capability_function": "delete", "max_delay": null, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 1}, {"opgw_capability_key": "deleteReachabilityStatusSubscriptions", "old_capability_key": null, "old_capability_id": "2412172248428439168", "status": "online", "name": "删除设备联网状态订阅", "name_en": "Delete a device networking subscription", "summary": "删除设备联网状态订阅", "summary_en": "Delete a device networking subscription", "create_time": "17/12/2024 22:48:43", "update_time": "17/12/2024 22:48:43", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "17/12/2024 22:48:43", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1734446922844, "capability_function": "delete", "max_delay": 10, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 1}, {"opgw_capability_key": "deleteRegion", "old_capability_key": "1aEbBB", "old_capability_id": "2405241631045469148", "status": "online", "name": "停用自定义区域", "name_en": "deleteRegion", "summary": "进行发送短信功能的能力", "summary_en": "deleteRegion", "create_time": "27/5/2024 10:39:36", "update_time": "26/6/2024 11:34:29", "userid": 99, "username": "jiangsd3", "realname": "蒋少东", "orgid": 39, "org_name": "联通智网创新中心本部-网络数据室", "online_time": "27/5/2024 10:39:36", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1719372868570, "capability_function": "update", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "DeleteSecurityGroup", "old_capability_key": null, "old_capability_id": "2501161536330019043", "status": "online", "name": "调用DeleteSecurityGroup删除一个安全组", "name_en": "Call DeleteSecurityGroup to delete a security group (repeated)", "summary": "调用DeleteSecurityGroup删除一个安全组", "summary_en": "Call DeleteSecurityGroup to delete a security group (repeated)", "create_time": "16/1/2025 15:36:33", "update_time": "16/1/2025 15:36:33", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/1/2025 15:36:33", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737012993001, "capability_function": "delete", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "DeleteSecurityGroupRule", "old_capability_key": null, "old_capability_id": "2501161536330339046", "status": "online", "name": "删除一条安全组规则", "name_en": "Delete a security group rule", "summary": "删除一条安全组规则", "summary_en": "Delete a security group rule", "create_time": "16/1/2025 15:36:33", "update_time": "16/1/2025 15:36:33", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/1/2025 15:36:33", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737012993033, "capability_function": "delete", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "deletesimSwapSubscriptions", "old_capability_key": null, "old_capability_id": "2412172248426409144", "status": "online", "name": "删除sim卡交换订阅", "name_en": "Delete a sim exchange subscription", "summary": "删除sim卡交换订阅", "summary_en": "Delete a sim exchange subscription", "create_time": "17/12/2024 22:48:43", "update_time": "17/12/2024 22:48:43", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "17/12/2024 22:48:43", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1734446922641, "capability_function": "delete", "max_delay": 10, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 1}, {"opgw_capability_key": "DeleteSnapshot", "old_capability_key": null, "old_capability_id": "2501161536326999013", "status": "online", "name": "删除快照", "name_en": "Delete snapshot", "summary": "删除快照", "summary_en": "Delete snapshot", "create_time": "16/1/2025 15:36:33", "update_time": "16/1/2025 15:36:33", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/1/2025 15:36:33", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737012992699, "capability_function": "delete", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "deleteSubscribe", "old_capability_key": "rg9RyM", "old_capability_id": "2405241611587669134", "status": "online", "name": "停用围栏事件订阅", "name_en": "deleteSubscribe", "summary": "进行发送短信功能的能力", "summary_en": "deleteSubscribe", "create_time": "27/5/2024 10:39:36", "update_time": "26/6/2024 11:34:28", "userid": 99, "username": "jiangsd3", "realname": "蒋少东", "orgid": 39, "org_name": "联通智网创新中心本部-网络数据室", "online_time": "27/5/2024 10:39:36", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1719372868081, "capability_function": "update", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "deletQosSessionId", "old_capability_key": null, "old_capability_id": "2412172210050529093", "status": "online", "name": "删除QoS会话", "name_en": "Deleting a QoS Session", "summary": "释放QoS会话相关资源", "summary_en": "Release resources related to QoS sessions", "create_time": "17/12/2024 22:10:05", "update_time": "17/12/2024 22:10:05", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "17/12/2024 22:10:05", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1734444605053, "capability_function": "delete", "max_delay": 10, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 0}, {"opgw_capability_key": "DescribeAvailabilityZone", "old_capability_key": null, "old_capability_id": "2501091811201459006", "status": "online", "name": "查询所有的可用区", "name_en": "DescribeAvailabilityZone", "summary": "查询所有的可用区", "summary_en": "DescribeAvailabilityZone", "create_time": "9/1/2025 18:11:20", "update_time": "9/1/2025 18:11:20", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "9/1/2025 18:11:20", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1736417480146, "capability_function": "query", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "DescribeImages", "old_capability_key": null, "old_capability_id": "2501161536328499028", "status": "online", "name": "查询可以使用的镜像资源", "name_en": "Query available image resources", "summary": "查询可以使用的镜像资源", "summary_en": "Query available image resources", "create_time": "16/1/2025 15:36:33", "update_time": "16/1/2025 15:36:33", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/1/2025 15:36:33", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737012992849, "capability_function": "query", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "DescribeInstances", "old_capability_key": null, "old_capability_id": "2501101623090809001", "status": "online", "name": "查询一台或多台ECS实例的详细信息", "name_en": "Example Query details about one or more ECS instances", "summary": "查询一台或多台ECS实例的详细信息", "summary_en": "Example Query details about one or more ECS instances", "create_time": "10/1/2025 16:23:09", "update_time": "10/1/2025 16:23:09", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "10/1/2025 16:23:09", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1736497389082, "capability_function": "query", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "DescribeInstanceTypeFamilies", "old_capability_key": null, "old_capability_id": "2501161536325849004", "status": "online", "name": "查询云主机规格组", "name_en": "Query Cloud Host Specification Group", "summary": "查询云主机规格组", "summary_en": "Query Cloud Host Specification Group", "create_time": "16/1/2025 15:36:33", "update_time": "16/1/2025 15:36:33", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/1/2025 15:36:33", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737012992587, "capability_function": "query", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "DescribeInstanceTypes", "old_capability_key": null, "old_capability_id": "2501161536326209007", "status": "online", "name": "查询云服务器ECS提供的所有云主机规格的信息", "name_en": "Query information about all cloud host specifications provided by ECS (Elastic Compute Service)", "summary": "查询云服务器ECS提供的所有云主机规格的信息", "summary_en": "Query information about all cloud host specifications provided by ECS (Elastic Compute Service)", "create_time": "16/1/2025 15:36:33", "update_time": "16/1/2025 15:36:33", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/1/2025 15:36:33", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737012992620, "capability_function": "query", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "DescribeSecurityGroupRules", "old_capability_key": null, "old_capability_id": "2501161536330839049", "status": "online", "name": "调用DescribeSecurityGroupRules查询安全组的规则列表", "name_en": "Call DescribeSecurityGroupRules to query the list of rules for a security group", "summary": "调用DescribeSecurityGroupRules查询安全组的规则列表", "summary_en": "Call DescribeSecurityGroupRules to query the list of rules for a security group", "create_time": "16/1/2025 15:36:33", "update_time": "16/1/2025 15:36:33", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/1/2025 15:36:33", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737012993083, "capability_function": "query", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "DescribeSecurityGroups", "old_capability_key": null, "old_capability_id": "2501161536331199052", "status": "online", "name": "调用DescribeSecurityGroups查询您创建的安全组的基本信息", "name_en": "Call DescribeSecurityGroups to query basic information about the security groups you have created", "summary": "调用DescribeSecurityGroups查询您创建的安全组的基本信息", "summary_en": "Call DescribeSecurityGroups to query basic information about the security groups you have created", "create_time": "16/1/2025 15:36:33", "update_time": "16/1/2025 15:36:33", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/1/2025 15:36:33", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737012993119, "capability_function": "query", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "DescribeSnapshots", "old_capability_key": null, "old_capability_id": "2501161536327349016", "status": "online", "name": "查看云硬盘快照", "name_en": "View cloud disk snapshots", "summary": "查看云硬盘快照", "summary_en": "View cloud disk snapshots", "create_time": "16/1/2025 15:36:33", "update_time": "16/1/2025 15:36:33", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/1/2025 15:36:33", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737012992734, "capability_function": "query", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "deviceRoamingSubscriptions", "old_capability_key": null, "old_capability_id": "2412172248426719147", "status": "online", "name": "添加设备漫游订阅", "name_en": "Add a device roaming subscription", "summary": "添加设备漫游订阅", "summary_en": "Add a device roaming subscription", "create_time": "17/12/2024 22:48:43", "update_time": "17/12/2024 22:48:43", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "17/12/2024 22:48:43", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1734446922671, "capability_function": "insert", "max_delay": 10, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 1}, {"opgw_capability_key": "deviceRoamingSubscriptionslist", "old_capability_key": null, "old_capability_id": "2412172248426979150", "status": "online", "name": "检索设备漫游订阅", "name_en": "Retrieve device roaming subscriptions", "summary": "检索设备漫游订阅", "summary_en": "Retrieve device roaming subscriptions", "create_time": "17/12/2024 22:48:43", "update_time": "17/12/2024 22:48:43", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "17/12/2024 22:48:43", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1734446922698, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 1}, {"opgw_capability_key": "enableCustomArea", "old_capability_key": "vae<PERSON>e", "old_capability_id": "2405241634239349298", "status": "online", "name": "启用自定义区域", "name_en": "enableCustomArea", "summary": "进行发送短信功能的能力", "summary_en": "enableCustomArea", "create_time": "27/5/2024 10:39:36", "update_time": "26/6/2024 11:34:29", "userid": 99, "username": "jiangsd3", "realname": "蒋少东", "orgid": 39, "org_name": "联通智网创新中心本部-网络数据室", "online_time": "27/5/2024 10:39:36", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1719372868707, "capability_function": "update", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "enableSubscribe", "old_capability_key": "0xlMQL", "old_capability_id": "2405241635315989156", "status": "online", "name": "启用围栏事件订阅", "name_en": "enableSubscribe", "summary": "进行发送短信功能的能力", "summary_en": "enableSubscribe", "create_time": "27/5/2024 10:39:37", "update_time": "26/6/2024 11:34:29", "userid": 99, "username": "jiangsd3", "realname": "蒋少东", "orgid": 39, "org_name": "联通智网创新中心本部-网络数据室", "online_time": "27/5/2024 10:39:37", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1719372868772, "capability_function": "update", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "extendQosSessionDuration", "old_capability_key": null, "old_capability_id": "2412172210050759096", "status": "online", "name": "延长活动QoS会话的总会话持续时间", "name_en": "Extend the general session duration of an active QoS session", "summary": "延长活动QoS会话的总会话持续时间（使用qosStatus = AVAILABLE）。", "summary_en": "Extend the general session duration of an active QoS session (using qosStatus = AVAILABLE).", "create_time": "17/12/2024 22:10:05", "update_time": "17/12/2024 22:10:05", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "17/12/2024 22:10:05", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1734444605075, "capability_function": "update", "max_delay": 10, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 0}, {"opgw_capability_key": "fenceInfo", "old_capability_key": "jof9RT", "old_capability_id": "2405241724462979336", "status": "online", "name": "司法矫正产品_围栏信息查看", "name_en": "fenceInfo", "summary": "进行发送短信功能的能力", "summary_en": "fenceInfo", "create_time": "27/5/2024 10:39:38", "update_time": "26/6/2024 11:34:30", "userid": 99, "username": "jiangsd3", "realname": "蒋少东", "orgid": 39, "org_name": "联通智网创新中心本部-网络数据室", "online_time": "27/5/2024 10:39:38", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1719372870258, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "getAllProfiles", "old_capability_key": null, "old_capability_id": "2412172210049379078", "status": "online", "name": "检索QOS配置文件列表", "name_en": "Retrieve the QOS configuration file list", "summary": "返回全部QOS配置文件1", "summary_en": "All QOS configuration files are displayed", "create_time": "17/12/2024 22:10:05", "update_time": "28/3/2025 16:29:08", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "17/12/2024 22:10:05", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1743150548345, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 1}, {"opgw_capability_key": "getApplicationProfiles", "old_capability_key": null, "old_capability_id": "2412172210046929055", "status": "online", "name": "查询网络检测模型", "name_en": "Read the application summary", "summary": "阅读应用程序概要", "summary_en": "Read the application summary", "create_time": "17/12/2024 22:10:05", "update_time": "17/12/2024 22:10:05", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "17/12/2024 22:10:05", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1734444604693, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 1}, {"opgw_capability_key": "getBCFStatus", "old_capability_key": "MnVOtv", "old_capability_id": "2405241550404229119", "status": "online", "name": "遇忙呼叫前转业务查询", "name_en": "getBCFStatus", "summary": "进行发送短信功能的能力", "summary_en": "getBCFStatus", "create_time": "27/5/2024 10:39:35", "update_time": "26/6/2024 11:34:28", "userid": 98, "username": "huzy39", "realname": "胡泽妍", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "27/5/2024 10:39:35", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1719372867797, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "getCallForwarding", "old_capability_key": null, "old_capability_id": "2505151626098959186", "status": "online", "name": "活动呼转查询", "name_en": "Retrieve the information", "summary": "此端点提供有关无条件呼叫转移状态的信息，无论是否处于活动状态。", "summary_en": "This endpoint provides information about the status of the unconditional call forwarding, beeing active or not", "create_time": "15/5/2025 16:26:10", "update_time": "11/6/2025 15:19:20", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "15/5/2025 16:26:10", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1749626359789, "capability_function": "query", "max_delay": null, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 0}, {"opgw_capability_key": "getCallForwardings", "old_capability_key": null, "old_capability_id": "2412172210051819105", "status": "online", "name": "无条件呼转查询", "name_en": "Retrieve the information", "summary": "此端点提供有关无条件呼叫转移状态的信息，无论是否处于活动状态。", "summary_en": "This endpoint provides information about the status of the unconditional call forwarding, beeing active or not", "create_time": "17/12/2024 22:10:05", "update_time": "17/12/2024 22:10:05", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "17/12/2024 22:10:05", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1734444605182, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 1}, {"opgw_capability_key": "getCellLocation", "old_capability_key": "WZKcoF", "old_capability_id": "2405241450024689091", "status": "online", "name": "用户实时基站位置定位查询", "name_en": "getCellLocation", "summary": "进行发送短信功能的能力", "summary_en": "getCellLocation", "create_time": "27/5/2024 10:39:35", "update_time": "26/6/2024 11:34:30", "userid": 99, "username": "jiangsd3", "realname": "蒋少东", "orgid": 39, "org_name": "联通智网创新中心本部-网络数据室", "online_time": "27/5/2024 10:39:35", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1719372870453, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "getCFDetails", "old_capability_key": "67dFvv", "old_capability_id": "2405241458319659247", "status": "online", "name": "呼叫前转业务查询", "name_en": "getCFDetails", "summary": "进行发送短信功能的能力", "summary_en": "getCFDetails", "create_time": "27/5/2024 10:39:37", "update_time": "26/6/2024 11:34:31", "userid": 98, "username": "huzy39", "realname": "胡泽妍", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "27/5/2024 10:39:37", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1719372870648, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "getCFInfo", "old_capability_key": "fJLMuX", "old_capability_id": "2405241602306689282", "status": "online", "name": "呼叫前转业务查询", "name_en": "getCFInfo", "summary": "进行发送短信功能的能力", "summary_en": "getCFInfo", "create_time": "27/5/2024 10:39:37", "update_time": "26/6/2024 11:34:28", "userid": 98, "username": "huzy39", "realname": "胡泽妍", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "27/5/2024 10:39:37", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1719372867938, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "getCFSetting", "old_capability_key": "BRo4H7", "old_capability_id": "2405241645282989163", "status": "online", "name": "呼叫前转业务查询", "name_en": "getCFSetting", "summary": "进行发送短信功能的能力", "summary_en": "getCFSetting", "create_time": "27/5/2024 10:39:36", "update_time": "26/6/2024 11:34:29", "userid": 98, "username": "huzy39", "realname": "胡泽妍", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "27/5/2024 10:39:36", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1719372868978, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "getCircuitRouteInfo", "old_capability_key": null, "old_capability_id": "2503211833520949610", "status": "online", "name": "大客户网管专线网络拓扑查询", "name_en": "Bulk Customer Network Management Dedicated Line Network Topology Query", "summary": "大客户网管专线网络拓扑查询。", "summary_en": "Bulk Customer Network Management Dedicated Line Network Topology Query", "create_time": "21/3/2025 18:33:52", "update_time": "25/3/2025 16:07:21", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "21/3/2025 18:33:52", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1742890041212, "capability_function": "query", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "getConnectivityInsightsSubscriptions", "old_capability_key": null, "old_capability_id": "2412172248429319177", "status": "online", "name": "获取网络质量检测订阅", "name_en": "Get a network quality inspection subscription", "summary": "获取网络质量检测订阅", "summary_en": "Get a network quality inspection subscription", "create_time": "17/12/2024 22:48:43", "update_time": "17/12/2024 22:48:43", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "17/12/2024 22:48:43", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1734446922931, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 1}, {"opgw_capability_key": "getDeviceCount", "old_capability_key": null, "old_capability_id": "2412171932106579001", "status": "online", "name": "统计设备数量", "name_en": "Counting equipment quantity", "summary": "统计设备数量", "summary_en": "Counting equipment quantity", "create_time": "17/12/2024 19:32:11", "update_time": "17/12/2024 19:32:11", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "17/12/2024 19:32:11", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1734435779849, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 1}, {"opgw_capability_key": "getDeviceRoamingSubscriptions", "old_capability_key": null, "old_capability_id": "2412172248427209153", "status": "online", "name": "获取指定设备漫游订阅", "name_en": "Gets a roaming subscription for the specified device", "summary": "获取指定设备漫游订阅", "summary_en": "Gets a roaming subscription for the specified device", "create_time": "17/12/2024 22:48:43", "update_time": "17/12/2024 22:48:43", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "17/12/2024 22:48:43", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1734446922720, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 1}, {"opgw_capability_key": "getDeviceType", "old_capability_key": null, "old_capability_id": "2412161900195589001", "status": "online", "name": "用户设备信息查询", "name_en": "Get details about the type of device being used by a given mobile subscriber", "summary": "获取给定移动用户使用的设备类型的详细信息", "summary_en": "Get details about the type of device being used by a given mobile subscriber", "create_time": "16/12/2024 19:00:20", "update_time": "16/12/2024 19:00:20", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/12/2024 19:00:20", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1734346819568, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 1}, {"opgw_capability_key": "getDomesticCircuitIn", "old_capability_key": null, "old_capability_id": "2502281413517939124", "status": "online", "name": "大客户网管国内电路割接单详情查询", "name_en": "Major Customer Network Management Domestic Circuit Cutover Order Details Inquiry", "summary": "该接口用于查询国内电路详情，通过 POST 请求传递割接影响电路的相关参数，返回包含割接单详情和影响电路清单的结果", "summary_en": "This interface is used to query the details of domestic circuits. It accepts relevant parameters of cutover-affected circuits via a POST request and returns the results containing cutover order details and the list of affected circuits.", "create_time": "28/2/2025 14:13:52", "update_time": "25/3/2025 16:05:42", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "28/2/2025 14:13:52", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1742889942357, "capability_function": "query", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "getDomesticInterCuto", "old_capability_key": null, "old_capability_id": "2502281406099939118", "status": "online", "name": "大客户网管割接单列表查询", "name_en": "Major Customer Network Management Cutover Order List Inquiry", "summary": "该接口用于查询国内国际割接单列表，通过 POST 请求传递相关查询参数，返回分页后的割接单列表信息。", "summary_en": "This interface is used to query the domestic and international cutover order list. The relevant query parameters are passed via a POST request, and it returns a paginated list of cutover order information.", "create_time": "28/2/2025 14:06:10", "update_time": "25/3/2025 16:06:04", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "28/2/2025 14:06:10", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1742889963748, "capability_function": "query", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "getDscp", "old_capability_key": "Q5Hd8V", "old_capability_id": "2405101757208058798", "status": "online", "name": "RequestToSendAnOTPcode（请求发送OTP验证码）", "name_en": "getDscp", "summary": "Request to send an OTP code（请求发送OTP验证码）", "summary_en": "getDscp", "create_time": "17/5/2024 14:23:34", "update_time": "28/6/2024 18:07:51", "userid": 13, "username": "yangf94", "realname": "杨帆", "orgid": 7, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "17/5/2024 14:23:34", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1719569271293, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "getFencePushLog", "old_capability_key": "dThK2f", "old_capability_id": "2405241638576859302", "status": "online", "name": "查询围栏事件推送记录", "name_en": "getFencePushLog", "summary": "进行发送短信功能的能力", "summary_en": "getFencePushLog", "create_time": "27/5/2024 10:39:37", "update_time": "26/6/2024 11:34:29", "userid": 99, "username": "jiangsd3", "realname": "蒋少东", "orgid": 39, "org_name": "联通智网创新中心本部-网络数据室", "online_time": "27/5/2024 10:39:37", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1719372868910, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "getFenceTask", "old_capability_key": "bqu3n3", "old_capability_id": "2405241620330519288", "status": "online", "name": "查询围栏事件信息", "name_en": "getFenceTask", "summary": "进行发送短信功能的能力", "summary_en": "getFenceTask", "create_time": "27/5/2024 10:39:36", "update_time": "26/6/2024 11:34:28", "userid": 99, "username": "jiangsd3", "realname": "蒋少东", "orgid": 39, "org_name": "联通智网创新中心本部-网络数据室", "online_time": "27/5/2024 10:39:36", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1719372868150, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "getGeoFencing", "old_capability_key": null, "old_capability_id": "2412172248425179129", "status": "online", "name": "检索给定订阅ID的地理围栏订阅信息", "name_en": "Retrieves geofenced subscription information for a given subscription ID", "summary": "检索给定订阅ID的地理围栏订阅信息。", "summary_en": "Retrieves geofenced subscription information for a given subscription ID.", "create_time": "17/12/2024 22:48:43", "update_time": "17/12/2024 22:48:43", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "17/12/2024 22:48:43", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1734446922518, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 1}, {"opgw_capability_key": "getGridUserCntByArea", "old_capability_key": "B7RXWf", "old_capability_id": "2405241535322539274", "status": "online", "name": "统计区域内各栅格用户数", "name_en": "getGridUserCntByArea", "summary": "进行发送短信功能的能力", "summary_en": "getGridUserCntByArea", "create_time": "27/5/2024 10:39:35", "update_time": "26/6/2024 11:34:28", "userid": 99, "username": "jiangsd3", "realname": "蒋少东", "orgid": 39, "org_name": "联通智网创新中心本部-网络数据室", "online_time": "27/5/2024 10:39:35", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1719372867657, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "getHighSpeedRealtimeC", "old_capability_key": null, "old_capability_id": "2412161038197989026", "status": "online", "name": "高速实时人流分析", "name_en": "High Speed Real time Crowd Flow Analysis", "summary": "根据省份、高速名称查询高速路段实时人流信息", "summary_en": "Query real-time pedestrian flow information on highway sections based on province and highway name", "create_time": "16/12/2024 10:38:20", "update_time": "16/12/2024 10:38:20", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/12/2024 10:38:20", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1734316699799, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "getInterCircuitInfoB", "old_capability_key": null, "old_capability_id": "2502281408040829121", "status": "online", "name": "大客户网管国际电路割接单详情查询", "name_en": "Major Customer Network Management International Circuit Cutover Order Details Inquiry", "summary": "该接口用于查询国际电路详情，通过 POST 请求传递国际割接电路清单的相关参数，返回包含国际割接影响电路清单和割接单信息的结果", "summary_en": "This interface is used to query the details of international circuits. It accepts relevant parameters of the international cutover circuit list via a POST request and returns the results containing the list of affected international cutover circuits and cutover order information.", "create_time": "28/2/2025 14:08:04", "update_time": "25/3/2025 16:05:53", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "28/2/2025 14:08:04", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1742889953197, "capability_function": "query", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "getLocationFeatureQue", "old_capability_key": null, "old_capability_id": "2412131134548209004", "status": "online", "name": "位置特征查询", "name_en": "Location Feature Query", "summary": "查询用户当前运动状态。", "summary_en": "Query the current exercise status of the user", "create_time": "13/12/2024 11:34:55", "update_time": "13/12/2024 11:34:55", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "13/12/2024 11:34:55", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1734060894851, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "getMobilePhone", "old_capability_key": null, "old_capability_id": "2505151606424229054", "status": "online", "name": "获取用户电话号码", "name_en": "Get the user's phone number", "summary": "返回电话号码，以便API客户端可以自己验证号码：", "summary_en": "Return the phone number so that the API client can verify the number itself:", "create_time": "15/5/2025 16:06:42", "update_time": "15/5/2025 16:06:42", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "15/5/2025 16:06:42", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1747296402422, "capability_function": "query", "max_delay": null, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 0}, {"opgw_capability_key": "getNCFStatus", "old_capability_key": "bdmaMN", "old_capability_id": "2405241556554679122", "status": "online", "name": "无应答呼叫前转业务查询", "name_en": "getNCFStatus", "summary": "进行发送短信功能的能力", "summary_en": "getNCFStatus", "create_time": "27/5/2024 10:39:35", "update_time": "26/6/2024 11:34:28", "userid": 98, "username": "huzy39", "realname": "胡泽妍", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "27/5/2024 10:39:35", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1719372867870, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "getNoAnswerCF", "old_capability_key": "AVq5Uw", "old_capability_id": "2405241647493389167", "status": "online", "name": "无应答呼叫前转业务查询", "name_en": "getNoAnswerCF", "summary": "进行发送短信功能的能力", "summary_en": "getNoAnswerCF", "create_time": "27/5/2024 10:39:37", "update_time": "26/6/2024 11:34:29", "userid": 98, "username": "huzy39", "realname": "胡泽妍", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "27/5/2024 10:39:37", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1719372869047, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "getNumbercarrierattri", "old_capability_key": null, "old_capability_id": "2412172210052369111", "status": "online", "name": "号码运营商归属查询", "name_en": "Number Carrier Attribution Inquiry", "summary": "通过输入手机号查询号码归属的运营商，携号转网号码返回当前的运营商", "summary_en": "Query the carrier of a phone number by entering the phone number, and for numbers that have undergone number portability, return the current carrier.", "create_time": "17/12/2024 22:10:05", "update_time": "17/12/2024 22:10:05", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "17/12/2024 22:10:05", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1734444605237, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "getNumberRiskIdentifi", "old_capability_key": null, "old_capability_id": "2412161038197039020", "status": "online", "name": "号码风险识别", "name_en": "Number Risk Identification", "summary": "通过输入手机号与身份证号查询手机号是否存在风险", "summary_en": "Query whether a phone number has any risks by entering the phone number and ID card number.", "create_time": "16/12/2024 10:38:20", "update_time": "16/12/2024 10:38:20", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/12/2024 10:38:20", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1734316699704, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "getOpenDetailCrossPr", "old_capability_key": null, "old_capability_id": "2502281355032769109", "status": "online", "name": "大客户网管一干开通详情查询", "name_en": "Major Customer Network Management Activation Details Inquiry", "summary": "一干电路开通详情接口。", "summary_en": "Trunk Circuit Activation Details Interface", "create_time": "28/2/2025 13:55:03", "update_time": "25/3/2025 16:06:38", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "28/2/2025 13:55:03", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1742889997841, "capability_function": "query", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "getOpenDetailInProvi", "old_capability_key": null, "old_capability_id": "2502281359355599115", "status": "online", "name": "大客户网管二干本地开通详情查询", "name_en": "Major Customer Network Management Secondary Trunk Local Activation Details Inquiry", "summary": "getOpenDetailInProvi.", "summary_en": "Secondary Trunk Circuit Activation Details Interface", "create_time": "28/2/2025 13:59:36", "update_time": "25/3/2025 16:06:15", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "28/2/2025 13:59:36", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1742889975461, "capability_function": "query", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "getOpenList", "old_capability_key": null, "old_capability_id": "2502281357164119112", "status": "online", "name": "大客户网管开通单列表查询", "name_en": "Bulk Customer Network Management Activation List Query", "summary": "服务可视一干二干本地混合开通单列表查询。", "summary_en": "Primary and Secondary Trunk Local Hybrid Activation List Query", "create_time": "28/2/2025 13:57:16", "update_time": "25/3/2025 16:06:27", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "28/2/2025 13:57:16", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1742889986668, "capability_function": "query", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "getPhoneNumber", "old_capability_key": null, "old_capability_id": "2412172210043199013", "status": "online", "name": "获取用户电话号码", "name_en": "Get the user's phone number", "summary": "返回电话号码，以便API客户端可以自己验证号码：", "summary_en": "Return the phone number so that the API client can verify the number itself:", "create_time": "17/12/2024 22:10:04", "update_time": "17/12/2024 22:10:04", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "17/12/2024 22:10:04", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1734444604320, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 1}, {"opgw_capability_key": "getProfilesByName", "old_capability_key": null, "old_capability_id": "2412172210049609081", "status": "online", "name": "检索匹配的QOS配置文件", "name_en": "Search the matching QOS configuration file", "summary": "返回与给定名称匹配的QOS配置文件。", "summary_en": "Returns a QOS profile matching the given name.", "create_time": "17/12/2024 22:10:05", "update_time": "17/12/2024 22:10:05", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "17/12/2024 22:10:05", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1734444604960, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 1}, {"opgw_capability_key": "getQodProvisioning", "old_capability_key": null, "old_capability_id": "2412172210045659040", "status": "online", "name": "查询QoD", "name_en": "Querying QoD", "summary": "查询QoD发放资源的详细信息", "summary_en": "Query details about QoD provisioning resources", "create_time": "17/12/2024 22:10:05", "update_time": "17/12/2024 22:10:05", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "17/12/2024 22:10:05", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1734444604566, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 1}, {"opgw_capability_key": "getQosSessions", "old_capability_key": null, "old_capability_id": "2412172210050299090", "status": "online", "name": "查询QoS会话的详细信息", "name_en": "Example Query details about QoS sessions", "summary": "查询QoS会话资源的详细信息", "summary_en": "Example Query details about QoS session resources", "create_time": "17/12/2024 22:10:05", "update_time": "17/12/2024 22:10:05", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "17/12/2024 22:10:05", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1734444605030, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 0}, {"opgw_capability_key": "getReachabilityStatusSubscriptions", "old_capability_key": null, "old_capability_id": "2412172248428219165", "status": "online", "name": "获取设备联网状态订阅", "name_en": "Get device networking status subscription", "summary": "获取设备联网状态订阅", "summary_en": "Get device networking status subscription", "create_time": "17/12/2024 22:48:43", "update_time": "17/12/2024 22:48:43", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "17/12/2024 22:48:43", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1734446922822, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 1}, {"opgw_capability_key": "getRegionalUserCount", "old_capability_key": null, "old_capability_id": "2505151620167469141", "status": "online", "name": "区域用户数查询", "name_en": "getRegionalUserCounts", "summary": "查询指定区域内的各小区实时用户数、小区总数及实时用户总数。", "summary_en": "getRegionalUserCounts", "create_time": "15/5/2025 16:20:17", "update_time": "15/5/2025 16:20:17", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "15/5/2025 16:20:17", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1747297216746, "capability_function": "query", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "getRegionalUserCounts", "old_capability_key": "YEinHG", "old_capability_id": "2405241533505689266", "status": "online", "name": "区域用户数查询", "name_en": "getRegionalUserCounts", "summary": "进行发送短信功能的能力", "summary_en": "getRegionalUserCounts", "create_time": "27/5/2024 10:39:35", "update_time": "26/6/2024 11:34:28", "userid": 99, "username": "jiangsd3", "realname": "蒋少东", "orgid": 39, "org_name": "联通智网创新中心本部-网络数据室", "online_time": "27/5/2024 10:39:35", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1719372868360, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "getScalperdeterminati", "old_capability_key": null, "old_capability_id": "2412161038197399023", "status": "online", "name": "羊毛党判定", "name_en": "Scalper Determination", "summary": "通过输入手机号查询该羊毛党风险等级，支持三网手机号。", "summary_en": "body the risk level of the wool party by entering a mobile phone number, supporting three network mobile phone numbers", "create_time": "16/12/2024 10:38:20", "update_time": "16/12/2024 10:38:20", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/12/2024 10:38:20", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1734316699739, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "getSimSwapSubscriptions", "old_capability_key": null, "old_capability_id": "2412172248426179141", "status": "online", "name": "获取指定sim卡交换订阅", "name_en": "Gets the specified sim exchange subscription", "summary": "获取指定sim卡交换订阅", "summary_en": "Gets the specified sim exchange subscription", "create_time": "17/12/2024 22:48:43", "update_time": "17/12/2024 22:48:43", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "17/12/2024 22:48:43", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1734446922617, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 1}, {"opgw_capability_key": "getSNumbercarrierattri", "old_capability_key": null, "old_capability_id": "2412161038196699017", "status": "online", "name": "号码运营商归属查询", "name_en": "Number Carrier Attribution Inquiry", "summary": "通过输入手机号查询号码归属的运营商，携号转网号码返回当前的运营商", "summary_en": "Query the carrier of a phone number by entering the phone number, and for numbers that have undergone number portability, return the current carrier.", "create_time": "16/12/2024 10:38:20", "update_time": "16/12/2024 10:38:20", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/12/2024 10:38:20", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1734316699670, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "getUCFStatus", "old_capability_key": "AEI58j", "old_capability_id": "2405241548369149116", "status": "online", "name": "无条件呼叫前转业务查询", "name_en": "getUCFStatus", "summary": "进行发送短信功能的能力", "summary_en": "getUCFStatus", "create_time": "27/5/2024 10:39:35", "update_time": "26/6/2024 11:34:28", "userid": 98, "username": "huzy39", "realname": "胡泽妍", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "27/5/2024 10:39:35", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1719372867724, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "getURCFStatus", "old_capability_key": "zaQ5sJ", "old_capability_id": "2405241600551459125", "status": "online", "name": "不可及呼叫前转业务查询", "name_en": "getURCFStatus", "summary": "进行发送短信功能的能力", "summary_en": "getURCFStatus", "create_time": "27/5/2024 10:39:36", "update_time": "26/6/2024 11:34:29", "userid": 98, "username": "huzy39", "realname": "胡泽妍", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "27/5/2024 10:39:36", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1719372868641, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "getUserByArea", "old_capability_key": "HJAaeA", "old_capability_id": "2405242042271559446", "status": "online", "name": "区域实时用户查询", "name_en": "getUserByArea", "summary": "进行发送短信功能的能力", "summary_en": "getUserByArea", "create_time": "27/5/2024 10:39:35", "update_time": "26/6/2024 11:34:31", "userid": 99, "username": "jiangsd3", "realname": "蒋少东", "orgid": 39, "org_name": "联通智网创新中心本部-网络数据室", "online_time": "27/5/2024 10:39:35", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1719372870778, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "getUserByCell", "old_capability_key": "JNOrad", "old_capability_id": "2405241444183039085", "status": "online", "name": "基站实时用户查询", "name_en": "getUserByCell", "summary": "进行发送短信功能的能力", "summary_en": "getUserByCell", "create_time": "27/5/2024 10:39:34", "update_time": "26/6/2024 11:34:31", "userid": 99, "username": "jiangsd3", "realname": "蒋少东", "orgid": 39, "org_name": "联通智网创新中心本部-网络数据室", "online_time": "27/5/2024 10:39:34", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1719372870712, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "getUserCntByCells", "old_capability_key": "OejJ0s", "old_capability_id": "2405241400544279081", "status": "online", "name": "基站(批量)实时用户数查询", "name_en": "getUserCntByCells", "summary": "进行发送短信功能的能力", "summary_en": "getUserCntByCells", "create_time": "27/5/2024 10:39:34", "update_time": "26/6/2024 11:34:30", "userid": 99, "username": "jiangsd3", "realname": "蒋少东", "orgid": 39, "org_name": "联通智网创新中心本部-网络数据室", "online_time": "27/5/2024 10:39:34", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": "3/6/2024 17:31:37", "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1719372870325, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "getUserCountByArea", "old_capability_key": "rsMvDx", "old_capability_id": "2405241454126599094", "status": "online", "name": "区域实时用户数查询", "name_en": "getUserCountByArea", "summary": "进行发送短信功能的能力", "summary_en": "getUserCountByArea", "create_time": "27/5/2024 10:39:35", "update_time": "26/6/2024 11:34:31", "userid": 99, "username": "jiangsd3", "realname": "蒋少东", "orgid": 39, "org_name": "联通智网创新中心本部-网络数据室", "online_time": "27/5/2024 10:39:35", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1719372870520, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "getUserDeviceType", "old_capability_key": null, "old_capability_id": "2506101445263399532", "status": "online", "name": "DevicIdentifier(获取用户设备类型)", "name_en": "getUserDeviceType", "summary": "DevicIdentifier(获取用户设备类型)", "summary_en": "getUserDeviceType", "create_time": "10/6/2025 14:45:26", "update_time": "10/6/2025 14:45:26", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "10/6/2025 14:45:26", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1749537926339, "capability_function": "query", "max_delay": null, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 0}, {"opgw_capability_key": "getUserIMEI", "old_capability_key": null, "old_capability_id": "2506101447227609535", "status": "online", "name": "DevicIdentifier(获取用户设备详细信息）", "name_en": "getUserIMEI", "summary": "Device Identifier(用户设备信息查询）", "summary_en": "getUserIMEI", "create_time": "10/6/2025 14:47:23", "update_time": "10/6/2025 14:47:23", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "10/6/2025 14:47:23", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1749538042760, "capability_function": "query", "max_delay": null, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 0}, {"opgw_capability_key": "getUserLocationGH", "old_capability_key": null, "old_capability_id": "2506131632595869032", "status": "online", "name": "查询用户实时位置的GeoHash值", "name_en": "getUserLocationGH", "summary": "通过区域轮廓验证用户实时位置及返回最后定位时间", "summary_en": "getUserLocationGH", "create_time": "13/6/2025 16:33:00", "update_time": "13/6/2025 16:33:00", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "13/6/2025 16:33:00", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1749803579621, "capability_function": "query", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "getUserLocationHash", "old_capability_key": null, "old_capability_id": "2506131728077879118", "status": "online", "name": "查询用户实时位置的GeoHash值新", "name_en": "getUserLocationGH", "summary": "查询用户实时位置GeoHash与最后定位时间。", "summary_en": "getUserLocationGH", "create_time": "13/6/2025 17:28:08", "update_time": "13/6/2025 17:28:08", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "13/6/2025 17:28:08", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1749806887788, "capability_function": "query", "max_delay": null, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 0}, {"opgw_capability_key": "getUserStatus", "old_capability_key": "0TIF10", "old_capability_id": "2405241455399779098", "status": "online", "name": "实时用户状态查询", "name_en": "getUserStatus", "summary": "进行发送短信功能的能力", "summary_en": "getUserStatus", "create_time": "27/5/2024 10:39:35", "update_time": "8/7/2024 14:36:38", "userid": 99, "username": "jiangsd3", "realname": "蒋少东", "orgid": 39, "org_name": "联通智网创新中心本部-网络数据室", "online_time": "27/5/2024 10:39:35", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1720420597834, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "getValidateCode", "old_capability_key": "fKHrcV", "old_capability_id": "2405101757228408801", "status": "online", "name": "RequestToValidateAnOTPcode（验证OTP验证码请求）", "name_en": "getValidateCode", "summary": "Request to validate an OTP code（验证OTP验证码请求）", "summary_en": "getValidateCode", "create_time": "17/5/2024 14:23:35", "update_time": "28/6/2024 18:07:51", "userid": 13, "username": "yangf94", "realname": "杨帆", "orgid": 7, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "17/5/2024 14:23:35", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1719569271212, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "historyMonit<PERSON><PERSON>y", "old_capability_key": null, "old_capability_id": "2503211823584509604", "status": "online", "name": "大客户网管历史监控详情查询", "name_en": "Bulk Customer Network Management Historical Monitoring Details Query", "summary": "大客户网管系统电路告警-历史监控详情接口。", "summary_en": "Bulk Customer Network Management System Circuit Alarm - Historical Monitoring Details API", "create_time": "21/3/2025 18:23:58", "update_time": "25/3/2025 16:07:44", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "21/3/2025 18:23:58", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1742890064077, "capability_function": "query", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "ImportImage", "old_capability_key": null, "old_capability_id": "2501161536328829031", "status": "online", "name": "上传一份自定义镜像", "name_en": "Upload a custom image", "summary": "上传一份自定义镜像", "summary_en": "Upload a custom image", "create_time": "16/1/2025 15:36:33", "update_time": "16/1/2025 15:36:33", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/1/2025 15:36:33", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737012992882, "capability_function": "query", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "interfaceIDmore", "old_capability_key": null, "old_capability_id": "2503201353361819450", "status": "online", "name": "生产SIM校验接口多id前段流，后端流", "name_en": "interfaceIDmore", "summary": "interfaceIDmoreinterfaceIDmore", "summary_en": "interfaceIDmoreinterfaceIDmore", "create_time": "20/3/2025 13:53:36", "update_time": "20/3/2025 15:44:11", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "20/3/2025 13:53:36", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1742456650974, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "jiekoubiaos1", "old_capability_key": null, "old_capability_id": "2501151143423939082", "status": "online", "name": "测试批量导入1", "name_en": "testtesttest", "summary": "测试批量导入", "summary_en": "testtesttest", "create_time": "15/1/2025 11:43:42", "update_time": "15/1/2025 11:43:42", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "15/1/2025 11:43:42", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1736912622393, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "jiekoubiaos10", "old_capability_key": null, "old_capability_id": "2501151143425889109", "status": "online", "name": "测试批量导入10", "name_en": "testtesttest", "summary": "测试批量导入", "summary_en": "testtesttest", "create_time": "15/1/2025 11:43:43", "update_time": "15/1/2025 11:43:43", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "15/1/2025 11:43:43", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1736912622588, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "jiekoubiaos2", "old_capability_key": null, "old_capability_id": "2501151143424159085", "status": "online", "name": "测试批量导入2", "name_en": "testtesttest", "summary": "测试批量导入", "summary_en": "testtesttest", "create_time": "15/1/2025 11:43:42", "update_time": "15/1/2025 11:43:42", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "15/1/2025 11:43:42", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1736912622415, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "jiekoubiaos3", "old_capability_key": null, "old_capability_id": "2501151143424379088", "status": "online", "name": "测试批量导入3", "name_en": "testtesttest", "summary": "测试批量导入", "summary_en": "testtesttest", "create_time": "15/1/2025 11:43:42", "update_time": "15/1/2025 11:43:42", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "15/1/2025 11:43:42", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1736912622437, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "jiekoubiaos4", "old_capability_key": null, "old_capability_id": "2501151143424609091", "status": "online", "name": "测试批量导入4", "name_en": "testtesttest", "summary": "测试批量导入", "summary_en": "testtesttest", "create_time": "15/1/2025 11:43:42", "update_time": "15/1/2025 11:43:42", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "15/1/2025 11:43:42", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1736912622460, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "jiekoubiaos5", "old_capability_key": null, "old_capability_id": "2501151143424829094", "status": "online", "name": "测试批量导入5", "name_en": "testtesttest", "summary": "测试批量导入", "summary_en": "testtesttest", "create_time": "15/1/2025 11:43:42", "update_time": "15/1/2025 11:43:42", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "15/1/2025 11:43:42", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1736912622482, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "jiekoubiaos6", "old_capability_key": null, "old_capability_id": "2501151143425029097", "status": "online", "name": "测试批量导入6", "name_en": "testtesttest", "summary": "测试批量导入", "summary_en": "testtesttest", "create_time": "15/1/2025 11:43:43", "update_time": "15/1/2025 11:43:43", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "15/1/2025 11:43:43", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1736912622502, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "jiekoubiaos7", "old_capability_key": null, "old_capability_id": "2501151143425249100", "status": "online", "name": "测试批量导入7", "name_en": "testtesttest", "summary": "测试批量导入", "summary_en": "testtesttest", "create_time": "15/1/2025 11:43:43", "update_time": "15/1/2025 11:43:43", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "15/1/2025 11:43:43", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1736912622524, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "jiekoubiaos8", "old_capability_key": null, "old_capability_id": "2501151143425459103", "status": "online", "name": "测试批量导入8", "name_en": "testtesttest", "summary": "测试批量导入", "summary_en": "testtesttest", "create_time": "15/1/2025 11:43:43", "update_time": "15/1/2025 11:43:43", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "15/1/2025 11:43:43", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1736912622545, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "jiekoubiaos9", "old_capability_key": null, "old_capability_id": "2501151143425669106", "status": "online", "name": "测试批量导入9", "name_en": "testtesttest", "summary": "测试批量导入", "summary_en": "testtesttest", "create_time": "15/1/2025 11:43:43", "update_time": "15/1/2025 11:43:43", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "15/1/2025 11:43:43", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1736912622566, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "jiekoubiaoshi1", "old_capability_key": null, "old_capability_id": "2501151117451659006", "status": "online", "name": "测试批量导入1", "name_en": "testtesttest", "summary": "测试批量导入", "summary_en": "testtesttest", "create_time": "15/1/2025 11:17:45", "update_time": "15/1/2025 11:17:45", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "15/1/2025 11:17:45", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1736911065166, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "jiekoubiaoshi10", "old_capability_key": null, "old_capability_id": "2501151119302949034", "status": "online", "name": "测试批量导入10", "name_en": "testtesttest", "summary": "测试批量导入", "summary_en": "testtesttest", "create_time": "15/1/2025 11:19:30", "update_time": "15/1/2025 11:19:30", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "15/1/2025 11:19:30", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1736911170295, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "jiekoubiaoshi11", "old_capability_key": null, "old_capability_id": "2501151119303189037", "status": "online", "name": "测试批量导入11", "name_en": "testtesttest", "summary": "测试批量导入", "summary_en": "testtesttest", "create_time": "15/1/2025 11:19:30", "update_time": "15/1/2025 11:19:30", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "15/1/2025 11:19:30", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1736911170318, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "jiekoubiaoshi12", "old_capability_key": null, "old_capability_id": "2501151119303429040", "status": "online", "name": "测试批量导入12", "name_en": "testtesttest", "summary": "测试批量导入", "summary_en": "testtesttest", "create_time": "15/1/2025 11:19:30", "update_time": "15/1/2025 11:19:30", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "15/1/2025 11:19:30", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1736911170342, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "jiekoubiaoshi13", "old_capability_key": null, "old_capability_id": "2501151119303649043", "status": "online", "name": "测试批量导入13", "name_en": "testtesttest", "summary": "测试批量导入", "summary_en": "testtesttest", "create_time": "15/1/2025 11:19:30", "update_time": "15/1/2025 11:19:30", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "15/1/2025 11:19:30", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1736911170364, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "jiekoubiaoshi14", "old_capability_key": null, "old_capability_id": "2501151119303889046", "status": "online", "name": "测试批量导入14", "name_en": "testtesttest", "summary": "测试批量导入", "summary_en": "testtesttest", "create_time": "15/1/2025 11:19:30", "update_time": "15/1/2025 11:19:30", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "15/1/2025 11:19:30", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1736911170388, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "jiekoubiaoshi15", "old_capability_key": null, "old_capability_id": "2501151119304129049", "status": "online", "name": "测试批量导入15", "name_en": "testtesttest", "summary": "测试批量导入", "summary_en": "testtesttest", "create_time": "15/1/2025 11:19:30", "update_time": "15/1/2025 11:19:30", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "15/1/2025 11:19:30", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1736911170412, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "jiekoubiaoshi16", "old_capability_key": null, "old_capability_id": "2501151119304359052", "status": "online", "name": "测试批量导入16", "name_en": "testtesttest", "summary": "测试批量导入", "summary_en": "testtesttest", "create_time": "15/1/2025 11:19:30", "update_time": "15/1/2025 11:19:30", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "15/1/2025 11:19:30", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1736911170435, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "jiekoubiaoshi17", "old_capability_key": null, "old_capability_id": "2501151119304689055", "status": "online", "name": "测试批量导入17", "name_en": "testtesttest", "summary": "测试批量导入", "summary_en": "testtesttest", "create_time": "15/1/2025 11:19:30", "update_time": "15/1/2025 11:19:30", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "15/1/2025 11:19:30", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1736911170468, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "jiekoubiaoshi18", "old_capability_key": null, "old_capability_id": "2501151119304929058", "status": "online", "name": "测试批量导入18", "name_en": "testtesttest", "summary": "测试批量导入", "summary_en": "testtesttest", "create_time": "15/1/2025 11:19:30", "update_time": "15/1/2025 11:19:30", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "15/1/2025 11:19:30", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1736911170492, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "jiekoubiaoshi19", "old_capability_key": null, "old_capability_id": "2501151119305159061", "status": "online", "name": "测试批量导入19", "name_en": "testtesttest", "summary": "测试批量导入", "summary_en": "testtesttest", "create_time": "15/1/2025 11:19:31", "update_time": "15/1/2025 11:19:31", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "15/1/2025 11:19:31", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1736911170515, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "jiekoubiaoshi2", "old_capability_key": null, "old_capability_id": "2501151119301029010", "status": "online", "name": "测试批量导入2", "name_en": "testtesttest", "summary": "测试批量导入", "summary_en": "testtesttest", "create_time": "15/1/2025 11:19:30", "update_time": "15/1/2025 11:19:30", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "15/1/2025 11:19:30", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1736911170102, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "jiekoubiaoshi20", "old_capability_key": null, "old_capability_id": "2501151119305379064", "status": "online", "name": "测试批量导入20", "name_en": "testtesttest", "summary": "测试批量导入", "summary_en": "testtesttest", "create_time": "15/1/2025 11:19:31", "update_time": "15/1/2025 11:19:31", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "15/1/2025 11:19:31", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1736911170537, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "jiekoubiaoshi3", "old_capability_key": null, "old_capability_id": "2501151119301279013", "status": "online", "name": "测试批量导入3", "name_en": "testtesttest", "summary": "测试批量导入", "summary_en": "testtesttest", "create_time": "15/1/2025 11:19:30", "update_time": "15/1/2025 11:19:30", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "15/1/2025 11:19:30", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1736911170127, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "jiekoubiaoshi4", "old_capability_key": null, "old_capability_id": "2501151119301509016", "status": "online", "name": "测试批量导入4", "name_en": "testtesttest", "summary": "测试批量导入", "summary_en": "testtesttest", "create_time": "15/1/2025 11:19:30", "update_time": "15/1/2025 11:19:30", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "15/1/2025 11:19:30", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1736911170150, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "jiekoubiaoshi5", "old_capability_key": null, "old_capability_id": "2501151119301759019", "status": "online", "name": "测试批量导入5", "name_en": "testtesttest", "summary": "测试批量导入", "summary_en": "testtesttest", "create_time": "15/1/2025 11:19:30", "update_time": "15/1/2025 11:19:30", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "15/1/2025 11:19:30", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1736911170175, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "jiekoubiaoshi6", "old_capability_key": null, "old_capability_id": "2501151119301999022", "status": "online", "name": "测试批量导入6", "name_en": "testtesttest", "summary": "测试批量导入", "summary_en": "testtesttest", "create_time": "15/1/2025 11:19:30", "update_time": "15/1/2025 11:19:30", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "15/1/2025 11:19:30", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1736911170199, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "jiekoubiaoshi7", "old_capability_key": null, "old_capability_id": "2501151119302229025", "status": "online", "name": "测试批量导入7", "name_en": "testtesttest", "summary": "测试批量导入", "summary_en": "testtesttest", "create_time": "15/1/2025 11:19:30", "update_time": "15/1/2025 11:19:30", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "15/1/2025 11:19:30", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1736911170222, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "jiekoubiaoshi8", "old_capability_key": null, "old_capability_id": "2501151119302479028", "status": "online", "name": "测试批量导入8", "name_en": "testtesttest", "summary": "测试批量导入", "summary_en": "testtesttest", "create_time": "15/1/2025 11:19:30", "update_time": "15/1/2025 11:19:30", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "15/1/2025 11:19:30", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1736911170247, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "jiekoubiaoshi9", "old_capability_key": null, "old_capability_id": "2501151119302709031", "status": "online", "name": "测试批量导入9", "name_en": "testtesttest", "summary": "测试批量导入", "summary_en": "testtesttest", "create_time": "15/1/2025 11:19:30", "update_time": "15/1/2025 11:19:30", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "15/1/2025 11:19:30", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1736911170270, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "jin<PERSON>", "old_capability_key": null, "old_capability_id": "2410301910508449001", "status": "online", "name": "测试put接口", "name_en": "jin<PERSON>", "summary": "测试put接口", "summary_en": "jin<PERSON>", "create_time": "30/10/2024 19:10:51", "update_time": "30/10/2024 19:10:51", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "30/10/2024 19:10:51", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1730286650851, "capability_function": "insert", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "JINTjosn", "old_capability_key": null, "old_capability_id": "2503131140339849014", "status": "online", "name": "生产导入接口测试", "name_en": "JINTjosn", "summary": "生产导入接口测试接口概述信息导入", "summary_en": "please input JINTjosn", "create_time": "13/3/2025 11:40:34", "update_time": "19/3/2025 22:48:23", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "13/3/2025 11:40:34", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": *************, "capability_function": "update", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "JINTjosn02", "old_capability_key": null, "old_capability_id": "2503171140321279126", "status": "online", "name": "生产导入客户端", "name_en": "JINTjosntestEnglish", "summary": "测试导入接口测试接口概述信息导入", "summary_en": "please input JINTjosn", "create_time": "17/3/2025 11:40:32", "update_time": "19/3/2025 22:48:23", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "17/3/2025 11:40:32", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": *************, "capability_function": "update", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "JINTjosn03", "old_capability_key": null, "old_capability_id": "2503171428569739176", "status": "online", "name": "金涛测试导入客户端", "name_en": "JINTjosntestEnglish", "summary": "测试导入接口测试接口概述信息导入", "summary_en": "please input JINTjosn", "create_time": "17/3/2025 14:28:57", "update_time": "19/3/2025 22:48:23", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "17/3/2025 14:28:57", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": *************, "capability_function": "update", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "JINTjosn04", "old_capability_key": null, "old_capability_id": "2503171651366729229", "status": "online", "name": "金涛测试导入前后端", "name_en": "JINTjosntestEnglish", "summary": "测试导入接口测试接口概述信息导入", "summary_en": "please input JINTjosn", "create_time": "17/3/2025 16:51:37", "update_time": "19/3/2025 22:48:23", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "17/3/2025 16:51:37", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": *************, "capability_function": "update", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "JINTjosn05", "old_capability_key": null, "old_capability_id": "2503171714397569274", "status": "online", "name": "金涛测试导入appid", "name_en": "JINTjosntestEnglish", "summary": "测试导入接口测试接口概述信息导入", "summary_en": "please input JINTjosn", "create_time": "17/3/2025 17:14:40", "update_time": "19/3/2025 22:48:23", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "17/3/2025 17:14:40", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": *************, "capability_function": "update", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "JINTjosntest", "old_capability_key": null, "old_capability_id": "2503131142259109020", "status": "online", "name": "测试导入接口测试", "name_en": "JINTjosntestEnglish", "summary": "测试导入接口测试接口概述信息导入1", "summary_en": "please input JINTjosn", "create_time": "13/3/2025 11:42:26", "update_time": "25/3/2025 16:05:29", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "13/3/2025 11:42:26", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1742889929418, "capability_function": "update", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "jinxingz", "old_capability_key": null, "old_capability_id": "2501091837229369016", "status": "online", "name": "测试1", "name_en": "cse11", "summary": "进行中测试", "summary_en": "coaijabn", "create_time": "9/1/2025 18:37:23", "update_time": "9/1/2025 18:37:23", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "9/1/2025 18:37:23", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1736419042936, "capability_function": "query", "max_delay": null, "capability_type": 2, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "jkMockvv1", "old_capability_key": null, "old_capability_id": "2503221202087999703", "status": "online", "name": "接口vv03", "name_en": "jkvv03", "summary": "jkvv03", "summary_en": "jkvv03", "create_time": "22/3/2025 12:02:09", "update_time": "22/3/2025 12:02:09", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "22/3/2025 12:02:09", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1742616128799, "capability_function": "update", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "jkMockvv2", "old_capability_key": null, "old_capability_id": "2503221203073689706", "status": "online", "name": "接口vv04", "name_en": "jkvv04", "summary": "jkvv04", "summary_en": "jkvv04", "create_time": "22/3/2025 12:03:07", "update_time": "22/3/2025 12:03:07", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "22/3/2025 12:03:07", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1742616187368, "capability_function": "update", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "jkMockvv3", "old_capability_key": null, "old_capability_id": "2503221904031169798", "status": "online", "name": "接口vv06", "name_en": "JINTjosntes06", "summary": "测试导入接口测试接口概述信息导入", "summary_en": "please input JINTjosn", "create_time": "22/3/2025 19:04:03", "update_time": "22/3/2025 19:04:03", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "22/3/2025 19:04:03", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1742641443116, "capability_function": "update", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "jkProjin1", "old_capability_key": null, "old_capability_id": "2503261425506319034", "status": "online", "name": "接口JIN01", "name_en": "portProjin1", "summary": "用于测试授权参数第一个接口\n测试导入接口测试接口概述信息导入", "summary_en": "portProjin1", "create_time": "26/3/2025 14:25:51", "update_time": "26/3/2025 14:25:51", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "26/3/2025 14:25:51", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1742970350632, "capability_function": "update", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "jkProjin2", "old_capability_key": null, "old_capability_id": "2503261425506759037", "status": "online", "name": "接口JIN02", "name_en": "portProjin2", "summary": "用于测试授权参数第二个接口\n测试导入接口测试接口概述信息导入", "summary_en": "portProjin2", "create_time": "26/3/2025 14:25:51", "update_time": "26/3/2025 14:25:51", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "26/3/2025 14:25:51", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1742970350675, "capability_function": "update", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "jkProjin3", "old_capability_key": null, "old_capability_id": "2503261425507119040", "status": "online", "name": "接口JIN03", "name_en": "portProjin3", "summary": "APPkey进行调用\n测试导入接口测试接口概述信息导入", "summary_en": "portProjin3", "create_time": "26/3/2025 14:25:51", "update_time": "26/3/2025 14:25:51", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "26/3/2025 14:25:51", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1742970350711, "capability_function": "update", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "jkProvv1", "old_capability_key": null, "old_capability_id": "2503221152508419624", "status": "online", "name": "接口vv01", "name_en": "jkvv01", "summary": "jkProvv1", "summary_en": "jkProvv1", "create_time": "22/3/2025 11:52:51", "update_time": "22/3/2025 11:52:51", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "22/3/2025 11:52:51", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1742615570841, "capability_function": "update", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "jkProvv2", "old_capability_key": null, "old_capability_id": "2503221859557559790", "status": "online", "name": "接口vv05", "name_en": "JINTjosntes05", "summary": "测试导入接口测试接口概述信息导入", "summary_en": "please input JINTjosn", "create_time": "22/3/2025 18:59:56", "update_time": "22/3/2025 18:59:56", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "22/3/2025 18:59:56", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1742641195755, "capability_function": "update", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "jkProvv6", "old_capability_key": null, "old_capability_id": "2503231046066409820", "status": "online", "name": "接口vv07", "name_en": "JINTjosntes06", "summary": "用于测试多层级授权参数\n测试导入接口测试接口概述信息导入", "summary_en": "please input JINTjosn", "create_time": "23/3/2025 10:46:07", "update_time": "23/3/2025 10:46:07", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "23/3/2025 10:46:07", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1742697966640, "capability_function": "update", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "jkvv02", "old_capability_key": null, "old_capability_id": "2503221154337599627", "status": "online", "name": "接口vv02", "name_en": "jkProvv2", "summary": "jkvv02", "summary_en": "jkvv02", "create_time": "22/3/2025 11:54:34", "update_time": "22/3/2025 11:55:23", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "22/3/2025 11:54:34", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1742615723408, "capability_function": "update", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "jnqhd", "old_capability_key": null, "old_capability_id": "2504091702545779129", "status": "online", "name": "济南前后端流接口", "name_en": "jinan front", "summary": "济南前后端流接口，用于测试api调测", "summary_en": "jinan frontjinan front", "create_time": "9/4/2025 17:02:55", "update_time": "10/4/2025 11:03:41", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "9/4/2025 17:02:55", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1744189374577, "capability_function": "update", "max_delay": null, "capability_type": 1, "capability_label": null, "note": "jinan front济南前后端流接口", "is_mock": 0}, {"opgw_capability_key": "jntestputMock", "old_capability_key": null, "old_capability_id": "2504081008483859006", "status": "online", "name": "济南接口http://192.168.0.130:48000/putMock", "name_en": "JKjntestputMock", "summary": "接口概述(中文)http://192.168.0.130:48000/putMock", "summary_en": "http://192.168.0.130:48000/putMock", "create_time": "8/4/2025 10:08:48", "update_time": "8/4/2025 10:12:01", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "8/4/2025 10:08:48", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1744078128387, "capability_function": "query", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "JNtoken", "old_capability_key": null, "old_capability_id": "2504081509224389023", "status": "online", "name": "济南获取token", "name_en": "JN GET token", "summary": "济南获取token接口", "summary_en": "JN GET token", "create_time": "8/4/2025 15:09:22", "update_time": "8/4/2025 15:09:22", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "8/4/2025 15:09:22", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1744096162438, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "listHistoryTimeAlar", "old_capability_key": null, "old_capability_id": "2503211751298019557", "status": "online", "name": "大客户网管历史业务监控查询", "name_en": "Large Customer Network Management Historical Business Monitoring Query", "summary": "大客户网管系统电路告警-历史业务监控列表接口。", "summary_en": "Large Customer Network Management System Circuit Alarm - Historical Business Monitoring List Interface", "create_time": "21/3/2025 17:51:30", "update_time": "25/3/2025 16:07:51", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "21/3/2025 17:51:30", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1742890071303, "capability_function": "query", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "listKpiData", "old_capability_key": null, "old_capability_id": "2503211830187449607", "status": "online", "name": "大客户网管电路性能数据查询", "name_en": "Bulk Customer Network Management Circuit Performance Data Query", "summary": "大客户网管电路性能数据查询", "summary_en": "Bulk Customer Network Management Circuit Performance Data Query。", "create_time": "21/3/2025 18:30:19", "update_time": "25/3/2025 16:07:36", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "21/3/2025 18:30:19", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1742890055585, "capability_function": "query", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "listRealTimeAlarm", "old_capability_key": null, "old_capability_id": "2503211747521319551", "status": "online", "name": "大客户网管实时业务监控查询", "name_en": "Large Customer Network Management Real-time Business Monitoring Query", "summary": "大客户网管实时业务监控查询。", "summary_en": "Large Customer Network Management Real-time Business Monitoring Query", "create_time": "21/3/2025 17:47:52", "update_time": "25/3/2025 16:08:07", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "21/3/2025 17:47:52", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1742890086742, "capability_function": "query", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "listRealTimeMonitorD", "old_capability_key": null, "old_capability_id": "2503211749474929554", "status": "online", "name": "大客户网管实时监控详情查询", "name_en": "Large Customer Network Management Real-time Monitoring Detail Query", "summary": "大客户网管系统电路告警-实时监控详情接口。", "summary_en": "Large Customer Network Management System Circuit Alarm - Real-time Monitoring Detail Interface", "create_time": "21/3/2025 17:49:47", "update_time": "25/3/2025 16:07:59", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "21/3/2025 17:49:47", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1742890079349, "capability_function": "query", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "locationquery", "old_capability_key": null, "old_capability_id": "2503202020144389502", "status": "online", "name": "位置查询-生产-JINTAO", "name_en": "locationquery", "summary": "位置查询-生产类型接口-JINTAO", "summary_en": "locationquery  JINTAO", "create_time": "20/3/2025 20:20:14", "update_time": "20/3/2025 20:20:14", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "20/3/2025 20:20:14", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1742473214438, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 0}, {"opgw_capability_key": "locationRetrieval", "old_capability_key": null, "old_capability_id": "2412172210049149075", "status": "online", "name": "检索设备所在区域", "name_en": "Retrieve the area where the device is located", "summary": "检索某个用户设备所在的区域。", "summary_en": "Retrieves the area where a user device is located.", "create_time": "17/12/2024 22:10:05", "update_time": "17/12/2024 22:10:05", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "17/12/2024 22:10:05", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1734444604915, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 1}, {"opgw_capability_key": "locationVerificat", "old_capability_key": null, "old_capability_id": "2412172019453309005", "status": "online", "name": "验证设备的位置是否在请求的区域内", "name_en": "Verify that the location of the device is within the requested area", "summary": "验证设备的位置是否在请求的区域内。该操作返回一个验证结果，并且可选地，以百分比形式提供位置验证的匹配率估计", "summary_en": "Verify that the location of the device is within the requested area. This operation returns a validation result and, optionally, provides an estimate of the match rate for location validation in percentage terms", "create_time": "17/12/2024 20:19:45", "update_time": "17/12/2024 20:19:45", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "17/12/2024 20:19:45", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1734437985333, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 1}, {"opgw_capability_key": "Mectest", "old_capability_key": null, "old_capability_id": "2501101424298109048", "status": "online", "name": "创建一块或多块数据盘", "name_en": "Create one or more data disks", "summary": "创建一块或多块数据盘", "summary_en": "Create one or more data disks", "create_time": "10/1/2025 14:24:30", "update_time": "10/1/2025 14:24:30", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "10/1/2025 14:24:30", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1736490269810, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "mockgetCallForward", "old_capability_key": null, "old_capability_id": "2506121402116549852", "status": "online", "name": "活动呼转查询", "name_en": "Active Call", "summary": "此端点提供有关哪种类型的呼叫转移服务处于活动状态的信息。多个服务可以是活动的，例如有条件和无条件的。该端点超出了CFS API的主要范围，因此可以返回错误代码501。", "summary_en": "This endpoint provides information about wich type of call forwarding service is active. More than one service can be active, e.g. conditional and unconditional. This endpoint exceeds the main scope of the CFS API, for this reason an error code 501 can be returned.", "create_time": "12/6/2025 14:02:12", "update_time": "12/6/2025 14:02:12", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "12/6/2025 14:02:12", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1749708131654, "capability_function": "query", "max_delay": null, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 1}, {"opgw_capability_key": "mockGetDscp", "old_capability_key": null, "old_capability_id": "2506121440369190058", "status": "online", "name": "RequestToSendAnOTPcode（请求发送OTP验证码）", "name_en": "getDscp", "summary": "Request to send an OTP code（请求发送OTP验证码）", "summary_en": "getDscp", "create_time": "12/6/2025 14:40:37", "update_time": "12/6/2025 14:40:37", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "12/6/2025 14:40:37", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1749710436919, "capability_function": "query", "max_delay": null, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 1}, {"opgw_capability_key": "mockGetMobilePhone", "old_capability_key": null, "old_capability_id": "2506121344130999759", "status": "online", "name": "获取用户电话号码", "name_en": "Get the user's phone number", "summary": "返回电话号码，以便API客户端可以自己验证号码：", "summary_en": "Return the phone number so that the API client can verify the number itself:", "create_time": "12/6/2025 13:44:13", "update_time": "12/6/2025 13:44:13", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "12/6/2025 13:44:13", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1749707053099, "capability_function": "query", "max_delay": null, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 1}, {"opgw_capability_key": "mockGetUserDevice", "old_capability_key": null, "old_capability_id": "2506121416073039936", "status": "online", "name": "DevicIdentifier(获取用户设备类型)", "name_en": "getUserDeviceType", "summary": "DevicIdentifier(获取用户设备类型)", "summary_en": "get User Device Type", "create_time": "12/6/2025 14:16:07", "update_time": "12/6/2025 14:16:07", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "12/6/2025 14:16:07", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1749708967303, "capability_function": "query", "max_delay": null, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 1}, {"opgw_capability_key": "mockGetUserIMEI", "old_capability_key": null, "old_capability_id": "2506121417546539940", "status": "online", "name": "DevicIdentifier(获取用户设备详细信息）", "name_en": "getUserIMEI", "summary": "Device Identifier(用户设备信息查询）", "summary_en": "getUserIMEI", "create_time": "12/6/2025 14:17:55", "update_time": "12/6/2025 14:17:55", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "12/6/2025 14:17:55", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1749709074653, "capability_function": "query", "max_delay": null, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 1}, {"opgw_capability_key": "mockgetUserLocationG", "old_capability_key": null, "old_capability_id": "2506131717391859074", "status": "online", "name": "查询用户实时位置的GeoHash值", "name_en": "getUserLocationGH", "summary": "查询用户实时位置的GeoHash值", "summary_en": "mock getUserLocationGH", "create_time": "13/6/2025 17:17:39", "update_time": "13/6/2025 17:17:39", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "13/6/2025 17:17:39", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1749806259185, "capability_function": "query", "max_delay": null, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 1}, {"opgw_capability_key": "mockGetValidateCode", "old_capability_key": null, "old_capability_id": "2506121443187530061", "status": "online", "name": "RequestToValidateAnOTPcode（验证OTP验证码请求）", "name_en": "getValidateCode", "summary": "RequestToValidateAnOTPcode（验证OTP验证码请求）", "summary_en": "getValidateCode", "create_time": "12/6/2025 14:43:19", "update_time": "12/6/2025 14:43:19", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "12/6/2025 14:43:19", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1749710598753, "capability_function": "query", "max_delay": null, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 1}, {"opgw_capability_key": "mockMobilePhone", "old_capability_key": null, "old_capability_id": "2506121342434789756", "status": "online", "name": "验证指定的电话号码", "name_en": "Verify the specified phone number", "summary": "验证已获取的电话号码", "summary_en": "Verify that the specified phone number (in plain text or hashed format) matches the phone number currently used by the user. Only one of the normal formats or hash formats can be provided.", "create_time": "12/6/2025 13:42:43", "update_time": "12/6/2025 13:42:43", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "12/6/2025 13:42:43", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1749706963478, "capability_function": "query", "max_delay": null, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 1}, {"opgw_capability_key": "mockQueryUncondit", "old_capability_key": null, "old_capability_id": "2506121405037339855", "status": "online", "name": "无条件呼转查询", "name_en": "queryUnconditionalCa", "summary": "能够查询手机号的呼叫转移业务开通状态，该能力主要用来防止欺诈者使用呼叫转移服务进行诈骗行为。", "summary_en": "The ability to query the call forwarding service activation status of a phone number is mainly used to prevent fraudsters from using call forwarding services for scams.", "create_time": "12/6/2025 14:05:04", "update_time": "12/6/2025 14:05:04", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "12/6/2025 14:05:04", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1749708303733, "capability_function": "query", "max_delay": null, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 1}, {"opgw_capability_key": "mockqueryUncondition", "old_capability_key": null, "old_capability_id": "2506121359584149848", "status": "online", "name": "无条件呼转查询", "name_en": "Unconditional call forwarding query", "summary": "此端点提供有关无条件呼叫转移状态的信息，无论是否处于活动状态。", "summary_en": "This endpoint provides information about the status of the unconditional call forwarding, beeing active or not", "create_time": "12/6/2025 13:59:58", "update_time": "12/6/2025 13:59:58", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "12/6/2025 13:59:58", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1749707998415, "capability_function": "query", "max_delay": null, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 0}, {"opgw_capability_key": "mockRegionUserCount", "old_capability_key": null, "old_capability_id": "2506121429013379980", "status": "online", "name": "RegionUserCount（区域设备数量）", "name_en": "regionUserCount", "summary": "Region User Count（区域设备数量）", "summary_en": "mockRegionUserCounts", "create_time": "12/6/2025 14:29:01", "update_time": "12/6/2025 20:34:10", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "12/6/2025 14:29:01", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1749731650029, "capability_function": "query", "max_delay": null, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 1}, {"opgw_capability_key": "mockSendMessage", "old_capability_key": null, "old_capability_id": "2506121409239279894", "status": "online", "name": "短信群发能力", "name_en": "sendMessage", "summary": "短信群发能力", "summary_en": "sendMessage", "create_time": "12/6/2025 14:09:24", "update_time": "12/6/2025 14:09:24", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "12/6/2025 14:09:24", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1749708563927, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "mockSendMessages", "old_capability_key": null, "old_capability_id": "2506131734305399124", "status": "online", "name": "短信群发能力新", "name_en": "mockSendMessage", "summary": "提供云网短信群发能力，仅限联通手机号", "summary_en": "sendMessage", "create_time": "13/6/2025 17:34:31", "update_time": "13/6/2025 17:34:43", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "13/6/2025 17:34:31", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1749807282531, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 1}, {"opgw_capability_key": "mockVerifyUserLocat", "old_capability_key": null, "old_capability_id": "2506121353582009807", "status": "online", "name": "通过轮廓验证用户位置", "name_en": "verifyUserLocation", "summary": "通过区域轮廓验证用户实时位置及返回最后定位时间。", "summary_en": "verifyUserLocation", "create_time": "12/6/2025 13:53:58", "update_time": "12/6/2025 13:53:58", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "12/6/2025 13:53:58", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1749707638200, "capability_function": "query", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "mockVoiceCode", "old_capability_key": null, "old_capability_id": "2506121448504570103", "status": "online", "name": "语音验证码能力", "name_en": "voiceCode", "summary": "语音验证码能力", "summary_en": "voiceCode", "create_time": "12/6/2025 14:48:50", "update_time": "12/6/2025 14:48:50", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "12/6/2025 14:48:50", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1749710930457, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "mockVoiceNotifySw", "old_capability_key": null, "old_capability_id": "2506121126550369750", "status": "online", "name": "语音通知能力", "name_en": "mockVoiceNotifySw", "summary": "对调用此接口的入参新增敏感词过滤。", "summary_en": "mockVoiceNotifySw", "create_time": "12/6/2025 11:26:55", "update_time": "12/6/2025 11:26:55", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "12/6/2025 11:26:55", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1749698815036, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "modifyCardTask", "old_capability_key": "0qWxEy", "old_capability_id": "2405241652387669311", "status": "online", "name": "企业名片任务修改", "name_en": "modifyCardTask", "summary": "进行发送短信功能的能力", "summary_en": "modifyCardTask", "create_time": "27/5/2024 10:39:37", "update_time": "26/6/2024 11:34:29", "userid": 99, "username": "jiangsd3", "realname": "蒋少东", "orgid": 39, "org_name": "联通智网创新中心本部-网络数据室", "online_time": "27/5/2024 10:39:37", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1719372869275, "capability_function": "update", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "ModifyImageAttribute", "old_capability_key": null, "old_capability_id": "2501161536329139034", "status": "online", "name": "更新自定义镜像信息", "name_en": "Update custom image information", "summary": "更新自定义镜像信息", "summary_en": "Update custom image information", "create_time": "16/1/2025 15:36:33", "update_time": "16/1/2025 15:36:33", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/1/2025 15:36:33", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737012992914, "capability_function": "update", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "ModifySecurityGroupAttribute", "old_capability_key": null, "old_capability_id": "2501161536331479055", "status": "online", "name": "调用ModifySecurityGroupAttribute修改指定安全组的属性，包括修改安全组名称和描述", "name_en": "Call ModifySecurityGroupAttribute to modify attributes of a specified security group, including changing the security group name and description", "summary": "调用ModifySecurityGroupAttribute修改指定安全组的属性，包括修改安全组名称和描述", "summary_en": "Call ModifySecurityGroupAttribute to modify attributes of a specified security group, including changing the security group name and description", "create_time": "16/1/2025 15:36:33", "update_time": "16/1/2025 15:36:33", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/1/2025 15:36:33", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737012993147, "capability_function": "update", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "ModifySnapshotAttribute", "old_capability_key": null, "old_capability_id": "2501161536327639019", "status": "online", "name": "更新快照信息", "name_en": "Update snapshot information", "summary": "更新快照信息", "summary_en": "Update snapshot information", "create_time": "16/1/2025 15:36:33", "update_time": "16/1/2025 15:36:33", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/1/2025 15:36:33", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1737012992763, "capability_function": "update", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "nameqosprofiles", "old_capability_key": null, "old_capability_id": "2502261035091999062", "status": "online", "name": "QOD节电资源信息系统根据name查询qos-profiles", "name_en": "nameqosprofiles", "summary": "根据name查询qos-profiles", "summary_en": "nameqosprofiles", "create_time": "26/2/2025 10:35:09", "update_time": "19/3/2025 22:48:23", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "26/2/2025 10:35:09", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": *************, "capability_function": "query", "max_delay": null, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 1}, {"opgw_capability_key": "obtainDeviceInformation", "old_capability_key": null, "old_capability_id": "2412172210052939114", "status": "online", "name": "获取设备信息", "name_en": "User device information query", "summary": "获取有关给定移动用户正在使用的特定设备的详细信息", "summary_en": "The ability to query IMEI numbers based on Device Identifier enables precise identification and tracking of the unique identity of mobile devices, helping to prevent cybercrime and enhance users' sense of security in their digital lives.", "create_time": "17/12/2024 22:10:05", "update_time": "17/12/2024 22:10:05", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "17/12/2024 22:10:05", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1734444605293, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 1}, {"opgw_capability_key": "opgw0qHVVq", "old_capability_key": "0qHVVq", "old_capability_id": "2407151520410920139", "status": "online", "name": "GW质量监控测试jintao-put-技术10", "name_en": "opgw0qHVVq", "summary": "质量监控测试jintao-10", "summary_en": "opgw0qHVVq", "create_time": "15/7/2024 15:34:21", "update_time": "15/7/2024 15:47:08", "userid": 104, "username": "wangmz90", "realname": "王孟哲", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "15/7/2024 15:34:21", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1721028860982, "capability_function": "update", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "opgw1I9Pav", "old_capability_key": "1I9Pav", "old_capability_id": "2405151044156269275", "status": "online", "name": "获得设备信息", "name_en": "opgw1I9Pav", "summary": "获得设备信息的能力", "summary_en": "opgw1I9Pav", "create_time": "15/5/2024 12:00:49", "update_time": "26/6/2024 11:34:27", "userid": 104, "username": "wangmz90", "realname": "王孟哲", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "15/5/2024 12:01:52", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1719372867256, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "opgw64wH6K", "old_capability_key": "64wH6K", "old_capability_id": "2304071004473312929", "status": "online", "name": "get请求-无参数", "name_en": "opgw64wH6K", "summary": "get请求-无参数get请求-无参数get请求-无参数get请求-无参数", "summary_en": "opgw64wH6K", "create_time": "24/4/2024 09:45:51", "update_time": "26/2/2025 14:00:20", "userid": 104, "username": "wangmz90", "realname": "王孟哲", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "24/4/2024 09:47:01", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": "6/6/2024 14:57:40", "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1740549619575, "capability_function": "insert", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "opgw6Gc31T", "old_capability_key": "6Gc31T", "old_capability_id": "2407151511472319465", "status": "online", "name": "GW质量监控测试jintao-put-技术06", "name_en": "opgw6Gc31T", "summary": "质量监控测试jintao-06", "summary_en": "opgw6Gc31T", "create_time": "15/7/2024 15:34:22", "update_time": "27/9/2024 16:15:13", "userid": 104, "username": "wangmz90", "realname": "王孟哲", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "15/7/2024 15:34:22", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1727424913423, "capability_function": "insert", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "opgw6Xzf8C", "old_capability_key": "6Xzf8C", "old_capability_id": "2405272144290119004", "status": "online", "name": "王孟哲测试同步OPGW", "name_en": "opgw6Xzf8C", "summary": "撒地方师傅水电费递四方速递", "summary_en": "opgw6Xzf8C", "create_time": "28/5/2024 10:41:02", "update_time": "26/6/2024 11:34:31", "userid": 104, "username": "wangmz90", "realname": "王孟哲", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "28/5/2024 10:41:02", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": "3/6/2024 17:01:12", "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1719372870844, "capability_function": "insert", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "opgw8BPFXo", "old_capability_key": "8BPFXo", "old_capability_id": "2407151427502249451", "status": "online", "name": "GW质量监控测试jintao-put-技术01", "name_en": "opgw8BPFXo", "summary": "质量监控测试jintao-put", "summary_en": "opgw8BPFXo", "create_time": "15/7/2024 15:34:21", "update_time": "15/7/2024 15:47:13", "userid": 104, "username": "wangmz90", "realname": "王孟哲", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "15/7/2024 15:34:21", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1721028860872, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "opgw8pCXMA", "old_capability_key": "8pCXMA", "old_capability_id": "2404281842156499106", "status": "online", "name": "创建qos", "name_en": "opgw8pCXMA", "summary": "创建qos", "summary_en": "opgw8pCXMA", "create_time": "28/4/2024 20:17:55", "update_time": "26/6/2024 11:34:27", "userid": 104, "username": "wangmz90", "realname": "王孟哲", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "28/4/2024 21:05:29", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1719372867105, "capability_function": "insert", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "opgw8t9rcH", "old_capability_key": "8t9rcH", "old_capability_id": "2405231618575289059", "status": "online", "name": "查询qos", "name_en": "opgw8t9rcH", "summary": "查询qos查询qos查询qos查询qos查询qos", "summary_en": "opgw8t9rcH", "create_time": "23/5/2024 17:23:57", "update_time": "26/6/2024 11:34:27", "userid": 104, "username": "wangmz90", "realname": "王孟哲", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "23/5/2024 17:23:57", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1719372867498, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "opgw9lLBHT", "old_capability_key": "9lLBHT", "old_capability_id": "2407151511088480097", "status": "online", "name": "GW质量监控测试jintao-put-技术04", "name_en": "opgw9lLBHT", "summary": "质量监控测试jintao-04", "summary_en": "opgw9lLBHT", "create_time": "15/7/2024 15:34:22", "update_time": "16/7/2024 15:18:45", "userid": 104, "username": "wangmz90", "realname": "王孟哲", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "15/7/2024 15:34:22", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": "16/7/2024 15:18:45", "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1721028861763, "capability_function": "delete", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "opgw9mqAs8", "old_capability_key": "9mqAs8", "old_capability_id": "2405151035051739349", "status": "online", "name": "验证短信", "name_en": "opgw9mqAs8", "summary": "验证短信的能力，保证使用的信息与发送信息一致", "summary_en": "opgw9mqAs8", "create_time": "15/5/2024 12:00:49", "update_time": "26/6/2024 11:34:27", "userid": 104, "username": "wangmz90", "realname": "王孟哲", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": null, "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1719372867415, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "opgwbMDIeN", "old_capability_key": "bMDIeN", "old_capability_id": "2407151520197490135", "status": "online", "name": "GW质量监控测试jintao-put-技术11", "name_en": "opgwbMDIeN", "summary": "质量监控测试jintao-11", "summary_en": "opgwbMDIeN", "create_time": "15/7/2024 15:34:21", "update_time": "15/7/2024 15:47:17", "userid": 104, "username": "wangmz90", "realname": "王孟哲", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "15/7/2024 15:34:21", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1721028861070, "capability_function": "insert", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "opgwCJHM", "old_capability_key": "CJHM", "old_capability_id": "2410231729666997080", "status": "online", "name": "用户拆机号码个数查询", "name_en": "opgwCJHM", "summary": "通过输入用户手机号查询用户拆机号码个数", "summary_en": "opgwCJHM", "create_time": "4/11/2024 16:17:49", "update_time": "4/11/2024 16:17:49", "userid": 104, "username": "wangmz90", "realname": "王孟哲", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "4/11/2024 16:17:49", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1730708269386, "capability_function": "query", "max_delay": 10, "capability_type": null, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "opgwCr4RBp", "old_capability_key": "Cr4RBp", "old_capability_id": "2407151512341590115", "status": "online", "name": "GW质量监控测试jintao-put-技术08", "name_en": "opgwCr4RBp", "summary": "质量监控测试jintao-08", "summary_en": "opgwCr4RBp", "create_time": "15/7/2024 15:34:22", "update_time": "27/9/2024 16:15:13", "userid": 104, "username": "wangmz90", "realname": "王孟哲", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "15/7/2024 15:34:22", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1727424913423, "capability_function": "update", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "opgwCtlSFf", "old_capability_key": "CtlSFf", "old_capability_id": "2405151015066199343", "status": "online", "name": "发送短信", "name_en": "opgwCtlSFf", "summary": "进行发送短信功能的能力", "summary_en": "opgwCtlSFf", "create_time": "15/5/2024 12:00:50", "update_time": "26/6/2024 11:34:27", "userid": 104, "username": "wangmz90", "realname": "王孟哲", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": null, "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1719372867179, "capability_function": "insert", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "opgwCZCS", "old_capability_key": "CZCS", "old_capability_id": "2410231729666952070", "status": "online", "name": "用户工作常驻城市查询", "name_en": "opgwCZCS", "summary": "通过输入手机号查询用户工作最常驻城市", "summary_en": "opgwCZCS", "create_time": "4/11/2024 16:17:25", "update_time": "4/11/2024 16:17:25", "userid": 104, "username": "wangmz90", "realname": "王孟哲", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "4/11/2024 16:17:25", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1730708244775, "capability_function": "query", "max_delay": 10, "capability_type": null, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "opgwDSBM", "old_capability_key": "DSBM", "old_capability_id": "2410231729666815077", "status": "online", "name": "实时查询地市编码", "name_en": "opgwDSBM", "summary": "通过输入用户手机号，查询用户实时位置所处地市编码", "summary_en": "opgwDSBM", "create_time": "4/11/2024 16:12:01", "update_time": "4/11/2024 16:12:01", "userid": 104, "username": "wangmz90", "realname": "王孟哲", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "4/11/2024 16:12:01", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1730707921426, "capability_function": "query", "max_delay": 10, "capability_type": null, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "opgwe5OTPa", "old_capability_key": "e5OTPa", "old_capability_id": "2112020944113309031", "status": "online", "name": "王孟哲202112020944V2", "name_en": "opgwe5OTPa", "summary": "22323232323sdf", "summary_en": "opgwe5OTPa", "create_time": "23/5/2024 18:07:00", "update_time": "26/6/2024 11:34:26", "userid": 104, "username": "wangmz90", "realname": "王孟哲", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "23/5/2024 18:07:00", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1719372866291, "capability_function": "insert", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "opgwECQL", "old_capability_key": "ECQL", "old_capability_id": "2410231729666924080", "status": "online", "name": "二次卡清理", "name_en": "opgwECQL", "summary": "利用手机号码查询最新入网时间，与合作客户提供的注册时间（以号码为账号）比较，入网时间晚于注册时间则为二次卡", "summary_en": "opgwECQL", "create_time": "4/11/2024 16:17:06", "update_time": "4/11/2024 16:17:06", "userid": 104, "username": "wangmz90", "realname": "王孟哲", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "4/11/2024 16:17:06", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1730708225851, "capability_function": "query", "max_delay": 10, "capability_type": null, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "opgwEj2GZE", "old_capability_key": "Ej2GZE", "old_capability_id": "2402221347211419352", "status": "online", "name": "固定请求头测试jintao-post-技术03", "name_en": "opgwEj2GZE", "summary": "固定请求头测试jintao-post-技术", "summary_en": "opgwEj2GZE", "create_time": "24/4/2024 09:45:51", "update_time": "26/6/2024 11:34:27", "userid": 104, "username": "wangmz90", "realname": "王孟哲", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "24/4/2024 09:47:01", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1719372866886, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "opgweo<PERSON>ld", "old_capability_key": "eo<PERSON>ld", "old_capability_id": "2404281845240669113", "status": "online", "name": "更新qos", "name_en": "opgweo<PERSON>ld", "summary": "更新qos", "summary_en": "opgweo<PERSON>ld", "create_time": "28/4/2024 20:17:56", "update_time": "26/6/2024 11:34:27", "userid": 104, "username": "wangmz90", "realname": "王孟哲", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "28/4/2024 21:05:29", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1719372867033, "capability_function": "update", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "opgwFjf4zo", "old_capability_key": "Fjf4zo", "old_capability_id": "2407151512031980102", "status": "online", "name": "GW质量监控测试jintao-put-技术07", "name_en": "opgwFjf4zo", "summary": "质量监控测试jintao-07", "summary_en": "opgwFjf4zo", "create_time": "15/7/2024 15:34:21", "update_time": "26/9/2024 16:06:59", "userid": 104, "username": "wangmz90", "realname": "王孟哲", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "15/7/2024 15:34:21", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1727338019213, "capability_function": "delete", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "opgwGHSY", "old_capability_key": "GHSY", "old_capability_id": "2410231729666937070", "status": "online", "name": "固话三要素验证", "name_en": "opgwGHSY", "summary": "验证用户姓名、固话号、证件号三要素是否一致", "summary_en": "opgwGHSY", "create_time": "4/11/2024 16:17:16", "update_time": "4/11/2024 16:17:16", "userid": 104, "username": "wangmz90", "realname": "王孟哲", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "4/11/2024 16:17:16", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1730708236458, "capability_function": "query", "max_delay": 10, "capability_type": null, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "opgwGHZT", "old_capability_key": "GHZT", "old_capability_id": "2410231729666943070", "status": "online", "name": "固话在网状态接口", "name_en": "opgwGHZT", "summary": "通过输入区号+固话号码查询其T-3天的状态", "summary_en": "opgwGHZT", "create_time": "4/11/2024 16:17:21", "update_time": "4/11/2024 16:17:21", "userid": 104, "username": "wangmz90", "realname": "王孟哲", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "4/11/2024 16:17:21", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1730708240664, "capability_function": "query", "max_delay": 10, "capability_type": null, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "opgwGHZW", "old_capability_key": "GHZW", "old_capability_id": "2410231729666968070", "status": "online", "name": "固话在网时长", "name_en": "opgwGHZW", "summary": "通过输入固话号查询该固话的在网时长区间", "summary_en": "opgwGHZW", "create_time": "4/11/2024 16:17:33", "update_time": "4/11/2024 16:17:33", "userid": 104, "username": "wangmz90", "realname": "王孟哲", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "4/11/2024 16:17:33", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1730708253153, "capability_function": "query", "max_delay": 10, "capability_type": null, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "opgwgKSEER", "old_capability_key": "gKSEER", "old_capability_id": "2407151428329839455", "status": "online", "name": "GW质量监控测试jintao-put-技术02", "name_en": "opgwgKSEER", "summary": "质量监控测试jintao-put02", "summary_en": "opgwgKSEER", "create_time": "15/7/2024 15:34:22", "update_time": "15/7/2024 15:46:54", "userid": 104, "username": "wangmz90", "realname": "王孟哲", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "15/7/2024 15:34:22", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1721028861931, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "opgwgPBAOR", "old_capability_key": "gPBAOR", "old_capability_id": "2211241445158739655", "status": "online", "name": "lfy-多文件多参数上传能力", "name_en": "opgwgPBAOR", "summary": "lfy-多文件多参数上传能力lfy-多文件多参数上传能力lfy-多文件多参数上传能力lfy-多文件多参数上传能力", "summary_en": "opgwgPBAOR", "create_time": "24/4/2024 09:45:51", "update_time": "12/11/2024 22:54:44", "userid": 104, "username": "wangmz90", "realname": "王孟哲", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": null, "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1731423284318, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "opgwGZD", "old_capability_key": "GZD", "old_capability_id": "2410231729666806070", "status": "online", "name": "工作地验证（三网）", "name_en": "opgwGZD", "summary": "通过用户手机号码获取用户工作日白天常出现的位置时间，月均排前4位的位置，与输入的经纬度进行对比，获取两个最近位置之间的距离差进行验证。详细地址验证会减弱数据的核准率及响应时间", "summary_en": "opgwGZD", "create_time": "4/11/2024 16:11:55", "update_time": "4/11/2024 16:11:55", "userid": 104, "username": "wangmz90", "realname": "王孟哲", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "4/11/2024 16:11:55", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1730707914524, "capability_function": "query", "max_delay": 10, "capability_type": null, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "opgwHMDC", "old_capability_key": "HMDC", "old_capability_id": "2410231729666899072", "status": "online", "name": "黑名单用户查询（仅联通）", "name_en": "opgwHMDC", "summary": "通过输入用户手机号，查询用户是否是黑名单号码", "summary_en": "opgwHMDC", "create_time": "4/11/2024 16:17:02", "update_time": "4/11/2024 16:17:02", "userid": 104, "username": "wangmz90", "realname": "王孟哲", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "4/11/2024 16:17:02", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1730708221666, "capability_function": "query", "max_delay": 10, "capability_type": null, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "opgwhmfxSb", "old_capability_key": "hmfxSb", "old_capability_id": "2407151511088480027", "status": "online", "name": "号码风险识别(测试)", "name_en": "opgwhmfxSb", "summary": "号码风险识别(测试)", "summary_en": "opgwhmfxSb", "create_time": "15/7/2024 15:34:22", "update_time": "21/10/2024 15:18:45", "userid": 104, "username": "wangmz90", "realname": "王孟哲", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "15/7/2024 15:34:22", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": "16/7/2024 15:18:45", "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1729478060587, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "opgwHMHQ", "old_capability_key": null, "old_capability_id": "2411141044417589083", "status": "online", "name": "号码获取", "name_en": "opgwHMHQ", "summary": "获取设备中当前使用的手机号码", "summary_en": "opgwHMHQ", "create_time": "14/11/2024 10:44:42", "update_time": "14/11/2024 14:13:06", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "14/11/2024 10:44:42", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1731564785978, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "opgwhMUa1j", "old_capability_key": "hMUa1j", "old_capability_id": "2211241036509866171", "status": "online", "name": "lfy-测试1124", "name_en": "opgwhMUa1j", "summary": "lfy-测试1124lfy-测试1124lfy-测试1124lfy-测试1124lfy-测试1124lfy-测试1124", "summary_en": "opgwhMUa1j", "create_time": "24/4/2024 09:45:50", "update_time": "26/6/2024 11:34:26", "userid": 104, "username": "wangmz90", "realname": "王孟哲", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "24/4/2024 09:47:00", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1719372866412, "capability_function": "insert", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "opgwHMyz", "old_capability_key": null, "old_capability_id": "2411141044417199080", "status": "online", "name": "号码验证", "name_en": "opgwHMyz", "summary": "验证设备中当前使用的手机号码", "summary_en": "opgwHMyz", "create_time": "14/11/2024 10:44:42", "update_time": "14/11/2024 14:13:10", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "14/11/2024 10:44:42", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1731564789694, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "opgwHYSF", "old_capability_key": "HYSF", "old_capability_id": "2410231729666978070", "status": "online", "name": "是否合约查询", "name_en": "opgwHYSF", "summary": "通过输入用户手机号查询当前用户是否办理了合约", "summary_en": "opgwHYSF", "create_time": "4/11/2024 16:17:37", "update_time": "4/11/2024 16:17:37", "userid": 104, "username": "wangmz90", "realname": "王孟哲", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "4/11/2024 16:17:37", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1730708257044, "capability_function": "query", "max_delay": 10, "capability_type": null, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "opgwJfo7HZ", "old_capability_key": "Jfo7HZ", "old_capability_id": "2405151048429579353", "status": "online", "name": "区域用户数查询", "name_en": "opgwJfo7HZ", "summary": "用于区域用户数查询的能力接口", "summary_en": "opgwJfo7HZ", "create_time": "15/5/2024 12:00:49", "update_time": "26/6/2024 11:34:27", "userid": 104, "username": "wangmz90", "realname": "王孟哲", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "15/5/2024 12:01:52", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1719372867340, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "opgwJKPF", "old_capability_key": "JKPF", "old_capability_id": "2410231729666888078", "status": "online", "name": "终端更换频次评分接口", "name_en": "opgwJKPF", "summary": "通过输入用户手机号，查询该用户更换终端的平均时间间隔", "summary_en": "opgwJKPF", "create_time": "4/11/2024 16:16:57", "update_time": "4/11/2024 16:16:57", "userid": 104, "username": "wangmz90", "realname": "王孟哲", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "4/11/2024 16:16:57", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1730708217018, "capability_function": "query", "max_delay": 10, "capability_type": null, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "opgwJtG2X0", "old_capability_key": "JtG2X0", "old_capability_id": "2211241111269476245", "status": "online", "name": "lfy-测试-put", "name_en": "opgwJtG2X0", "summary": "lfy-测试-putlfy-测试-putlfy-测试-putlfy-测试-put", "summary_en": "opgwJtG2X0", "create_time": "24/4/2024 09:45:51", "update_time": "26/6/2024 18:44:05", "userid": 104, "username": "wangmz90", "realname": "王孟哲", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "24/4/2024 09:47:00", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1719395045003, "capability_function": "update", "max_delay": 20, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "opgwJTSF", "old_capability_key": "JTSF", "old_capability_id": "2410231729666986071", "status": "online", "name": "是否集团用户查询", "name_en": "opgwJTSF", "summary": "通过输入用户手机号查询当前用户是否为集团用户", "summary_en": "opgwJTSF", "create_time": "4/11/2024 16:17:42", "update_time": "4/11/2024 16:17:42", "userid": 104, "username": "wangmz90", "realname": "王孟哲", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "4/11/2024 16:17:42", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1730708261505, "capability_function": "query", "max_delay": 10, "capability_type": null, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "opgwJZD", "old_capability_key": "JZD", "old_capability_id": "2410231729666800072", "status": "online", "name": "居住地验证（三网）", "name_en": "opgwJZD", "summary": "通过用户手机号码获取用户工作日晚上常出现的位置及周末晚上常出现的同一位置时间总和，月均排前3位的位置，与输入的经纬度进行对比，获取两个最近位置之间的距离差进行验证", "summary_en": "opgwJZD", "create_time": "4/11/2024 16:11:42", "update_time": "4/11/2024 16:11:42", "userid": 104, "username": "wangmz90", "realname": "王孟哲", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "4/11/2024 16:11:42", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1730707902158, "capability_function": "query", "max_delay": 10, "capability_type": null, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "opgwkWRlHx", "old_capability_key": "kWRlHx", "old_capability_id": "2407151432159759459", "status": "online", "name": "GW质量监控测试jintao-put-技术03", "name_en": "opgwkWRlHx", "summary": "质量监控测试jintao-03", "summary_en": "opgwkWRlHx", "create_time": "15/7/2024 15:34:22", "update_time": "15/7/2024 15:46:59", "userid": 104, "username": "wangmz90", "realname": "王孟哲", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "15/7/2024 15:34:22", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1721028861592, "capability_function": "insert", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "opgwLSWZ", "old_capability_key": "LSWZ", "old_capability_id": "2410231729666832071", "status": "online", "name": "历史位置验证", "name_en": "opgwLSWZ", "summary": "通过输入用户手机号、日期、地市编码来判断用户输入日期所在的省份是否匹配 ，月数据支持近半年", "summary_en": "opgwLSWZ", "create_time": "4/11/2024 16:12:14", "update_time": "4/11/2024 16:12:14", "userid": 104, "username": "wangmz90", "realname": "王孟哲", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "4/11/2024 16:12:14", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1730707933612, "capability_function": "query", "max_delay": 10, "capability_type": null, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "opgwNFXC", "old_capability_key": "NFXC", "old_capability_id": "2410231729666848071", "status": "online", "name": "号码风险识别", "name_en": "opgwNFXC", "summary": "入参手机号，输出号码风险", "summary_en": "opgwNFXC", "create_time": "4/11/2024 20:34:14", "update_time": "4/11/2024 20:34:14", "userid": 104, "username": "wangmz90", "realname": "王孟哲", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "4/11/2024 20:34:14", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1730723653715, "capability_function": "query", "max_delay": 10, "capability_type": null, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "opgwNGSC", "old_capability_key": "NGSC", "old_capability_id": "2410231729666722078", "status": "online", "name": "号码运营商归属查询", "name_en": "opgwNGSC", "summary": "通过输入用户手机号来查看该手机号码归属哪个运营商", "summary_en": "opgwNGSC", "create_time": "4/11/2024 16:11:15", "update_time": "4/11/2024 16:11:15", "userid": 104, "username": "wangmz90", "realname": "王孟哲", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "4/11/2024 16:11:15", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1730707874814, "capability_function": "query", "max_delay": 10, "capability_type": null, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "opgwNSMC", "old_capability_key": "NSMC", "old_capability_id": "2410231729666860069", "status": "online", "name": "手机号是否实名制认证", "name_en": "opgwNSMC", "summary": "通过输入用户手机号，查询该手机号是否已经实名制", "summary_en": "opgwNSMC", "create_time": "4/11/2024 16:16:42", "update_time": "4/11/2024 16:16:42", "userid": 104, "username": "wangmz90", "realname": "王孟哲", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "4/11/2024 16:16:42", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1730708201684, "capability_function": "query", "max_delay": 10, "capability_type": null, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "opgwNXC", "old_capability_key": "NXC", "old_capability_id": "2410231729666713070", "status": "online", "name": "手机号-姓名核查（三网）", "name_en": "opgwNXC", "summary": "通过输入用户手机号、姓名。验证信息是否一致", "summary_en": "opgwNXC", "create_time": "4/11/2024 16:11:06", "update_time": "4/11/2024 16:11:06", "userid": 104, "username": "wangmz90", "realname": "王孟哲", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "4/11/2024 16:11:06", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1730707865873, "capability_function": "query", "max_delay": 10, "capability_type": null, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "opgwNZC", "old_capability_key": "NZC", "old_capability_id": "2410231729666701076", "status": "online", "name": "手机号-证件核查（三网）", "name_en": "opgwNZC", "summary": "通过输入用户手机号，证件类型，证件号查询该手机号证件验证信息是否一致", "summary_en": "opgwNZC", "create_time": "4/11/2024 16:10:00", "update_time": "4/11/2024 16:10:00", "userid": 104, "username": "wangmz90", "realname": "王孟哲", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "4/11/2024 16:10:00", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1730707800457, "capability_function": "query", "max_delay": 10, "capability_type": null, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "opgwNZTS", "old_capability_key": "NZTS", "old_capability_id": "2410231729666728070", "status": "online", "name": "手机号状态(三网）", "name_en": "opgwNZTS", "summary": "通过输入用户手机号查询该手机号目前的状态", "summary_en": "opgwNZTS", "create_time": "4/11/2024 16:11:22", "update_time": "4/11/2024 16:11:22", "userid": 104, "username": "wangmz90", "realname": "王孟哲", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "4/11/2024 16:11:22", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1730707882011, "capability_function": "query", "max_delay": 10, "capability_type": null, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "opgwr4I0GL", "old_capability_key": "r4I0GL", "old_capability_id": "2404281844422369110", "status": "online", "name": "删除qos", "name_en": "opgwr4I0GL", "summary": "删除qos", "summary_en": "opgwr4I0GL", "create_time": "28/4/2024 20:17:56", "update_time": "26/6/2024 11:34:27", "userid": 104, "username": "wangmz90", "realname": "王孟哲", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "28/4/2024 21:05:29", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1719372866963, "capability_function": "delete", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "opgwRHSF", "old_capability_key": "RHSF", "old_capability_id": "2410231729666992069", "status": "online", "name": "是否融合业务用户查询", "name_en": "opgwRHSF", "summary": "通过输入用户手机号查询当前用户是否办理了融合业务", "summary_en": "opgwRHSF", "create_time": "4/11/2024 16:17:45", "update_time": "4/11/2024 16:17:45", "userid": 104, "username": "wangmz90", "realname": "王孟哲", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "4/11/2024 16:17:45", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1730708264950, "capability_function": "query", "max_delay": 10, "capability_type": null, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "opgwRKMD", "old_capability_key": null, "old_capability_id": "2411131722277779061", "status": "online", "name": "人口密度数据", "name_en": "opgwRKMD", "summary": "查询未来该区域的预计的人口密度", "summary_en": "opgwRKMD", "create_time": "13/11/2024 17:22:28", "update_time": "14/11/2024 14:13:16", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "13/11/2024 17:22:28", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1731564796442, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "opgws2K7dG", "old_capability_key": "s2K7dG", "old_capability_id": "2211241344578149589", "status": "online", "name": "lfy-测试-deleteMockBody", "name_en": "opgws2K7dG", "summary": "lfy-测试-deleteMockBody", "summary_en": "opgws2K7dG", "create_time": "24/4/2024 09:45:51", "update_time": "12/11/2024 22:54:32", "userid": 104, "username": "wangmz90", "realname": "王孟哲", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "24/4/2024 10:43:20", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1731423271938, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "opgwSbLWzT", "old_capability_key": null, "old_capability_id": "2411131722278439067", "status": "online", "name": "设备联网状态", "name_en": "opgwSbLWzT", "summary": "检查设备联网状态，请求参数为手机号，响应参数为最新状态上报时间，连接状态（CONNECTED_DATA：数据连接，CONNECTED_SMS：SMS连接,NOT_CONNECTED：未连接）", "summary_en": "opgwSbLWzT", "create_time": "13/11/2024 17:22:28", "update_time": "14/11/2024 14:13:20", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "13/11/2024 17:22:28", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1731564799778, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "opgwSBMY", "old_capability_key": null, "old_capability_id": "2411131709422729055", "status": "online", "name": "设备漫游状态", "name_en": "opgwSBMY", "summary": "检查设备漫游状态，传入手机号，返回设备是否处于漫游状态", "summary_en": "opgwSBMY", "create_time": "13/11/2024 17:09:42", "update_time": "14/11/2024 14:13:42", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "13/11/2024 17:09:42", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1731564821813, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "opgwSIMCk", "old_capability_key": null, "old_capability_id": "2411131805403549073", "status": "online", "name": "SIM交换(检查)", "name_en": "opgwSIMCk", "summary": "检查SIM卡交换是否在过去一段时间内执行过以及获取SIM交换日期", "summary_en": "opgwSIMCk", "create_time": "13/11/2024 18:05:40", "update_time": "14/11/2024 14:13:13", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "13/11/2024 18:05:40", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1731564793365, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "opgwSIMJH", "old_capability_key": null, "old_capability_id": "2411131722278119064", "status": "online", "name": "SIM交换(检索日期)", "name_en": "opgwSIMJH", "summary": "检查SIM卡交换是否在过去一段时间内执行过以及获取SIM交换日期", "summary_en": "opgwSIMJH", "create_time": "13/11/2024 17:22:28", "update_time": "14/11/2024 14:13:30", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "13/11/2024 17:22:28", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1731564810165, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "opgwSSCS", "old_capability_key": "SSCS", "old_capability_id": "2410231729666823077", "status": "online", "name": "实时城市对比", "name_en": "opgwSSCS", "summary": "通过输入用户手机号、地市编码，查询手机号码所在的地市与当前输入地市编码是否一致", "summary_en": "opgwSSCS", "create_time": "4/11/2024 16:12:07", "update_time": "4/11/2024 16:12:07", "userid": 104, "username": "wangmz90", "realname": "王孟哲", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "4/11/2024 16:12:07", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1730707926742, "capability_function": "query", "max_delay": 10, "capability_type": null, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "opgwSSP", "old_capability_key": "SSP", "old_capability_id": "2410231729666793073", "status": "online", "name": "用户归属省市判断", "name_en": "opgwSSP", "summary": "通过输入用户手机号，查询该用户的手机号码归属省市", "summary_en": "opgwSSP", "create_time": "4/11/2024 16:11:35", "update_time": "4/11/2024 16:11:35", "userid": 104, "username": "wangmz90", "realname": "王孟哲", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "4/11/2024 16:11:35", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1730707894998, "capability_function": "query", "max_delay": 10, "capability_type": null, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "opgwsySyZ", "old_capability_key": "sySyZ", "old_capability_id": "2410231729650739968", "status": "online", "name": "三要素验证", "name_en": "opgwsySyZ", "summary": "验证用户姓名、手机号、证件号三要素是否一致。", "summary_en": "opgwsySyZ", "create_time": "23/10/2024 10:50:30", "update_time": "4/11/2024 20:40:47", "userid": 104, "username": "wangmz90", "realname": "王孟哲", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "23/10/2024 10:50:30", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": 0, "version": 1730170410464, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "opgwUNS", "old_capability_key": "UNS", "old_capability_id": "2410231729666854075", "status": "online", "name": "评估用户名下手机号码数量", "name_en": "opgwUNS", "summary": "通过输入用户手机号查询该用户名下的手机号码数量（包含在网号码和已注销且暂未放出的号码）", "summary_en": "opgwUNS", "create_time": "4/11/2024 16:16:34", "update_time": "4/11/2024 16:16:34", "userid": 104, "username": "wangmz90", "realname": "王孟哲", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "4/11/2024 16:16:34", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1730708194204, "capability_function": "query", "max_delay": 10, "capability_type": null, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "opgwuZmmA7", "old_capability_key": "uZmmA7", "old_capability_id": "2211241419248099720", "status": "online", "name": "lfy-测试-文件", "name_en": "opgwuZmmA7", "summary": "lfy-测试-文件lfy-测试-文件lfy-测试-文件lfy-测试-文件lfy-测试-文件", "summary_en": "opgwuZmmA7", "create_time": "24/4/2024 09:45:51", "update_time": "26/6/2024 11:34:27", "userid": 104, "username": "wangmz90", "realname": "王孟哲", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "24/4/2024 11:06:44", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1719372866730, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "opgwV5xiE6", "old_capability_key": "V5xiE6", "old_capability_id": "2407151520022690131", "status": "online", "name": "GW质量监控测试jintao-put-技术09", "name_en": "opgwV5xiE6", "summary": "质量监控测试jintao-09", "summary_en": "opgwV5xiE6", "create_time": "15/7/2024 15:34:21", "update_time": "26/9/2024 16:06:59", "userid": 104, "username": "wangmz90", "realname": "王孟哲", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "15/7/2024 15:34:21", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1727338019213, "capability_function": "update", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "opgwWLLJZt", "old_capability_key": null, "old_capability_id": "2411131722278749070", "status": "online", "name": "网络连接状态分析", "name_en": "opgwWLLJZt", "summary": "检查网络质量。传入用户设备信息和配置文件id，返回当前的网络质量能够满足应用程序配置文件质量的置信度", "summary_en": "opgwWLLJZt", "create_time": "13/11/2024 17:22:28", "update_time": "14/11/2024 14:13:37", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "13/11/2024 17:22:28", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1731564817147, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "opgwwm51JM", "old_capability_key": "wm51JM", "old_capability_id": "2407151511271119462", "status": "online", "name": "GW质量监控测试jintao-put-技术05", "name_en": "opgwwm51JM", "summary": "质量监控测试jintao-05", "summary_en": "opgwwm51JM", "create_time": "15/7/2024 15:34:22", "update_time": "15/7/2024 15:47:03", "userid": 104, "username": "wangmz90", "realname": "王孟哲", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "15/7/2024 15:34:22", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1721028861679, "capability_function": "delete", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "opgwWZDB", "old_capability_key": "WZDB", "old_capability_id": "2410231729666840077", "status": "online", "name": "位置比对", "name_en": "opgwWZDB", "summary": "通过输入用户手机号，目标点所在经度、纬度，判断用户所在位置与输入位置的距离，实时位置数据每五分钟更新一次", "summary_en": "opgwWZDB", "create_time": "4/11/2024 16:12:19", "update_time": "4/11/2024 16:12:19", "userid": 104, "username": "wangmz90", "realname": "王孟哲", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "4/11/2024 16:12:19", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1730707938605, "capability_function": "query", "max_delay": 10, "capability_type": null, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "opgwY3Q4Wv", "old_capability_key": "Y3Q4Wv", "old_capability_id": "2204071008567869003", "status": "online", "name": "能力入驻2022-0407-001", "name_en": "opgwY3Q4Wv", "summary": "能力入驻2022-0407-001能力入驻2022-0407-001", "summary_en": "opgwY3Q4Wv", "create_time": "20/10/2024 17:37:46", "update_time": "20/10/2024 17:37:46", "userid": 104, "username": "wangmz90", "realname": "王孟哲", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "20/10/2024 17:37:46", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1729417065504, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "opgwYHYZ", "old_capability_key": "YHYZ", "old_capability_id": "2410231729666959070", "status": "online", "name": "用户状态验证", "name_en": "opgwYHYZ", "summary": "通过输入用户手机号查询该手机号的状态", "summary_en": "opgwYHYZ", "create_time": "4/11/2024 16:17:29", "update_time": "4/11/2024 16:17:29", "userid": 104, "username": "wangmz90", "realname": "王孟哲", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "4/11/2024 16:17:29", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1730708249401, "capability_function": "query", "max_delay": 10, "capability_type": null, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "opgwYMDC", "old_capability_key": "YMDC", "old_capability_id": "2410231729666931071", "status": "online", "name": "羊毛党判定", "name_en": "opgwYMDC", "summary": "过输入手机号查询该羊毛党风险等级，支持三网手机号", "summary_en": "opgwYMDC", "create_time": "4/11/2024 16:17:12", "update_time": "4/11/2024 16:17:12", "userid": 104, "username": "wangmz90", "realname": "王孟哲", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "4/11/2024 16:17:12", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1730708232023, "capability_function": "query", "max_delay": 10, "capability_type": null, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "opgwZJHY", "old_capability_key": "ZJHY", "old_capability_id": "2410231729666871074", "status": "online", "name": "最近一次活跃时间", "name_en": "opgwZJHY", "summary": "通过手机号查询用户最近一次活跃动作的时间，活跃动作包括：上网、通话、短信等跟基站有互动的动作", "summary_en": "opgwZJHY", "create_time": "4/11/2024 16:16:47", "update_time": "4/11/2024 16:16:47", "userid": 104, "username": "wangmz90", "realname": "王孟哲", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "4/11/2024 16:16:47", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1730708207150, "capability_function": "query", "max_delay": 10, "capability_type": null, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "opgwZJZD", "old_capability_key": "ZJZD", "old_capability_id": "2410231729666881078", "status": "online", "name": "最近使用的终端厂商型号查询", "name_en": "opgwZJZD", "summary": "通过输入用户手机号查询用户最近使用的终端", "summary_en": "opgwZJZD", "create_time": "4/11/2024 16:16:52", "update_time": "4/11/2024 16:16:52", "userid": 104, "username": "wangmz90", "realname": "王孟哲", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "4/11/2024 16:16:52", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1730708212491, "capability_function": "query", "max_delay": 10, "capability_type": null, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "opgwZWHM", "old_capability_key": "ZWHM", "old_capability_id": "2410231729667005070", "status": "online", "name": "用户在网号码个数查询", "name_en": "opgwZWHM", "summary": "通过输入用户手机号查询用户在网号码个数", "summary_en": "opgwZWHM", "create_time": "4/11/2024 16:17:59", "update_time": "4/11/2024 16:17:59", "userid": 104, "username": "wangmz90", "realname": "王孟哲", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "4/11/2024 16:17:59", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1730708279024, "capability_function": "query", "max_delay": 10, "capability_type": null, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "opgwZWS", "old_capability_key": "ZWS", "old_capability_id": "2410231729666786076", "status": "online", "name": "在网时长", "name_en": "opgwZWS", "summary": "通过输入手机号查询该手机号在网时长区间", "summary_en": "opgwZWS", "create_time": "4/11/2024 16:11:29", "update_time": "4/11/2024 16:11:29", "userid": 104, "username": "wangmz90", "realname": "王孟哲", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "4/11/2024 16:11:29", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": *************, "capability_function": "query", "max_delay": 10, "capability_type": null, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "postaccount1", "old_capability_key": null, "old_capability_id": "2504021601456489152", "status": "online", "name": "post接口account1", "name_en": "postaccount1", "summary": "post接口account1", "summary_en": "{\"REQ_HEAD\":{\"APP_ID\":\"apphmiaf\",\"TIMESTAMP\":\"2016-04-12 15:06:06 100\",\"TRANS_ID\":\"20160412150606100335423\",\"TOKEN\":\"2b353bc52180f0e41e12eab32c99ddd4\"},\"REQ_BODY\":{\"custNo\":\"zhang<PERSON>\",\"userId\":\"**********\"}}", "create_time": "2/4/2025 16:01:46", "update_time": "2/4/2025 16:01:46", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "2/4/2025 16:01:46", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": *************, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "postMock", "old_capability_key": null, "old_capability_id": "2504170958415459026", "status": "online", "name": "济南postMock接口", "name_en": "postMock", "summary": "济南postMock接口描述信息", "summary_en": "postMock business", "create_time": "17/4/2025 09:58:42", "update_time": "17/4/2025 09:58:42", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "17/4/2025 09:58:42", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1744855121560, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "putMock", "old_capability_key": null, "old_capability_id": "2504032208389249005", "status": "online", "name": "putMock济南接口", "name_en": "putMock", "summary": "http://192.168.0.130:48000/putMock", "summary_en": "http://192.168.0.130:48000/putMock", "create_time": "3/4/2025 22:08:39", "update_time": "7/4/2025 14:12:43", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "3/4/2025 22:08:39", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1743689318925, "capability_function": "update", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "QODDsys", "old_capability_key": null, "old_capability_id": "2502260944231779009", "status": "online", "name": "QOD节电资源信息系统创建qos接口", "name_en": "QODbase", "summary": "这里是QOD接口概述信息 QOD节电资源信息系统创建qos接口", "summary_en": "addInterface-QODDsys-QODbase", "create_time": "26/2/2025 09:44:23", "update_time": "19/3/2025 22:48:23", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "26/2/2025 09:44:23", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": *************, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 1}, {"opgw_capability_key": "qosprofiles", "old_capability_key": null, "old_capability_id": "2502261033532189058", "status": "online", "name": "QOD节电资源信息系统查询所有qos-profiles", "name_en": "selectqosprofiles", "summary": "查询所有qos-profiles", "summary_en": "selectqosprofiles", "create_time": "26/2/2025 10:33:53", "update_time": "19/3/2025 22:48:23", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "26/2/2025 10:33:53", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": *************, "capability_function": "query", "max_delay": null, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 1}, {"opgw_capability_key": "Query1", "old_capability_key": null, "old_capability_id": "2501131754266049004", "status": "online", "name": "查询用户轨迹（新手动添加）", "name_en": "Query user trajectories (new manual addition)", "summary": "查询用户轨迹-新手动添加", "summary_en": "Query user trajectories -new manual addition", "create_time": "13/1/2025 17:54:27", "update_time": "13/1/2025 19:35:19", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "13/1/2025 17:54:27", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1736768119377, "capability_function": "query", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "queryCityCode", "old_capability_key": null, "old_capability_id": "2412161038198429029", "status": "online", "name": "实时查询地市编码", "name_en": "Real time query of city code", "summary": "实时查询地市编码", "summary_en": "Real time query of city code", "create_time": "16/12/2024 10:38:20", "update_time": "16/12/2024 10:38:20", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/12/2024 10:38:20", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1734316699843, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "queryDeviceInformation", "old_capability_key": null, "old_capability_id": "2412161345420639130", "status": "online", "name": "用户设备信息查询", "name_en": "User device information query", "summary": "基于Device Identifier查询IMEI号的能力，实现了对移动设备唯一身份的精确识别与追踪，助力防范网络犯罪，增强用户数字生活安全感。", "summary_en": "The ability to query IMEI numbers based on Device Identifier enables precise identification and tracking of the unique identity of mobile devices, helping to prevent cybercrime and enhance users' sense of security in their digital lives.", "create_time": "16/12/2024 13:45:42", "update_time": "16/12/2024 13:45:42", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/12/2024 13:45:42", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1734327942085, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "queryDeviceLocation", "old_capability_key": null, "old_capability_id": "2412172210053249117", "status": "online", "name": "查询用户轨迹", "name_en": "User trajectory query", "summary": "此接口用于查询指定用户在一定时间范围内的轨迹数据。支持根据时间范围查询，时间范围限制为不超过 24 小时，但只可查询过去一周内的数据。", "summary_en": "This API is used to query the trajectory data of a specified user within a given time range. The time range is limited to no more than 24 hours, but only data within the past 7 days can be queried.", "create_time": "17/12/2024 22:10:05", "update_time": "17/12/2024 22:10:05", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "17/12/2024 22:10:05", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1734444605325, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "queryUnconditionalCa", "old_capability_key": null, "old_capability_id": "2505151624289079183", "status": "online", "name": "无条件呼转查询", "name_en": "Active Call", "summary": "此端点提供有关哪种类型的呼叫转移服务处于活动状态的信息。多个服务可以是活动的，例如有条件和无条件的。该端点超出了CFS API的主要范围，因此可以返回错误代码501。", "summary_en": "This endpoint provides information about wich type of call forwarding service is active. More than one service can be active, e.g. conditional and unconditional. This endpoint exceeds the main scope of the CFS API, for this reason an error code 501 can be returned.", "create_time": "15/5/2025 16:24:29", "update_time": "11/6/2025 15:18:51", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "15/5/2025 16:24:29", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1749626330904, "capability_function": "query", "max_delay": null, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 0}, {"opgw_capability_key": "queryUnconditionalCall", "old_capability_key": null, "old_capability_id": "2412172210052089108", "status": "online", "name": "活动呼转查询", "name_en": "Active Call", "summary": "此端点提供有关哪种类型的呼叫转移服务处于活动状态的信息。多个服务可以是活动的，例如有条件和无条件的。该端点超出了CFS API的主要范围，因此可以返回错误代码501。", "summary_en": "This endpoint provides information about wich type of call forwarding service is active. More than one service can be active, e.g. conditional and unconditional. This endpoint exceeds the main scope of the CFS API, for this reason an error code 501 can be returned.", "create_time": "17/12/2024 22:10:05", "update_time": "17/12/2024 22:10:05", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "17/12/2024 22:10:05", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1734444605209, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 1}, {"opgw_capability_key": "reachabilityStatusSubscriptions", "old_capability_key": null, "old_capability_id": "2412172248427729159", "status": "online", "name": "添加设备联网状态订阅", "name_en": "Add a device network status subscription", "summary": "添加设备联网状态订阅", "summary_en": "Add a device network status subscription", "create_time": "17/12/2024 22:48:43", "update_time": "17/12/2024 22:48:43", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "17/12/2024 22:48:43", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1734446922772, "capability_function": "insert", "max_delay": 10, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 1}, {"opgw_capability_key": "reachabilityStatusSubscriptionsList", "old_capability_key": null, "old_capability_id": "2412172248427989162", "status": "online", "name": "检索设备联网状态订阅", "name_en": "Retrieve device networking status subscription", "summary": "检索设备联网状态订阅", "summary_en": "Retrieve device networking status subscription", "create_time": "17/12/2024 22:48:43", "update_time": "17/12/2024 22:48:43", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "17/12/2024 22:48:43", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1734446922799, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 1}, {"opgw_capability_key": "regionUserCount", "old_capability_key": "BRlWHq", "old_capability_id": "2405101757248498804", "status": "online", "name": "RegionUserCount（区域设备数量）", "name_en": "regionUserCount", "summary": "Region User Count（区域设备数量）", "summary_en": "regionUserCount", "create_time": "17/5/2024 14:23:35", "update_time": "12/6/2025 20:21:21", "userid": 13, "username": "yangf94", "realname": "杨帆", "orgid": 7, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "17/5/2024 14:23:35", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1749730881416, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "retrieveGeoFencing", "old_capability_key": null, "old_capability_id": "2412172248424959126", "status": "online", "name": "检索地理围栏事件订阅列表", "name_en": "Retrieves a list of geofencing event subscriptions", "summary": "检索地理围栏事件订阅列表。", "summary_en": "Retrieves a list of geofencing event subscriptions.", "create_time": "17/12/2024 22:48:42", "update_time": "17/12/2024 22:48:42", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "17/12/2024 22:48:42", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1734446922496, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 1}, {"opgw_capability_key": "retrieveQodProvisioning", "old_capability_key": null, "old_capability_id": "2412172210046169046", "status": "online", "name": "检索QOD配置", "name_en": "Retrieves the QoD issuance of the device.", "summary": "检索设备的QoD发放。", "summary_en": "Retrieves the QoD issuance of the device.", "create_time": "17/12/2024 22:10:05", "update_time": "17/12/2024 22:10:05", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "17/12/2024 22:10:05", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1734444604617, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 1}, {"opgw_capability_key": "retrieveReachabilityStatus", "old_capability_key": null, "old_capability_id": "2412172210044929031", "status": "online", "name": "获取当前可达性状态信息", "name_en": "Gets current reachability status information", "summary": "获取当前可达性状态信息", "summary_en": "Gets current reachability status information", "create_time": "17/12/2024 22:10:04", "update_time": "17/12/2024 22:10:04", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "17/12/2024 22:10:04", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1734444604493, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 1}, {"opgw_capability_key": "retrieveRoamingStatus", "old_capability_key": null, "old_capability_id": "2412172210045189034", "status": "online", "name": "获取当前漫游状态和国家信息", "name_en": "Get current roaming status and country information", "summary": "获取当前漫游状态和国家信息", "summary_en": "Get current roaming status and country information", "create_time": "17/12/2024 22:10:05", "update_time": "17/12/2024 22:10:05", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "17/12/2024 22:10:05", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1734444604519, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 1}, {"opgw_capability_key": "retrieveSessions", "old_capability_key": null, "old_capability_id": "2412172210050069087", "status": "online", "name": "检索QOS会话", "name_en": "Retrieve QOS session", "summary": "检索QOS会话", "summary_en": "Retrieve QOS session", "create_time": "17/12/2024 22:10:05", "update_time": "17/12/2024 22:10:05", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "17/12/2024 22:10:05", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1734444605007, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 0}, {"opgw_capability_key": "retrieveSimSwap", "old_capability_key": null, "old_capability_id": "2412172210044439025", "status": "online", "name": "获取最后一次SIM交换事件时间戳", "name_en": "Gets the last SIM exchange event timestamp", "summary": "获取提供电话号码的移动用户帐户的最后一次SIM交换事件的时间戳。", "summary_en": "Gets a timestamp of the last SIM exchange event for the mobile user account that provided the phone number.", "create_time": "17/12/2024 22:10:04", "update_time": "17/12/2024 22:10:04", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "17/12/2024 22:10:04", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": *************, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 1}, {"opgw_capability_key": "SCcheckSimSwap", "old_capability_key": null, "old_capability_id": "2503181514181759331", "status": "online", "name": "检查换卡操作-生产接口", "name_en": "Check the card change operation", "summary": "检查在过去一段时间内是否进行过换卡操作", "summary_en": "Check whether the card has been changed in the past period of time", "create_time": "18/3/2025 15:14:18", "update_time": "19/3/2025 22:48:23", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "18/3/2025 15:14:18", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": *************, "capability_function": "query", "max_delay": null, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 0}, {"opgw_capability_key": "SceneExperienceLogin", "old_capability_key": null, "old_capability_id": "2505271917432809308", "status": "online", "name": "能力试用-用户安全登陆验证联合能力", "name_en": "Capability Trial - User security login verification Joint capability", "summary": "能力试用-用户安全登陆验证联合能力", "summary_en": "Capability Trial - User security login verification Joint capability", "create_time": "27/5/2025 19:17:43", "update_time": "27/5/2025 19:17:43", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "27/5/2025 19:17:43", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": *************, "capability_function": "query", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "SceneExperienceRisk", "old_capability_key": null, "old_capability_id": "2505271651160299304", "status": "online", "name": "能力试用-银行客户风险辅助评估联合能力", "name_en": "Capability Trial - Joint Capability for Risk Auxiliary Assessment of Bank Customers", "summary": "能力试用-银行客户风险辅助评估联合能力", "summary_en": "Capability Trial - Joint Capability for Risk Auxiliary Assessment of Bank Customers", "create_time": "27/5/2025 16:51:16", "update_time": "27/5/2025 16:51:16", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "27/5/2025 16:51:16", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": *************, "capability_function": "query", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "SCretrieveSimSwap", "old_capability_key": null, "old_capability_id": "2503181517056759334", "status": "online", "name": "获取最后一次SIM交换事件时间戳-生产类型接口", "name_en": "Gets the last SIM exchange event timestamp", "summary": "获取提供电话号码的移动用户帐户的最后一次SIM交换事件的时间戳。", "summary_en": "Gets a timestamp of the last SIM exchange event for the mobile user account that provided the phone number.", "create_time": "18/3/2025 15:17:06", "update_time": "19/3/2025 22:48:23", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "18/3/2025 15:17:06", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": *************, "capability_function": "query", "max_delay": null, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 0}, {"opgw_capability_key": "searchRegion", "old_capability_key": "ng0Vg0", "old_capability_id": "2405241623214229137", "status": "online", "name": "查询自定义区域配置", "name_en": "searchRegion", "summary": "进行发送短信功能的能力", "summary_en": "searchRegion", "create_time": "27/5/2024 10:39:36", "update_time": "26/6/2024 11:34:28", "userid": 99, "username": "jiangsd3", "realname": "蒋少东", "orgid": 39, "org_name": "联通智网创新中心本部-网络数据室", "online_time": "27/5/2024 10:39:36", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1719372868291, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "selectqos", "old_capability_key": null, "old_capability_id": "2502261030491699052", "status": "online", "name": "QOD节电资源信息系统查询qos", "name_en": "selectqos", "summary": "查询qos", "summary_en": "selectqos", "create_time": "26/2/2025 10:30:49", "update_time": "19/3/2025 22:48:23", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "26/2/2025 10:30:49", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": *************, "capability_function": "query", "max_delay": null, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 1}, {"opgw_capability_key": "sendCode", "old_capability_key": null, "old_capability_id": "2412172210043969019", "status": "online", "name": "发送验证码", "name_en": "Send verification code", "summary": "向收到的电话号码发送包含所需消息和OTP代码的SMS。", "summary_en": "An SMS containing the required message and OTP code is sent to the received phone number.", "create_time": "17/12/2024 22:10:04", "update_time": "17/12/2024 22:10:04", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "17/12/2024 22:10:04", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1734444604397, "capability_function": "insert", "max_delay": 10, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 1}, {"opgw_capability_key": "sendCodebing", "old_capability_key": null, "old_capability_id": "2503200922155539410", "status": "online", "name": "发送验证码-bing", "name_en": "Send verification code", "summary": "向收到的电话号码发送包含所需消息和OTP代码的SMS。", "summary_en": "An SMS containing the required message and OTP code is sent to the received phone number.", "create_time": "20/3/2025 09:22:16", "update_time": "20/3/2025 09:22:16", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "20/3/2025 09:22:16", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1742433735553, "capability_function": "query", "max_delay": null, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 0}, {"opgw_capability_key": "sendMessage", "old_capability_key": "52pgE8", "old_capability_id": "2307040931130159582", "status": "online", "name": "短信群发能力", "name_en": "sendMessage", "summary": "短信群发能力", "summary_en": "sendMessage", "create_time": "20/9/2024 09:55:43", "update_time": "20/9/2024 09:56:47", "userid": 13, "username": "yangf94", "realname": "杨帆", "orgid": 7, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "20/9/2024 09:55:43", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1726797343430, "capability_function": "insert", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "sendMessageBackend", "old_capability_key": null, "old_capability_id": "2506162033490139470", "status": "online", "name": "后端测试-发送短信", "name_en": "Back-end testing - Sending text messages", "summary": "后端测试-发送短信", "summary_en": "Back-end testing - Sending text messages", "create_time": "16/6/2025 20:33:49", "update_time": "16/6/2025 20:33:49", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/6/2025 20:33:49", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1750077229013, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "sendVoiceNotification", "old_capability_key": null, "old_capability_id": "2412172210051549102", "status": "online", "name": "发送语音通知", "name_en": "Send voice notification", "summary": "发送语音通知到指定手机号", "summary_en": "Send a voice notification to a specified phone number", "create_time": "17/12/2024 22:10:05", "update_time": "17/12/2024 22:10:05", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "17/12/2024 22:10:05", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1734444605154, "capability_function": "insert", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "sendVoiceVerification", "old_capability_key": null, "old_capability_id": "2412172210051209099", "status": "online", "name": "发送语音验证码", "name_en": "Send the voice verification code", "summary": "发送语音验证码到指定手机号", "summary_en": "Send the voice verification code to the specified phone number", "create_time": "17/12/2024 22:10:05", "update_time": "17/12/2024 22:10:05", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "17/12/2024 22:10:05", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1734444605121, "capability_function": "insert", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "shegnchan001", "old_capability_key": null, "old_capability_id": "2503171009033719077", "status": "online", "name": "生产api调测接口001", "name_en": "001", "summary": "接口概述生产api调测接口001", "summary_en": "shegnchan001", "create_time": "17/3/2025 10:09:03", "update_time": "19/3/2025 22:48:23", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "17/3/2025 10:09:03", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": *************, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "shortMessage", "old_capability_key": null, "old_capability_id": "2412172210047739064", "status": "online", "name": "发送短信", "name_en": "Send a text message", "summary": "发送短信", "summary_en": "Send a text message", "create_time": "17/12/2024 22:10:05", "update_time": "17/12/2024 22:10:05", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "17/12/2024 22:10:05", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1734444604773, "capability_function": "insert", "max_delay": 10, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 1}, {"opgw_capability_key": "showRule", "old_capability_key": "iVB1m3", "old_capability_id": "2405241716370179192", "status": "online", "name": "司法矫正产品_围栏监控规则查看", "name_en": "showRule", "summary": "进行发送短信功能的能力", "summary_en": "showRule", "create_time": "27/5/2024 10:39:38", "update_time": "8/7/2024 14:38:10", "userid": 99, "username": "jiangsd3", "realname": "蒋少东", "orgid": 39, "org_name": "联通智网创新中心本部-网络数据室", "online_time": "27/5/2024 10:39:38", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1720420690398, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "simSwapSubscriptions", "old_capability_key": null, "old_capability_id": "2412172248425679135", "status": "online", "name": "添加sim卡交换订阅", "name_en": "Add a sim exchange subscription", "summary": "添加sim卡交换订阅", "summary_en": "Add a sim exchange subscription", "create_time": "17/12/2024 22:48:43", "update_time": "17/12/2024 22:48:43", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "17/12/2024 22:48:43", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1734446922568, "capability_function": "insert", "max_delay": 10, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 1}, {"opgw_capability_key": "SimSwapSubscriptionslist", "old_capability_key": null, "old_capability_id": "2412172248425949138", "status": "online", "name": "检索sim卡交换订阅", "name_en": "Retrieve a sim exchange subscription", "summary": "检索sim卡交换订阅", "summary_en": "Retrieve a sim exchange subscription", "create_time": "17/12/2024 22:48:43", "update_time": "17/12/2024 22:48:43", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "17/12/2024 22:48:43", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1734446922594, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 1}, {"opgw_capability_key": "sksendcode", "old_capability_key": null, "old_capability_id": "2505292216186539348", "status": "online", "name": "三网短信", "name_en": "sksendcode", "summary": "sksendcode", "summary_en": "sksendcode", "create_time": "29/5/2025 22:16:19", "update_time": "29/5/2025 22:16:19", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "29/5/2025 22:16:19", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1748528178653, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 0}, {"opgw_capability_key": "skverify", "old_capability_key": null, "old_capability_id": "2506031205597039428", "status": "online", "name": "三网短信-验证接口", "name_en": "sk-verify", "summary": "三网短信-验证接口三网短信-验证接口", "summary_en": "sdfdfdf", "create_time": "3/6/2025 12:06:00", "update_time": "3/6/2025 12:06:00", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "3/6/2025 12:06:00", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1748923559703, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 0}, {"opgw_capability_key": "switchCardTask", "old_capability_key": "RpY75M", "old_capability_id": "2405241654335129176", "status": "online", "name": "企业名片任务启用停用", "name_en": "switchCardTask", "summary": "进行发送短信功能的能力", "summary_en": "switchCardTask", "create_time": "27/5/2024 10:39:37", "update_time": "26/6/2024 11:34:29", "userid": 99, "username": "jiangsd3", "realname": "蒋少东", "orgid": 39, "org_name": "联通智网创新中心本部-网络数据室", "online_time": "27/5/2024 10:39:37", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1719372869358, "capability_function": "update", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "switchFence", "old_capability_key": "SFnfvD", "old_capability_id": "2405241721146509331", "status": "online", "name": "司法矫正产品_围栏启用停用", "name_en": "switchFence", "summary": "进行发送短信功能的能力", "summary_en": "switchFence", "create_time": "27/5/2024 10:39:38", "update_time": "26/6/2024 11:34:30", "userid": 99, "username": "jiangsd3", "realname": "蒋少东", "orgid": 39, "org_name": "联通智网创新中心本部-网络数据室", "online_time": "27/5/2024 10:39:38", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1719372870124, "capability_function": "update", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "<PERSON><PERSON><PERSON>", "old_capability_key": "M3WH9i", "old_capability_id": "2405241713038049322", "status": "online", "name": "司法矫正产品_矫正人员监控启停", "name_en": "<PERSON><PERSON><PERSON>", "summary": "进行发送短信功能的能力", "summary_en": "<PERSON><PERSON><PERSON>", "create_time": "27/5/2024 10:39:38", "update_time": "26/6/2024 11:34:30", "userid": 99, "username": "jiangsd3", "realname": "蒋少东", "orgid": 39, "org_name": "联通智网创新中心本部-网络数据室", "online_time": "27/5/2024 10:39:38", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1719372869848, "capability_function": "update", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "test0113chen", "old_capability_key": null, "old_capability_id": "2501131054117559001", "status": "online", "name": "test0113_path", "name_en": "test0113_path", "summary": "test0113chen", "summary_en": "test0113chen", "create_time": "13/1/2025 10:54:12", "update_time": "13/1/2025 11:22:30", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "13/1/2025 10:54:12", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1736738550406, "capability_function": "query", "max_delay": null, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 1}, {"opgw_capability_key": "test0113chen2", "old_capability_key": null, "old_capability_id": "2501131057055199007", "status": "online", "name": "test0113_query", "name_en": "test0113_query", "summary": "test0113_chen1", "summary_en": "test0113_chen1", "create_time": "13/1/2025 10:57:06", "update_time": "13/1/2025 11:22:38", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "13/1/2025 10:57:06", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1736738557721, "capability_function": "query", "max_delay": null, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 1}, {"opgw_capability_key": "test1", "old_capability_key": null, "old_capability_id": "2501071008183139006", "status": "online", "name": "test1", "name_en": "test1", "summary": "test1", "summary_en": "test1", "create_time": "7/1/2025 10:08:18", "update_time": "7/1/2025 10:08:48", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "7/1/2025 10:08:18", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1736215727636, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": "1", "is_mock": 0}, {"opgw_capability_key": "test1112", "old_capability_key": null, "old_capability_id": "2501091917247069029", "status": "online", "name": "test1112", "name_en": "test1112", "summary": "test1112", "summary_en": "test1112", "create_time": "9/1/2025 19:17:25", "update_time": "9/1/2025 19:17:25", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "9/1/2025 19:17:25", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1736421444706, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "test1test1", "old_capability_key": null, "old_capability_id": "2501071740044659011", "status": "online", "name": "测试test1(非camara)", "name_en": "test1", "summary": "测试test1", "summary_en": "test1test1", "create_time": "7/1/2025 17:40:04", "update_time": "7/1/2025 17:40:04", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "7/1/2025 17:40:04", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1736242804465, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": "测试", "is_mock": 0}, {"opgw_capability_key": "test1test1test1", "old_capability_key": null, "old_capability_id": "2501071741479599014", "status": "online", "name": "测试test1(camara)1", "name_en": "test1test1", "summary": "测试test1(camara)", "summary_en": "test1", "create_time": "7/1/2025 17:41:48", "update_time": "7/1/2025 17:42:04", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "7/1/2025 17:41:48", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1736242923867, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": 101, "note": "test1", "is_mock": 0}, {"opgw_capability_key": "test2", "old_capability_key": null, "old_capability_id": "2501071130076849001", "status": "online", "name": "test2", "name_en": "test2", "summary": "test2", "summary_en": "test2", "create_time": "7/1/2025 11:30:08", "update_time": "7/1/2025 11:30:08", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "7/1/2025 11:30:08", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1736220607686, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": "test2", "is_mock": 0}, {"opgw_capability_key": "test3", "old_capability_key": null, "old_capability_id": "2501071356413659006", "status": "online", "name": "test3(非camara)", "name_en": "test3", "summary": "test3", "summary_en": "test3", "create_time": "7/1/2025 13:56:41", "update_time": "7/1/2025 13:56:41", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "7/1/2025 13:56:41", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1736229401365, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": "test3", "is_mock": 0}, {"opgw_capability_key": "test4", "old_capability_key": null, "old_capability_id": "2501071404132939009", "status": "online", "name": "test4(camara)", "name_en": "test4", "summary": "test4", "summary_en": "test4", "create_time": "7/1/2025 14:04:13", "update_time": "7/1/2025 14:04:13", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "7/1/2025 14:04:13", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1736229853293, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": 101, "note": "test4测试", "is_mock": 0}, {"opgw_capability_key": "test5", "old_capability_key": null, "old_capability_id": "2501071441330979003", "status": "online", "name": "test5(非camara)", "name_en": "test5", "summary": "test5", "summary_en": "test5", "create_time": "7/1/2025 14:41:33", "update_time": "7/1/2025 14:41:33", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "7/1/2025 14:41:33", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1736232093097, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": "test5测试", "is_mock": 0}, {"opgw_capability_key": "test6", "old_capability_key": null, "old_capability_id": "2501071604062429006", "status": "online", "name": "test6(camara)", "name_en": "test6", "summary": "test6", "summary_en": "test6", "create_time": "7/1/2025 16:04:06", "update_time": "7/1/2025 16:04:06", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "7/1/2025 16:04:06", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1736237046244, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": 101, "note": "test6", "is_mock": 0}, {"opgw_capability_key": "uncondlCallForward", "old_capability_key": null, "old_capability_id": "2412161038195899014", "status": "online", "name": "无条件呼转查询", "name_en": "Retrieve the information", "summary": "此端点提供有关无条件呼叫转移状态的信息，无论是否处于活动状态。", "summary_en": "This endpoint provides information about the status of the unconditional call forwarding, beeing active or not", "create_time": "16/12/2024 10:38:20", "update_time": "16/12/2024 10:38:20", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "16/12/2024 10:38:20", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1734316699591, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "updateApplicationProfiles", "old_capability_key": null, "old_capability_id": "2412172210046659052", "status": "online", "name": "更新网络检测模型", "name_en": "Use the new threshold", "summary": "使用新的阈值集更新应用程序的完整网络质量阈值集，以确保良好的最终用户体验", "summary_en": "Update the application's complete network quality threshold set with the new threshold set to ensure a good end-user experience", "create_time": "17/12/2024 22:10:05", "update_time": "17/12/2024 22:10:05", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "17/12/2024 22:10:05", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1734444604666, "capability_function": "update", "max_delay": 10, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 1}, {"opgw_capability_key": "update<PERSON><PERSON>", "old_capability_key": "S85uuv", "old_capability_id": "2405241712065669318", "status": "online", "name": "司法矫正产品_矫正人员修改", "name_en": "update<PERSON><PERSON>", "summary": "进行发送短信功能的能力", "summary_en": "update<PERSON><PERSON>", "create_time": "27/5/2024 10:39:38", "update_time": "26/6/2024 11:34:30", "userid": 99, "username": "jiangsd3", "realname": "蒋少东", "orgid": 39, "org_name": "联通智网创新中心本部-网络数据室", "online_time": "27/5/2024 10:39:38", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1719372869777, "capability_function": "update", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "updateQOS", "old_capability_key": null, "old_capability_id": "2502261056216869073", "status": "online", "name": "QOD节电资源信息系统更新qos", "name_en": "updateQOS", "summary": "更新qos", "summary_en": "updateQOS", "create_time": "26/2/2025 10:56:22", "update_time": "19/3/2025 22:48:23", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "26/2/2025 10:56:22", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": *************, "capability_function": "update", "max_delay": null, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 1}, {"opgw_capability_key": "updateRegion", "old_capability_key": "K6Pdk9", "old_capability_id": "2405241628140399145", "status": "online", "name": "修改自定义区域配置", "name_en": "updateRegion", "summary": "进行发送短信功能的能力", "summary_en": "updateRegion", "create_time": "27/5/2024 10:39:36", "update_time": "26/6/2024 11:34:28", "userid": 99, "username": "jiangsd3", "realname": "蒋少东", "orgid": 39, "org_name": "联通智网创新中心本部-网络数据室", "online_time": "27/5/2024 10:39:36", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1719372868499, "capability_function": "update", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "updateRule", "old_capability_key": "PuHvAL", "old_capability_id": "2405241717436639327", "status": "online", "name": "司法矫正产品_围栏监控规则更新", "name_en": "updateRule", "summary": "进行发送短信功能的能力", "summary_en": "updateRule", "create_time": "27/5/2024 10:39:38", "update_time": "26/6/2024 11:34:30", "userid": 99, "username": "jiangsd3", "realname": "蒋少东", "orgid": 39, "org_name": "联通智网创新中心本部-网络数据室", "online_time": "27/5/2024 10:39:38", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1719372870058, "capability_function": "update", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}, {"opgw_capability_key": "validateCode", "old_capability_key": null, "old_capability_id": "2412172210044199022", "status": "online", "name": "验证用户的验证码", "name_en": "Verify the user's verification code", "summary": "验证代码对于接收到的authenticationId是否有效", "summary_en": "Verify that the code is valid for the authenticationId received", "create_time": "17/12/2024 22:10:04", "update_time": "17/12/2024 22:10:04", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "17/12/2024 22:10:04", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1734444604420, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 1}, {"opgw_capability_key": "verfiyPhoneNumber", "old_capability_key": null, "old_capability_id": "2412172210043649016", "status": "online", "name": "验证指定的电话号码", "name_en": "Verify the specified phone number", "summary": "验证已获取的电话号码", "summary_en": "Verify that the specified phone number (in plain text or hashed format) matches the phone number currently used by the user. Only one of the normal formats or hash formats can be provided.", "create_time": "17/12/2024 22:10:04", "update_time": "17/12/2024 22:10:04", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "17/12/2024 22:10:04", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1734444604365, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 1}, {"opgw_capability_key": "verifyMobilePhone", "old_capability_key": null, "old_capability_id": "2505151603496989049", "status": "online", "name": "验证指定的电话号码", "name_en": "Verify the specified phone number1", "summary": "验证已获取的电话号码", "summary_en": "Verify that the specified phone number (in plain text or hashed format) matches the phone number currently used by the user. Only one of the normal formats or hash formats can be provided.", "create_time": "15/5/2025 16:03:50", "update_time": "20/6/2025 02:33:29", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "15/5/2025 16:03:50", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1750358008737, "capability_function": "query", "max_delay": null, "capability_type": 1, "capability_label": 101, "note": null, "is_mock": 0}, {"opgw_capability_key": "verifyUserLocation", "old_capability_key": null, "old_capability_id": "2505151618249199138", "status": "online", "name": "通过轮廓验证用户位置", "name_en": "verifyUserLocation", "summary": "通过区域轮廓验证用户实时位置及返回最后定位时间。", "summary_en": "verifyUserLocation", "create_time": "15/5/2025 16:18:25", "update_time": "15/5/2025 16:18:25", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "15/5/2025 16:18:25", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1747297104919, "capability_function": "insert", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "voiceCheck", "old_capability_key": null, "old_capability_id": "2505211043161689268", "status": "online", "name": "语音验证", "name_en": "voiceCheck", "summary": "语音验证码能力", "summary_en": "voiceCheck", "create_time": "21/5/2025 10:43:16", "update_time": "21/5/2025 10:43:16", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "21/5/2025 10:43:16", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1747795396168, "capability_function": "query", "max_delay": null, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "voiceCode", "old_capability_key": "q6x9HR", "old_capability_id": "2111121107280289448", "status": "online", "name": "语音验证码能力", "name_en": "voiceCode", "summary": "语音验证码能力", "summary_en": "voiceCode", "create_time": "8/6/2024 21:45:11", "update_time": "28/6/2024 18:07:40", "userid": 13, "username": "yangf94", "realname": "杨帆", "orgid": 7, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "8/6/2024 21:45:11", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1719569259734, "capability_function": "insert", "max_delay": 60, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "voiceNotifySw", "old_capability_key": "sMjLwt", "old_capability_id": "2111121109337999450", "status": "online", "name": "语音通知能力", "name_en": "voiceNotifySw", "summary": "语音通知能力，", "summary_en": "voiceNotifySw", "create_time": "8/6/2024 21:45:11", "update_time": "10/6/2025 19:52:44", "userid": 13, "username": "yangf94", "realname": "杨帆", "orgid": 7, "org_name": "联通智网创新中心本部-网络中台研发室", "online_time": "8/6/2024 21:45:11", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1749556364104, "capability_function": "insert", "max_delay": 60, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "wmzTestCallBack1", "old_capability_key": null, "old_capability_id": "2410311838214309001", "status": "online", "name": "王孟哲测试回调1", "name_en": "wmzTestCallBack1", "summary": "王孟哲测试回调1", "summary_en": "wmzTestCallBack1", "create_time": "31/10/2024 18:38:21", "update_time": "31/10/2024 18:38:21", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "31/10/2024 18:38:21", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1730371101435, "capability_function": "insert", "max_delay": 10, "capability_type": 2, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "wmzTestCallBack2", "old_capability_key": null, "old_capability_id": "2410311838216219004", "status": "online", "name": "王孟哲测试回调2", "name_en": "wmzTestCallBack2", "summary": "王孟哲测试回调2", "summary_en": "wmzTestCallBack2", "create_time": "31/10/2024 18:38:22", "update_time": "31/10/2024 18:38:22", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "31/10/2024 18:38:22", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1730371101624, "capability_function": "insert", "max_delay": 10, "capability_type": 2, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "wmzTestDecimal", "old_capability_key": null, "old_capability_id": "2412131204427899001", "status": "online", "name": "王孟哲测试参数示例小数", "name_en": "ssdssdsds", "summary": "根据省份、高速名称查询高速路段实时人流信息", "summary_en": "Query real-time pedestrian flow information on highway sections based on province and highway name", "create_time": "13/12/2024 12:04:43", "update_time": "13/12/2024 12:04:43", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "13/12/2024 12:04:43", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 1, "opengateway_auth_config": 0, "version": 1734062682807, "capability_function": "query", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 0}, {"opgw_capability_key": "wmzTestImport1", "old_capability_key": null, "old_capability_id": "2410301948386859001", "status": "online", "name": "王孟哲测试导入1", "name_en": "wmzTestImport1", "summary": "王孟哲测试导入1", "summary_en": "wmzTestImport1", "create_time": "30/10/2024 19:48:39", "update_time": "30/10/2024 19:48:39", "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "online_time": "30/10/2024 19:48:39", "protocol_type": "HTTP", "version_recent_update": 0, "version_recent_update_time": null, "opengateway_auth_type": 0, "opengateway_auth_config": -1, "version": 1730288918703, "capability_function": "insert", "max_delay": 10, "capability_type": 1, "capability_label": null, "note": null, "is_mock": 1}]