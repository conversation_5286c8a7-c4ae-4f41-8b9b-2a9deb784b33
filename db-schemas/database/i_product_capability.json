[{"opgw_product_key": "1111", "opgw_capability_key": "getUserLocationHash", "product_id": "2506251738377929209", "old_capability_id": "2506131728077879118", "create_time": "1/8/2025 17:17:20", "update_time": "1/8/2025 17:17:20", "version": 1754039840479}, {"opgw_product_key": "1111", "opgw_capability_key": "sendMessageBackend", "product_id": "2506251738377929209", "old_capability_id": "2506162033490139470", "create_time": "1/8/2025 17:17:20", "update_time": "1/8/2025 17:17:20", "version": 1754039840479}, {"opgw_product_key": "11111111111111", "opgw_capability_key": "getUserByCell", "product_id": "2407151040106749051", "old_capability_id": "2405241444183039085", "create_time": "26/9/2024 18:30:49", "update_time": "26/9/2024 18:30:49", "version": 1727346649251}, {"opgw_product_key": "11111111111111111111", "opgw_capability_key": "add<PERSON><PERSON>", "product_id": "2406041450128599062", "old_capability_id": "2405241714480809188", "create_time": "4/6/2024 17:55:36", "update_time": "4/6/2024 17:55:36", "version": 1717494935820}, {"opgw_product_key": "11111111111111111111", "opgw_capability_key": "opgw6Xzf8C", "product_id": "2406041450128599062", "old_capability_id": "2405272144290119004", "create_time": "4/6/2024 17:55:36", "update_time": "4/6/2024 17:55:36", "version": 1717494935820}, {"opgw_product_key": "11111111111111111111", "opgw_capability_key": "updateRule", "product_id": "2406041450128599062", "old_capability_id": "2405241717436639327", "create_time": "4/6/2024 17:55:36", "update_time": "4/6/2024 17:55:36", "version": 1717494935820}, {"opgw_product_key": "5GSlicing", "opgw_capability_key": "opgwNFXC", "product_id": "2411111938291819030", "old_capability_id": "2410231729666848071", "create_time": "23/6/2025 15:42:15", "update_time": "23/6/2025 15:42:15", "version": 1750664535033}, {"opgw_product_key": "aaaaaaaaaa", "opgw_capability_key": "wmzTestImport1", "product_id": "2409262039185929252", "old_capability_id": "2410301948386859001", "create_time": "11/11/2024 17:08:16", "update_time": "11/11/2024 17:08:16", "version": 1731316096347}, {"opgw_product_key": "aaaaaaaaaaaaaaaaaa", "opgw_capability_key": "opgw6Gc31T", "product_id": "2409262008220669216", "old_capability_id": "2407151511472319465", "create_time": "18/10/2024 14:07:39", "update_time": "18/10/2024 14:07:39", "version": 1729231659481}, {"opgw_product_key": "aaaaaaaaaaaaaaaaaa", "opgw_capability_key": "opgw8BPFXo", "product_id": "2409262008220669216", "old_capability_id": "2407151427502249451", "create_time": "18/10/2024 14:07:39", "update_time": "18/10/2024 14:07:39", "version": 1729231659481}, {"opgw_product_key": "aaaaaaaaaaaaaaaaaa", "opgw_capability_key": "opgwgKSEER", "product_id": "2409262008220669216", "old_capability_id": "2407151428329839455", "create_time": "18/10/2024 14:07:39", "update_time": "18/10/2024 14:07:39", "version": 1729231659481}, {"opgw_product_key": "aaaaaaaaaaas", "opgw_capability_key": "getDomesticCircuitIn", "product_id": "2503121620421479016", "old_capability_id": "2502281413517939124", "create_time": "12/3/2025 16:22:18", "update_time": "12/3/2025 16:22:18", "version": 1741767738457}, {"opgw_product_key": "addCapacityapitiaoce", "opgw_capability_key": "shegnchan001", "product_id": "2503171009595319081", "old_capability_id": "2503171009033719077", "create_time": "26/6/2025 14:12:57", "update_time": "26/6/2025 14:12:57", "version": 1750918376942}, {"opgw_product_key": "addCapacitykehuduan", "opgw_capability_key": "JINTjosn02", "product_id": "2503171140416759129", "old_capability_id": "2503171140321279126", "create_time": "23/6/2025 11:01:47", "update_time": "23/6/2025 11:01:47", "version": 1750647707200}, {"opgw_product_key": "addCapacitypost", "opgw_capability_key": "addInterfacepost", "product_id": "2507241141312429148", "old_capability_id": "2507241131467729144", "create_time": "24/7/2025 11:45:54", "update_time": "24/7/2025 11:45:54", "version": 1753328753872}, {"opgw_product_key": "allupset", "opgw_capability_key": "opgw6Xzf8C", "product_id": "2406031759538499004", "old_capability_id": "2405272144290119004", "create_time": "6/6/2024 11:44:57", "update_time": "6/6/2024 11:44:57", "version": 1717645497490}, {"opgw_product_key": "apikeyJIN", "opgw_capability_key": "jkProjin3", "product_id": "2503261436255659083", "old_capability_id": "2503261425507119040", "create_time": "23/6/2025 11:02:38", "update_time": "23/6/2025 11:02:38", "version": 1750647757530}, {"opgw_product_key": "appidbusiness", "opgw_capability_key": "JINTjosn05", "product_id": "2503171715498069283", "old_capability_id": "2503171714397569274", "create_time": "26/6/2025 10:42:12", "update_time": "26/6/2025 10:42:12", "version": 1750905732412}, {"opgw_product_key": "appidbusiness", "opgw_capability_key": "jntestputMock", "product_id": "2503171715498069283", "old_capability_id": "2504081008483859006", "create_time": "26/6/2025 10:42:12", "update_time": "26/6/2025 10:42:12", "version": 1750905732412}, {"opgw_product_key": "baonuanCapacity", "opgw_capability_key": "JINTjosntest", "product_id": "2503131339247939023", "old_capability_id": "2503131142259109020", "create_time": "13/3/2025 13:48:47", "update_time": "13/3/2025 13:48:47", "version": 1741844926894}, {"opgw_product_key": "BulkCustomer", "opgw_capability_key": "getDomesticCircuitIn", "product_id": "2502281414219959127", "old_capability_id": "2502281413517939124", "create_time": "23/6/2025 10:22:59", "update_time": "23/6/2025 10:22:59", "version": 1750645379487}, {"opgw_product_key": "BulkCustomer", "opgw_capability_key": "getDomesticInterCuto", "product_id": "2502281414219959127", "old_capability_id": "2502281406099939118", "create_time": "23/6/2025 10:22:59", "update_time": "23/6/2025 10:22:59", "version": 1750645379487}, {"opgw_product_key": "BulkCustomer", "opgw_capability_key": "getInterCircuitInfoB", "product_id": "2502281414219959127", "old_capability_id": "2502281408040829121", "create_time": "23/6/2025 10:22:59", "update_time": "23/6/2025 10:22:59", "version": 1750645379487}, {"opgw_product_key": "BulkCustomer", "opgw_capability_key": "getOpenDetailCrossPr", "product_id": "2502281414219959127", "old_capability_id": "2502281355032769109", "create_time": "23/6/2025 10:22:59", "update_time": "23/6/2025 10:22:59", "version": 1750645379487}, {"opgw_product_key": "BulkCustomer", "opgw_capability_key": "getOpenDetailInProvi", "product_id": "2502281414219959127", "old_capability_id": "2502281359355599115", "create_time": "23/6/2025 10:22:59", "update_time": "23/6/2025 10:22:59", "version": 1750645379487}, {"opgw_product_key": "BulkCustomer", "opgw_capability_key": "getOpenList", "product_id": "2502281414219959127", "old_capability_id": "2502281357164119112", "create_time": "23/6/2025 10:22:59", "update_time": "23/6/2025 10:22:59", "version": 1750645379487}, {"opgw_product_key": "callforwardingsign", "opgw_capability_key": "getCallForwarding", "product_id": "2505151629252459194", "old_capability_id": "2505151626098959186", "create_time": "22/7/2025 11:07:24", "update_time": "22/7/2025 11:07:24", "version": 1753153644069}, {"opgw_product_key": "callforwardingsign", "opgw_capability_key": "queryUnconditionalCa", "product_id": "2505151629252459194", "old_capability_id": "2505151624289079183", "create_time": "22/7/2025 11:07:24", "update_time": "22/7/2025 11:07:24", "version": 1753153644069}, {"opgw_product_key": "capacity", "opgw_capability_key": "opgwV5xiE6", "product_id": "2406131641483179002", "old_capability_id": "2407151520022690131", "create_time": "23/6/2025 10:30:34", "update_time": "23/6/2025 10:30:34", "version": 1750645834259}, {"opgw_product_key": "capacityJINTAO", "opgw_capability_key": "JINTjosn04", "product_id": "2503171651432789232", "old_capability_id": "2503171651366729229", "create_time": "17/3/2025 16:52:42", "update_time": "17/3/2025 16:52:42", "version": 1742201562009}, {"opgw_product_key": "capacitykehuduan", "opgw_capability_key": "JINTjosn03", "product_id": "2503171429386059179", "old_capability_id": "2503171428569739176", "create_time": "17/3/2025 14:33:00", "update_time": "17/3/2025 14:33:00", "version": 1742193180077}, {"opgw_product_key": "cloudCapacity", "opgw_capability_key": "cloudsoft", "product_id": "2411041753176179039", "old_capability_id": "2411041751199309048", "create_time": "11/11/2024 17:04:49", "update_time": "11/11/2024 17:04:49", "version": 1731315888997}, {"opgw_product_key": "CorporateBusinessCard", "opgw_capability_key": "sendMessageBackend", "product_id": "2209130942236306204", "old_capability_id": "2506162033490139470", "create_time": "23/6/2025 10:15:36", "update_time": "23/6/2025 10:15:36", "version": 1750644935871}, {"opgw_product_key": "cs3", "opgw_capability_key": "wmzTestImport1", "product_id": "2406091823303839032", "old_capability_id": "2410301948386859001", "create_time": "4/11/2024 16:56:30", "update_time": "4/11/2024 16:56:30", "version": 1730710590246}, {"opgw_product_key": "css1", "opgw_capability_key": "opgw6Xzf8C", "product_id": "2406061145515699372", "old_capability_id": "2405272144290119004", "create_time": "6/6/2024 11:47:08", "update_time": "6/6/2024 11:47:08", "version": 1717645627855}, {"opgw_product_key": "css2", "opgw_capability_key": "opgw6Xzf8C", "product_id": "2406061506509809408", "old_capability_id": "2405272144290119004", "create_time": "6/6/2024 15:19:47", "update_time": "6/6/2024 15:19:47", "version": 1717658386736}, {"opgw_product_key": "csshi11", "opgw_capability_key": "opgw6Xzf8C", "product_id": "2405301955328239050", "old_capability_id": "2405272144290119004", "create_time": "30/5/2024 19:56:47", "update_time": "30/5/2024 19:56:47", "version": 1717070206548}, {"opgw_product_key": "DCLocationFeatureQue", "opgw_capability_key": "0109test", "product_id": "2412181409097249533", "old_capability_id": "2501091614388169005", "create_time": "23/6/2025 15:41:39", "update_time": "23/6/2025 15:41:39", "version": 1750664498634}, {"opgw_product_key": "DCLocationFeatureQue", "opgw_capability_key": "CreateDis299", "product_id": "2412181409097249533", "old_capability_id": "2501171144291449007", "create_time": "23/6/2025 15:41:39", "update_time": "23/6/2025 15:41:39", "version": 1750664498634}, {"opgw_product_key": "DCLocationFeatureQue", "opgw_capability_key": "DescribeAvailabilityZone", "product_id": "2412181409097249533", "old_capability_id": "2501091811201459006", "create_time": "23/6/2025 15:41:39", "update_time": "23/6/2025 15:41:39", "version": 1750664498634}, {"opgw_product_key": "DCLocationFeatureQue", "opgw_capability_key": "getLocationFeatureQue", "product_id": "2412181409097249533", "old_capability_id": "2412131134548209004", "create_time": "23/6/2025 15:41:39", "update_time": "23/6/2025 15:41:39", "version": 1750664498634}, {"opgw_product_key": "dddddddddddddddddddd", "opgw_capability_key": "opgw6Xzf8C", "product_id": "2406031021392199136", "old_capability_id": "2405272144290119004", "create_time": "26/11/2024 11:02:34", "update_time": "26/11/2024 11:02:34", "version": 1732590154099}, {"opgw_product_key": "dddddddddddddddddddd", "opgw_capability_key": "showRule", "product_id": "2406031021392199136", "old_capability_id": "2405241716370179192", "create_time": "26/11/2024 11:02:34", "update_time": "26/11/2024 11:02:34", "version": 1732590154099}, {"opgw_product_key": "DescribeAvailability", "opgw_capability_key": "DescribeAvailabilityZone", "product_id": "2501091813452199009", "old_capability_id": "2501091811201459006", "create_time": "23/6/2025 11:28:11", "update_time": "23/6/2025 11:28:11", "version": 1750649291266}, {"opgw_product_key": "DeviceIdentifier", "opgw_capability_key": "getUserDeviceType", "product_id": "2405111917047399001", "old_capability_id": "2506101445263399532", "create_time": "23/6/2025 09:54:42", "update_time": "23/6/2025 09:54:42", "version": 1750643681752}, {"opgw_product_key": "DeviceIdentifier", "opgw_capability_key": "getUserIMEI", "product_id": "2405111917047399001", "old_capability_id": "2506101447227609535", "create_time": "23/6/2025 09:54:42", "update_time": "23/6/2025 09:54:42", "version": 1750643681752}, {"opgw_product_key": "DSapplicationprofile", "opgw_capability_key": "createApplicationProfiles", "product_id": "2412181402402049382", "old_capability_id": "2412172210046399049", "create_time": "23/6/2025 15:27:04", "update_time": "23/6/2025 15:27:04", "version": 1750663624052}, {"opgw_product_key": "DSapplicationprofile", "opgw_capability_key": "CreateDis299", "product_id": "2412181402402049382", "old_capability_id": "2501171144291449007", "create_time": "23/6/2025 15:27:04", "update_time": "23/6/2025 15:27:04", "version": 1750663624052}, {"opgw_product_key": "DSapplicationprofile", "opgw_capability_key": "deleteApplicationProfiles", "product_id": "2412181402402049382", "old_capability_id": "2412172210047169058", "create_time": "23/6/2025 15:27:04", "update_time": "23/6/2025 15:27:04", "version": 1750663624052}, {"opgw_product_key": "DSapplicationprofile", "opgw_capability_key": "getApplicationProfiles", "product_id": "2412181402402049382", "old_capability_id": "2412172210046929055", "create_time": "23/6/2025 15:27:04", "update_time": "23/6/2025 15:27:04", "version": 1750663624052}, {"opgw_product_key": "DSapplicationprofile", "opgw_capability_key": "updateApplicationProfiles", "product_id": "2412181402402049382", "old_capability_id": "2412172210046659052", "create_time": "23/6/2025 15:27:04", "update_time": "23/6/2025 15:27:04", "version": 1750663624052}, {"opgw_product_key": "DScallforwardingsign", "opgw_capability_key": "Adiskca", "product_id": "2412181348448919188", "old_capability_id": "2411131146381189015", "create_time": "23/6/2025 14:16:15", "update_time": "23/6/2025 14:16:15", "version": 1750659375089}, {"opgw_product_key": "DScallforwardingsign", "opgw_capability_key": "getCallForwardings", "product_id": "2412181348448919188", "old_capability_id": "2412172210051819105", "create_time": "23/6/2025 14:16:15", "update_time": "23/6/2025 14:16:15", "version": 1750659375089}, {"opgw_product_key": "DScallforwardingsign", "opgw_capability_key": "queryUnconditionalCall", "product_id": "2412181348448919188", "old_capability_id": "2412172210052089108", "create_time": "23/6/2025 14:16:15", "update_time": "23/6/2025 14:16:15", "version": 1750659375089}, {"opgw_product_key": "DSconnecitivity", "opgw_capability_key": "connectivityInsightsSubscriptions", "product_id": "2412181419055569698", "old_capability_id": "2412172248428789171", "create_time": "23/6/2025 15:24:46", "update_time": "23/6/2025 15:24:46", "version": 1750663485721}, {"opgw_product_key": "DSconnecitivity", "opgw_capability_key": "connectivityInsightsSubscriptionsList", "product_id": "2412181419055569698", "old_capability_id": "2412172248429049174", "create_time": "23/6/2025 15:24:46", "update_time": "23/6/2025 15:24:46", "version": 1750663485721}, {"opgw_product_key": "DSconnecitivity", "opgw_capability_key": "deleteConnectivityInsightsSubscriptions", "product_id": "2412181419055569698", "old_capability_id": "2412172248429539180", "create_time": "23/6/2025 15:24:46", "update_time": "23/6/2025 15:24:46", "version": 1750663485721}, {"opgw_product_key": "DSconnecitivity", "opgw_capability_key": "getConnectivityInsightsSubscriptions", "product_id": "2412181419055569698", "old_capability_id": "2412172248429319177", "create_time": "23/6/2025 15:24:46", "update_time": "23/6/2025 15:24:46", "version": 1750663485721}, {"opgw_product_key": "DSconnectivityinsigh", "opgw_capability_key": "checkNetworkQuality", "product_id": "2412181404447339418", "old_capability_id": "2412172210047479061", "create_time": "23/6/2025 15:25:09", "update_time": "23/6/2025 15:25:09", "version": 1750663509215}, {"opgw_product_key": "DSdevice", "opgw_capability_key": "deleteReachabilityStatusSubscriptions", "product_id": "2412181415338149655", "old_capability_id": "2412172248428439168", "create_time": "23/6/2025 15:23:54", "update_time": "23/6/2025 15:23:54", "version": 1750663434452}, {"opgw_product_key": "DSdevice", "opgw_capability_key": "getReachabilityStatusSubscriptions", "product_id": "2412181415338149655", "old_capability_id": "2412172248428219165", "create_time": "23/6/2025 15:23:54", "update_time": "23/6/2025 15:23:54", "version": 1750663434452}, {"opgw_product_key": "DSdevice", "opgw_capability_key": "reachabilityStatusSubscriptions", "product_id": "2412181415338149655", "old_capability_id": "2412172248427729159", "create_time": "23/6/2025 15:23:54", "update_time": "23/6/2025 15:23:54", "version": 1750663434452}, {"opgw_product_key": "DSdevice", "opgw_capability_key": "reachabilityStatusSubscriptionsList", "product_id": "2412181415338149655", "old_capability_id": "2412172248427989162", "create_time": "23/6/2025 15:23:54", "update_time": "23/6/2025 15:23:54", "version": 1750663434452}, {"opgw_product_key": "DSdeviceeachability", "opgw_capability_key": "retrieveReachabilityStatus", "product_id": "2412181345270959109", "old_capability_id": "2412172210044929031", "create_time": "23/6/2025 15:23:27", "update_time": "23/6/2025 15:23:27", "version": 1750663407182}, {"opgw_product_key": "DSDeviceIdentifier", "opgw_capability_key": "getDeviceType", "product_id": "2412181420186959741", "old_capability_id": "2412161900195589001", "create_time": "23/6/2025 15:25:56", "update_time": "23/6/2025 15:25:56", "version": 1750663555915}, {"opgw_product_key": "DSDeviceIdentifier", "opgw_capability_key": "obtainDeviceInformation", "product_id": "2412181420186959741", "old_capability_id": "2412172210052939114", "create_time": "23/6/2025 15:25:56", "update_time": "23/6/2025 15:25:56", "version": 1750663555915}, {"opgw_product_key": "DSdeviceroaming", "opgw_capability_key": "deleteDeviceRoamingSubscriptions", "product_id": "2412181414162589612", "old_capability_id": "2412172248427419156", "create_time": "23/6/2025 14:17:13", "update_time": "23/6/2025 14:17:13", "version": 1750659433182}, {"opgw_product_key": "DSdeviceroaming", "opgw_capability_key": "deviceRoamingSubscriptions", "product_id": "2412181414162589612", "old_capability_id": "2412172248426719147", "create_time": "23/6/2025 14:17:13", "update_time": "23/6/2025 14:17:13", "version": 1750659433182}, {"opgw_product_key": "DSdeviceroaming", "opgw_capability_key": "deviceRoamingSubscriptionslist", "product_id": "2412181414162589612", "old_capability_id": "2412172248426979150", "create_time": "23/6/2025 14:17:13", "update_time": "23/6/2025 14:17:13", "version": 1750659433182}, {"opgw_product_key": "DSdeviceroaming", "opgw_capability_key": "getDeviceRoamingSubscriptions", "product_id": "2412181414162589612", "old_capability_id": "2412172248427209153", "create_time": "23/6/2025 14:17:13", "update_time": "23/6/2025 14:17:13", "version": 1750659433182}, {"opgw_product_key": "DSdeviceroamingstatu", "opgw_capability_key": "current1", "product_id": "2412181347224379152", "old_capability_id": "2501131844001259013", "create_time": "23/6/2025 14:20:25", "update_time": "23/6/2025 14:20:25", "version": 1750659625257}, {"opgw_product_key": "DSdeviceroamingstatu", "opgw_capability_key": "retrieveRoamingStatus", "product_id": "2412181347224379152", "old_capability_id": "2412172210045189034", "create_time": "23/6/2025 14:20:25", "update_time": "23/6/2025 14:20:25", "version": 1750659625257}, {"opgw_product_key": "dsdsdsd", "opgw_capability_key": "wmzTestImport1", "product_id": "2409261836205399083", "old_capability_id": "2410301948386859001", "create_time": "11/11/2024 17:09:30", "update_time": "11/11/2024 17:09:30", "version": 1731316170006}, {"opgw_product_key": "DSgeofencing", "opgw_capability_key": "createGeoFencing", "product_id": "2412181336125709001", "old_capability_id": "2412172248424589123", "create_time": "23/6/2025 15:32:04", "update_time": "23/6/2025 15:32:04", "version": 1750663923719}, {"opgw_product_key": "DSgeofencing", "opgw_capability_key": "deleteGeoFencing", "product_id": "2412181336125709001", "old_capability_id": "2412172248425399132", "create_time": "23/6/2025 15:32:04", "update_time": "23/6/2025 15:32:04", "version": 1750663923719}, {"opgw_product_key": "DSgeofencing", "opgw_capability_key": "getGeoFencing", "product_id": "2412181336125709001", "old_capability_id": "2412172248425179129", "create_time": "23/6/2025 15:32:04", "update_time": "23/6/2025 15:32:04", "version": 1750663923719}, {"opgw_product_key": "DSgeofencing", "opgw_capability_key": "retrieveGeoFencing", "product_id": "2412181336125709001", "old_capability_id": "2412172248424959126", "create_time": "23/6/2025 15:32:04", "update_time": "23/6/2025 15:32:04", "version": 1750663923719}, {"opgw_product_key": "DSHighSpeedRealtimeC", "opgw_capability_key": "getHighSpeedRealtimeC", "product_id": "2412181408054459497", "old_capability_id": "2412161038197989026", "create_time": "23/6/2025 15:20:00", "update_time": "23/6/2025 15:20:00", "version": 1750663200469}, {"opgw_product_key": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "opgw_capability_key": "locationRetrieval", "product_id": "2412181110200029253", "old_capability_id": "2412172210049149075", "create_time": "23/6/2025 15:30:30", "update_time": "23/6/2025 15:30:30", "version": 1750663829641}, {"opgw_product_key": "DSlocationverificat", "opgw_capability_key": "locationVerificat", "product_id": "2412181046240079216", "old_capability_id": "2412172019453309005", "create_time": "23/6/2025 14:15:37", "update_time": "23/6/2025 14:15:37", "version": 1750659337392}, {"opgw_product_key": "DSNumbercarrierattri", "opgw_capability_key": "getNumbercarrierattri", "product_id": "2412181350227619224", "old_capability_id": "2412172210052369111", "create_time": "23/6/2025 15:28:13", "update_time": "23/6/2025 15:28:13", "version": 1750663692562}, {"opgw_product_key": "DSNumberRiskIdentifi", "opgw_capability_key": "getNumberRiskIdentifi", "product_id": "2412181353455809260", "old_capability_id": "2412161038197039020", "create_time": "23/6/2025 15:29:27", "update_time": "23/6/2025 15:29:27", "version": 1750663766697}, {"opgw_product_key": "DSnumberverification", "opgw_capability_key": "getPhoneNumber", "product_id": "2412181339296789037", "old_capability_id": "2412172210043199013", "create_time": "23/6/2025 11:41:48", "update_time": "23/6/2025 11:41:48", "version": 1750650107744}, {"opgw_product_key": "DSnumberverification", "opgw_capability_key": "verfiyPhoneNumber", "product_id": "2412181339296789037", "old_capability_id": "2412172210043649016", "create_time": "23/6/2025 11:41:48", "update_time": "23/6/2025 11:41:48", "version": 1750650107744}, {"opgw_product_key": "DSonetimebing", "opgw_capability_key": "sendCodebing", "product_id": "2503200922227829413", "old_capability_id": "2503200922155539410", "create_time": "27/6/2025 10:18:27", "update_time": "27/6/2025 10:18:27", "version": 1750990707134}, {"opgw_product_key": "DSonetimepasswordsm", "opgw_capability_key": "opgwhMUa1j", "product_id": "2412101621310289073", "old_capability_id": "2211241036509866171", "create_time": "23/6/2025 11:43:36", "update_time": "23/6/2025 11:43:36", "version": 1750650215552}, {"opgw_product_key": "DSonetimepasswordsm", "opgw_capability_key": "sendCode", "product_id": "2412101621310289073", "old_capability_id": "2412172210043969019", "create_time": "23/6/2025 11:43:36", "update_time": "23/6/2025 11:43:36", "version": 1750650215552}, {"opgw_product_key": "DSonetimepasswordsm", "opgw_capability_key": "validateCode", "product_id": "2412101621310289073", "old_capability_id": "2412172210044199022", "create_time": "23/6/2025 11:43:36", "update_time": "23/6/2025 11:43:36", "version": 1750650215552}, {"opgw_product_key": "DSqodprovisioning", "opgw_capability_key": "createQodProvisioning", "product_id": "2412101044584369005", "old_capability_id": "2412172210045419037", "create_time": "23/6/2025 15:32:34", "update_time": "23/6/2025 15:32:34", "version": 1750663953652}, {"opgw_product_key": "DSqodprovisioning", "opgw_capability_key": "deleteQodProvisioning", "product_id": "2412101044584369005", "old_capability_id": "2412172210045929043", "create_time": "23/6/2025 15:32:34", "update_time": "23/6/2025 15:32:34", "version": 1750663953652}, {"opgw_product_key": "DSqodprovisioning", "opgw_capability_key": "getQodProvisioning", "product_id": "2412101044584369005", "old_capability_id": "2412172210045659040", "create_time": "23/6/2025 15:32:34", "update_time": "23/6/2025 15:32:34", "version": 1750663953652}, {"opgw_product_key": "DSqodprovisioning", "opgw_capability_key": "retrieveQodProvisioning", "product_id": "2412101044584369005", "old_capability_id": "2412172210046169046", "create_time": "23/6/2025 15:32:34", "update_time": "23/6/2025 15:32:34", "version": 1750663953652}, {"opgw_product_key": "DSqosprofiles", "opgw_capability_key": "getAllProfiles", "product_id": "2412181356579509339", "old_capability_id": "2412172210049379078", "create_time": "23/6/2025 15:27:24", "update_time": "23/6/2025 15:27:24", "version": 1750663643778}, {"opgw_product_key": "DSqosprofiles", "opgw_capability_key": "getProfilesByName", "product_id": "2412181356579509339", "old_capability_id": "2412172210049609081", "create_time": "23/6/2025 15:27:24", "update_time": "23/6/2025 15:27:24", "version": 1750663643778}, {"opgw_product_key": "DSQualityOnDemand", "opgw_capability_key": "jkMockvv3", "product_id": "2412101049579599041", "old_capability_id": "2503221904031169798", "create_time": "23/6/2025 13:57:36", "update_time": "23/6/2025 13:57:36", "version": 1750658256472}, {"opgw_product_key": "DSRealtimequeryofcod", "opgw_capability_key": "queryCityCode", "product_id": "2412181456501999793", "old_capability_id": "2412161038198429029", "create_time": "23/6/2025 15:37:47", "update_time": "23/6/2025 15:37:47", "version": 1750664267434}, {"opgw_product_key": "DSRegionDeviceCount", "opgw_capability_key": "getDeviceCount", "product_id": "2412181406375099454", "old_capability_id": "2412171932106579001", "create_time": "16/6/2025 09:27:01", "update_time": "16/6/2025 09:27:01", "version": 1750037220930}, {"opgw_product_key": "DSScalperdeterminati", "opgw_capability_key": "getScalperdeterminati", "product_id": "2412181355083889296", "old_capability_id": "2412161038197399023", "create_time": "23/6/2025 15:26:50", "update_time": "23/6/2025 15:26:50", "version": 1750663609889}, {"opgw_product_key": "DSShortMessageServ", "opgw_capability_key": "opgwhMUa1j", "product_id": "2412101623539319109", "old_capability_id": "2211241036509866171", "create_time": "23/6/2025 15:37:23", "update_time": "23/6/2025 15:37:23", "version": 1750664243422}, {"opgw_product_key": "DSShortMessageServ", "opgw_capability_key": "shortMessage", "product_id": "2412101623539319109", "old_capability_id": "2412172210047739064", "create_time": "23/6/2025 15:37:23", "update_time": "23/6/2025 15:37:23", "version": 1750664243422}, {"opgw_product_key": "DSsimswap", "opgw_capability_key": "checkSimSwap", "product_id": "2412181343092489073", "old_capability_id": "2412172210044689028", "create_time": "23/6/2025 14:46:30", "update_time": "23/6/2025 14:46:30", "version": 1750661190190}, {"opgw_product_key": "DSsimswap", "opgw_capability_key": "retrieveSimSwap", "product_id": "2412181343092489073", "old_capability_id": "2412172210044439025", "create_time": "23/6/2025 14:46:30", "update_time": "23/6/2025 14:46:30", "version": 1750661190190}, {"opgw_product_key": "DSsimswapsubscriptio", "opgw_capability_key": "deletesimSwapSubscriptions", "product_id": "2412181410160599569", "old_capability_id": "2412172248426409144", "create_time": "23/6/2025 11:40:53", "update_time": "23/6/2025 11:40:53", "version": 1750650052546}, {"opgw_product_key": "DSsimswapsubscriptio", "opgw_capability_key": "getSimSwapSubscriptions", "product_id": "2412181410160599569", "old_capability_id": "2412172248426179141", "create_time": "23/6/2025 11:40:53", "update_time": "23/6/2025 11:40:53", "version": 1750650052546}, {"opgw_product_key": "DSsimswapsubscriptio", "opgw_capability_key": "simSwapSubscriptions", "product_id": "2412181410160599569", "old_capability_id": "2412172248425679135", "create_time": "23/6/2025 11:40:53", "update_time": "23/6/2025 11:40:53", "version": 1750650052546}, {"opgw_product_key": "DSsimswapsubscriptio", "opgw_capability_key": "SimSwapSubscriptionslist", "product_id": "2412181410160599569", "old_capability_id": "2412172248425949138", "create_time": "23/6/2025 11:40:53", "update_time": "23/6/2025 11:40:53", "version": 1750650052546}, {"opgw_product_key": "DSUserTrajectoryInqu", "opgw_capability_key": "CreateD1111", "product_id": "2412181459002169829", "old_capability_id": "2501171420507969037", "create_time": "23/6/2025 15:44:13", "update_time": "23/6/2025 15:44:13", "version": 1750664652847}, {"opgw_product_key": "DSUserTrajectoryInqu", "opgw_capability_key": "CreateDis399", "product_id": "2412181459002169829", "old_capability_id": "2501171159065419017", "create_time": "23/6/2025 15:44:13", "update_time": "23/6/2025 15:44:13", "version": 1750664652847}, {"opgw_product_key": "DSUserTrajectoryInqu", "opgw_capability_key": "CreateDis499", "product_id": "2412181459002169829", "old_capability_id": "2501171234034139024", "create_time": "23/6/2025 15:44:13", "update_time": "23/6/2025 15:44:13", "version": 1750664652847}, {"opgw_product_key": "DSUserTrajectoryInqu", "opgw_capability_key": "CreateDis600", "product_id": "2412181459002169829", "old_capability_id": "2501171426209969044", "create_time": "23/6/2025 15:44:13", "update_time": "23/6/2025 15:44:13", "version": 1750664652847}, {"opgw_product_key": "DSUserTrajectoryInqu", "opgw_capability_key": "Query1", "product_id": "2412181459002169829", "old_capability_id": "2501131754266049004", "create_time": "23/6/2025 15:44:13", "update_time": "23/6/2025 15:44:13", "version": 1750664652847}, {"opgw_product_key": "DSUserTrajectoryInqu", "opgw_capability_key": "queryDeviceLocation", "product_id": "2412181459002169829", "old_capability_id": "2412172210053249117", "create_time": "23/6/2025 15:44:13", "update_time": "23/6/2025 15:44:13", "version": 1750664652847}, {"opgw_product_key": "DSVoicenotification", "opgw_capability_key": "opgwhMUa1j", "product_id": "2412101619210429037", "old_capability_id": "2211241036509866171", "create_time": "18/12/2024 00:07:15", "update_time": "18/12/2024 00:07:15", "version": 1734451634529}, {"opgw_product_key": "DSVoicenotification", "opgw_capability_key": "sendVoiceNotification", "product_id": "2412101619210429037", "old_capability_id": "2412172210051549102", "create_time": "18/12/2024 00:07:15", "update_time": "18/12/2024 00:07:15", "version": 1734451634529}, {"opgw_product_key": "DSVoiceverification", "opgw_capability_key": "opgwhMUa1j", "product_id": "2412101616107119001", "old_capability_id": "2211241036509866171", "create_time": "18/12/2024 00:07:33", "update_time": "18/12/2024 00:07:33", "version": 1734451653474}, {"opgw_product_key": "DSVoiceverification", "opgw_capability_key": "sendVoiceVerification", "product_id": "2412101616107119001", "old_capability_id": "2412172210051209099", "create_time": "18/12/2024 00:07:33", "update_time": "18/12/2024 00:07:33", "version": 1734451653474}, {"opgw_product_key": "ElectronicFence", "opgw_capability_key": "addFenceTask", "product_id": "2206150930394509794", "old_capability_id": "2405241621180269292", "create_time": "11/12/2024 22:03:18", "update_time": "11/12/2024 22:03:18", "version": 1733925797981}, {"opgw_product_key": "ElectronicFence", "opgw_capability_key": "addLimitedRegionShape", "product_id": "2206150930394509794", "old_capability_id": "2405241636488249159", "create_time": "11/12/2024 22:03:18", "update_time": "11/12/2024 22:03:18", "version": 1733925797981}, {"opgw_product_key": "ElectronicFence", "opgw_capability_key": "addRegionCell", "product_id": "2206150930394509794", "old_capability_id": "2405241625031919141", "create_time": "11/12/2024 22:03:18", "update_time": "11/12/2024 22:03:18", "version": 1733925797981}, {"opgw_product_key": "ElectronicFence", "opgw_capability_key": "addRegionShape", "product_id": "2206150930394509794", "old_capability_id": "2405241609336039131", "create_time": "11/12/2024 22:03:18", "update_time": "11/12/2024 22:03:18", "version": 1733925797981}, {"opgw_product_key": "ElectronicFence", "opgw_capability_key": "deleteRegion", "product_id": "2206150930394509794", "old_capability_id": "2405241631045469148", "create_time": "11/12/2024 22:03:18", "update_time": "11/12/2024 22:03:18", "version": 1733925797981}, {"opgw_product_key": "ElectronicFence", "opgw_capability_key": "deleteSubscribe", "product_id": "2206150930394509794", "old_capability_id": "2405241611587669134", "create_time": "11/12/2024 22:03:18", "update_time": "11/12/2024 22:03:18", "version": 1733925797981}, {"opgw_product_key": "ElectronicFence", "opgw_capability_key": "enableCustomArea", "product_id": "2206150930394509794", "old_capability_id": "2405241634239349298", "create_time": "11/12/2024 22:03:18", "update_time": "11/12/2024 22:03:18", "version": 1733925797981}, {"opgw_product_key": "ElectronicFence", "opgw_capability_key": "enableSubscribe", "product_id": "2206150930394509794", "old_capability_id": "2405241635315989156", "create_time": "11/12/2024 22:03:18", "update_time": "11/12/2024 22:03:18", "version": 1733925797981}, {"opgw_product_key": "ElectronicFence", "opgw_capability_key": "getFencePushLog", "product_id": "2206150930394509794", "old_capability_id": "2405241638576859302", "create_time": "11/12/2024 22:03:18", "update_time": "11/12/2024 22:03:18", "version": 1733925797981}, {"opgw_product_key": "ElectronicFence", "opgw_capability_key": "getFenceTask", "product_id": "2206150930394509794", "old_capability_id": "2405241620330519288", "create_time": "11/12/2024 22:03:18", "update_time": "11/12/2024 22:03:18", "version": 1733925797981}, {"opgw_product_key": "ElectronicFence", "opgw_capability_key": "searchRegion", "product_id": "2206150930394509794", "old_capability_id": "2405241623214229137", "create_time": "11/12/2024 22:03:18", "update_time": "11/12/2024 22:03:18", "version": 1733925797981}, {"opgw_product_key": "ElectronicFence", "opgw_capability_key": "updateRegion", "product_id": "2206150930394509794", "old_capability_id": "2405241628140399145", "create_time": "11/12/2024 22:03:18", "update_time": "11/12/2024 22:03:18", "version": 1733925797981}, {"opgw_product_key": "Geofencing", "opgw_capability_key": "opgwNFXC", "product_id": "2411111944144559057", "old_capability_id": "2410231729666848071", "create_time": "23/6/2025 15:31:44", "update_time": "23/6/2025 15:31:44", "version": 1750663903704}, {"opgw_product_key": "getRegionalUserCount", "opgw_capability_key": "getRegionalUserCount", "product_id": "2505151616023339101", "old_capability_id": "2505151620167469141", "create_time": "15/5/2025 16:20:38", "update_time": "15/5/2025 16:20:38", "version": 1747297237611}, {"opgw_product_key": "getUserByCell", "opgw_capability_key": "getUserByCell", "product_id": "2409121735584889161", "old_capability_id": "2405241444183039085", "create_time": "23/6/2025 10:25:50", "update_time": "23/6/2025 10:25:50", "version": 1750645550163}, {"opgw_product_key": "HSSSigning", "opgw_capability_key": "getCFDetails", "product_id": "2303201518155153280", "old_capability_id": "2405241458319659247", "create_time": "26/11/2024 09:59:40", "update_time": "26/11/2024 09:59:40", "version": 1732586379974}, {"opgw_product_key": "IEMessageService", "opgw_capability_key": "opgw6Gc31T", "product_id": "2409271446047689290", "old_capability_id": "2407151511472319465", "create_time": "23/6/2025 10:29:06", "update_time": "23/6/2025 10:29:06", "version": 1750645745935}, {"opgw_product_key": "IEMessageService", "opgw_capability_key": "opgwCr4RBp", "product_id": "2409271446047689290", "old_capability_id": "2407151512341590115", "create_time": "23/6/2025 10:29:06", "update_time": "23/6/2025 10:29:06", "version": 1750645745935}, {"opgw_product_key": "IntMock1", "opgw_capability_key": "jkMockvv1", "product_id": "2503221203168419709", "old_capability_id": "2503221202087999703", "create_time": "22/3/2025 19:05:26", "update_time": "22/3/2025 19:05:26", "version": 1742641526498}, {"opgw_product_key": "IntMock1", "opgw_capability_key": "jkMockvv3", "product_id": "2503221203168419709", "old_capability_id": "2503221904031169798", "create_time": "22/3/2025 19:05:26", "update_time": "22/3/2025 19:05:26", "version": 1742641526498}, {"opgw_product_key": "IntMock2", "opgw_capability_key": "jkMockvv2", "product_id": "2503221204566609745", "old_capability_id": "2503221203073689706", "create_time": "22/3/2025 12:05:44", "update_time": "22/3/2025 12:05:44", "version": 1742616344281}, {"opgw_product_key": "IntPro1", "opgw_capability_key": "jkProvv1", "product_id": "2503221156475619631", "old_capability_id": "2503221152508419624", "create_time": "23/6/2025 10:57:56", "update_time": "23/6/2025 10:57:56", "version": 1750647475736}, {"opgw_product_key": "IntPro1", "opgw_capability_key": "jkProvv2", "product_id": "2503221156475619631", "old_capability_id": "2503221859557559790", "create_time": "23/6/2025 10:57:56", "update_time": "23/6/2025 10:57:56", "version": 1750647475736}, {"opgw_product_key": "IntPro1", "opgw_capability_key": "jkProvv6", "product_id": "2503221156475619631", "old_capability_id": "2503231046066409820", "create_time": "23/6/2025 10:57:56", "update_time": "23/6/2025 10:57:56", "version": 1750647475736}, {"opgw_product_key": "IntPro2", "opgw_capability_key": "jkvv02", "product_id": "2503221159008409667", "old_capability_id": "2503221154337599627", "create_time": "23/6/2025 10:58:39", "update_time": "23/6/2025 10:58:39", "version": 1750647518813}, {"opgw_product_key": "JinanFB", "opgw_capability_key": "jnqhd", "product_id": "2504091952267369140", "old_capability_id": "2504091702545779129", "create_time": "9/4/2025 20:05:00", "update_time": "9/4/2025 20:05:00", "version": 1744200299578}, {"opgw_product_key": "JINCapacityys", "opgw_capability_key": "jkProjin1", "product_id": "2503261431254569047", "old_capability_id": "2503261425506319034", "create_time": "27/6/2025 09:38:03", "update_time": "27/6/2025 09:38:03", "version": 1750988283239}, {"opgw_product_key": "JINCapacityys", "opgw_capability_key": "jkProjin2", "product_id": "2503261431254569047", "old_capability_id": "2503261425506759037", "create_time": "27/6/2025 09:38:03", "update_time": "27/6/2025 09:38:03", "version": 1750988283239}, {"opgw_product_key": "jinceCode", "opgw_capability_key": "jin<PERSON>", "product_id": "2410181407478999080", "old_capability_id": "2410301910508449001", "create_time": "10/1/2025 14:58:06", "update_time": "10/1/2025 14:58:06", "version": 1736492286482}, {"opgw_product_key": "jinceCode", "opgw_capability_key": "opgwgKSEER", "product_id": "2410181407478999080", "old_capability_id": "2407151428329839455", "create_time": "10/1/2025 14:58:06", "update_time": "10/1/2025 14:58:06", "version": 1736492286482}, {"opgw_product_key": "jinceCode", "opgw_capability_key": "wmzTestCallBack1", "product_id": "2410181407478999080", "old_capability_id": "2410311838214309001", "create_time": "10/1/2025 14:58:06", "update_time": "10/1/2025 14:58:06", "version": 1736492286482}, {"opgw_product_key": "JINOIDCAuthorizati", "opgw_capability_key": "JINTjosntest", "product_id": "2503131438586929098", "old_capability_id": "2503131142259109020", "create_time": "13/3/2025 15:18:53", "update_time": "13/3/2025 15:18:53", "version": 1741850332522}, {"opgw_product_key": "jinput01", "opgw_capability_key": "jin<PERSON>", "product_id": "2410301911330589003", "old_capability_id": "2410301910508449001", "create_time": "6/3/2025 15:51:15", "update_time": "6/3/2025 15:51:15", "version": 1741247475051}, {"opgw_product_key": "jintao01", "opgw_capability_key": "opgw8BPFXo", "product_id": "2407151620132909151", "old_capability_id": "2407151427502249451", "create_time": "11/11/2024 17:11:50", "update_time": "11/11/2024 17:11:50", "version": 1731316310398}, {"opgw_product_key": "jintao01", "opgw_capability_key": "opgwwm51JM", "product_id": "2407151620132909151", "old_capability_id": "2407151511271119462", "create_time": "11/11/2024 17:11:50", "update_time": "11/11/2024 17:11:50", "version": 1731316310398}, {"opgw_product_key": "jintao02", "opgw_capability_key": "opgwV5xiE6", "product_id": "2407161417182369004", "old_capability_id": "2407151520022690131", "create_time": "23/6/2025 10:50:31", "update_time": "23/6/2025 10:50:31", "version": 1750647031157}, {"opgw_product_key": "jintaotest02", "opgw_capability_key": "opgw9lLBHT", "product_id": "2410301621577019003", "old_capability_id": "2407151511088480097", "create_time": "23/6/2025 11:04:10", "update_time": "23/6/2025 11:04:10", "version": 1750647849704}, {"opgw_product_key": "jintaotest02", "opgw_capability_key": "opgwgKSEER", "product_id": "2410301621577019003", "old_capability_id": "2407151428329839455", "create_time": "23/6/2025 11:04:10", "update_time": "23/6/2025 11:04:10", "version": 1750647849704}, {"opgw_product_key": "JINtest", "opgw_capability_key": "opgwgKSEER", "product_id": "2411071416425239011", "old_capability_id": "2407151428329839455", "create_time": "11/11/2024 17:03:19", "update_time": "11/11/2024 17:03:19", "version": 1731315799331}, {"opgw_product_key": "jnqhdnl", "opgw_capability_key": "jkMockvv1", "product_id": "2504091553192209074", "old_capability_id": "2503221202087999703", "create_time": "9/4/2025 15:55:30", "update_time": "9/4/2025 15:55:30", "version": 1744185329806}, {"opgw_product_key": "jsd", "opgw_capability_key": "wmzTestImport1", "product_id": "2406051614505709083", "old_capability_id": "2410301948386859001", "create_time": "4/11/2024 16:55:09", "update_time": "4/11/2024 16:55:09", "version": 1730710509195}, {"opgw_product_key": "jzswb", "opgw_capability_key": "opgw6Xzf8C", "product_id": "2406051643536489127", "old_capability_id": "2405272144290119004", "create_time": "9/6/2024 18:21:29", "update_time": "9/6/2024 18:21:29", "version": 1717928488967}, {"opgw_product_key": "kdgnb", "opgw_capability_key": "opgw6Xzf8C", "product_id": "2406051650368659201", "old_capability_id": "2405272144290119004", "create_time": "15/7/2024 11:46:11", "update_time": "15/7/2024 11:46:11", "version": 1721015170576}, {"opgw_product_key": "kdgqt", "opgw_capability_key": "opgw6Xzf8C", "product_id": "2406051646359689192", "old_capability_id": "2405272144290119004", "create_time": "15/7/2024 11:50:00", "update_time": "15/7/2024 11:50:00", "version": 1721015400401}, {"opgw_product_key": "kdgqt", "opgw_capability_key": "opgwJtG2X0", "product_id": "2406051646359689192", "old_capability_id": "2211241111269476245", "create_time": "15/7/2024 11:50:00", "update_time": "15/7/2024 11:50:00", "version": 1721015400401}, {"opgw_product_key": "kdgwb", "opgw_capability_key": "opgw6Xzf8C", "product_id": "2406051648596219173", "old_capability_id": "2405272144290119004", "create_time": "9/6/2024 18:37:31", "update_time": "9/6/2024 18:37:31", "version": 1717929451046}, {"opgw_product_key": "KKK0605", "opgw_capability_key": "opgw6Xzf8C", "product_id": "2406051624101449089", "old_capability_id": "2405272144290119004", "create_time": "5/6/2024 17:33:43", "update_time": "5/6/2024 17:33:43", "version": 1717580023008}, {"opgw_product_key": "KKK0605", "opgw_capability_key": "updateRule", "product_id": "2406051624101449089", "old_capability_id": "2405241717436639327", "create_time": "5/6/2024 17:33:43", "update_time": "5/6/2024 17:33:43", "version": 1717580023008}, {"opgw_product_key": "KKK999", "opgw_capability_key": "opgw6Xzf8C", "product_id": "2406051645594979132", "old_capability_id": "2405272144290119004", "create_time": "9/6/2024 17:57:53", "update_time": "9/6/2024 17:57:53", "version": 1717927073494}, {"opgw_product_key": "LawRectify", "opgw_capability_key": "add<PERSON><PERSON>", "product_id": "2303311439161595378", "old_capability_id": "2405241714480809188", "create_time": "26/11/2024 10:55:33", "update_time": "26/11/2024 10:55:33", "version": 1732589733080}, {"opgw_product_key": "LawRectify", "opgw_capability_key": "createFence", "product_id": "2303311439161595378", "old_capability_id": "2405241722419889195", "create_time": "26/11/2024 10:55:33", "update_time": "26/11/2024 10:55:33", "version": 1732589733080}, {"opgw_product_key": "LawRectify", "opgw_capability_key": "deletePerson", "product_id": "2303311439161595378", "old_capability_id": "2405241709376069315", "create_time": "26/11/2024 10:55:33", "update_time": "26/11/2024 10:55:33", "version": 1732589733080}, {"opgw_product_key": "LawRectify", "opgw_capability_key": "fenceInfo", "product_id": "2303311439161595378", "old_capability_id": "2405241724462979336", "create_time": "26/11/2024 10:55:33", "update_time": "26/11/2024 10:55:33", "version": 1732589733080}, {"opgw_product_key": "LawRectify", "opgw_capability_key": "showRule", "product_id": "2303311439161595378", "old_capability_id": "2405241716370179192", "create_time": "26/11/2024 10:55:33", "update_time": "26/11/2024 10:55:33", "version": 1732589733080}, {"opgw_product_key": "LawRectify", "opgw_capability_key": "switchFence", "product_id": "2303311439161595378", "old_capability_id": "2405241721146509331", "create_time": "26/11/2024 10:55:33", "update_time": "26/11/2024 10:55:33", "version": 1732589733080}, {"opgw_product_key": "LawRectify", "opgw_capability_key": "<PERSON><PERSON><PERSON>", "product_id": "2303311439161595378", "old_capability_id": "2405241713038049322", "create_time": "26/11/2024 10:55:33", "update_time": "26/11/2024 10:55:33", "version": 1732589733080}, {"opgw_product_key": "LawRectify", "opgw_capability_key": "update<PERSON><PERSON>", "product_id": "2303311439161595378", "old_capability_id": "2405241712065669318", "create_time": "26/11/2024 10:55:33", "update_time": "26/11/2024 10:55:33", "version": 1732589733080}, {"opgw_product_key": "LawRectify", "opgw_capability_key": "updateRule", "product_id": "2303311439161595378", "old_capability_id": "2405241717436639327", "create_time": "26/11/2024 10:55:33", "update_time": "26/11/2024 10:55:33", "version": 1732589733080}, {"opgw_product_key": "listNetwork", "opgw_capability_key": "getCircuitRouteInfo", "product_id": "2503211758312109560", "old_capability_id": "2503211833520949610", "create_time": "23/6/2025 10:21:43", "update_time": "23/6/2025 10:21:43", "version": 1750645302908}, {"opgw_product_key": "listNetwork", "opgw_capability_key": "historyMonit<PERSON><PERSON>y", "product_id": "2503211758312109560", "old_capability_id": "2503211823584509604", "create_time": "23/6/2025 10:21:43", "update_time": "23/6/2025 10:21:43", "version": 1750645302908}, {"opgw_product_key": "listNetwork", "opgw_capability_key": "listHistoryTimeAlar", "product_id": "2503211758312109560", "old_capability_id": "2503211751298019557", "create_time": "23/6/2025 10:21:43", "update_time": "23/6/2025 10:21:43", "version": 1750645302908}, {"opgw_product_key": "listNetwork", "opgw_capability_key": "listKpiData", "product_id": "2503211758312109560", "old_capability_id": "2503211830187449607", "create_time": "23/6/2025 10:21:43", "update_time": "23/6/2025 10:21:43", "version": 1750645302908}, {"opgw_product_key": "listNetwork", "opgw_capability_key": "listRealTimeAlarm", "product_id": "2503211758312109560", "old_capability_id": "2503211747521319551", "create_time": "23/6/2025 10:21:43", "update_time": "23/6/2025 10:21:43", "version": 1750645302908}, {"opgw_product_key": "listNetwork", "opgw_capability_key": "listRealTimeMonitorD", "product_id": "2503211758312109560", "old_capability_id": "2503211749474929554", "create_time": "23/6/2025 10:21:43", "update_time": "23/6/2025 10:21:43", "version": 1750645302908}, {"opgw_product_key": "LocalJINTAO", "opgw_capability_key": "locationquery", "product_id": "2503202022446159506", "old_capability_id": "2503202020144389502", "create_time": "23/6/2025 09:58:28", "update_time": "23/6/2025 09:58:28", "version": 1750643907522}, {"opgw_product_key": "<PERSON><PERSON><PERSON><PERSON>", "opgw_capability_key": "getUserLocationHash", "product_id": "2506131633169909036", "old_capability_id": "2506131728077879118", "create_time": "16/6/2025 23:39:29", "update_time": "16/6/2025 23:39:29", "version": 1750088369300}, {"opgw_product_key": "LocationVerification", "opgw_capability_key": "verifyUserLocation", "product_id": "2505151620186529144", "old_capability_id": "2505151618249199138", "create_time": "18/7/2025 17:38:26", "update_time": "18/7/2025 17:38:26", "version": 1752831505721}, {"opgw_product_key": "Mecinstance", "opgw_capability_key": "AttachDisk", "product_id": "2501101623189899005", "old_capability_id": "2501101538039279003", "create_time": "10/2/2025 14:35:58", "update_time": "10/2/2025 14:35:58", "version": 1739169358433}, {"opgw_product_key": "Mecinstance", "opgw_capability_key": "DescribeInstances", "product_id": "2501101623189899005", "old_capability_id": "2501101623090809001", "create_time": "10/2/2025 14:35:58", "update_time": "10/2/2025 14:35:58", "version": 1739169358433}, {"opgw_product_key": "mockCallforwarding", "opgw_capability_key": "mockgetCallForward", "product_id": "2506121405192789858", "old_capability_id": "2506121402116549852", "create_time": "18/7/2025 17:28:37", "update_time": "18/7/2025 17:28:37", "version": 1752830917495}, {"opgw_product_key": "mockCallforwarding", "opgw_capability_key": "mockQueryUncondit", "product_id": "2506121405192789858", "old_capability_id": "2506121405037339855", "create_time": "18/7/2025 17:28:37", "update_time": "18/7/2025 17:28:37", "version": 1752830917495}, {"opgw_product_key": "mockDeviceIdentifier", "opgw_capability_key": "mockGetUserDevice", "product_id": "2506121418235329944", "old_capability_id": "2506121416073039936", "create_time": "18/7/2025 17:25:47", "update_time": "18/7/2025 17:25:47", "version": 1752830747343}, {"opgw_product_key": "mockDeviceIdentifier", "opgw_capability_key": "mockGetUserIMEI", "product_id": "2506121418235329944", "old_capability_id": "2506121417546539940", "create_time": "18/7/2025 17:25:47", "update_time": "18/7/2025 17:25:47", "version": 1752830747343}, {"opgw_product_key": "mockLoc<PERSON><PERSON><PERSON><PERSON>", "opgw_capability_key": "mockgetUserLocationG", "product_id": "2506131719144369078", "old_capability_id": "2506131717391859074", "create_time": "18/7/2025 17:23:16", "update_time": "18/7/2025 17:23:16", "version": 1752830595877}, {"opgw_product_key": "mockLocationVerifica", "opgw_capability_key": "mockVerifyUserLocat", "product_id": "2506121354446549811", "old_capability_id": "2506121353582009807", "create_time": "23/6/2025 14:14:44", "update_time": "23/6/2025 14:14:44", "version": 1750659283838}, {"opgw_product_key": "mockNumberVerificat", "opgw_capability_key": "mockGetMobilePhone", "product_id": "2506121348420569771", "old_capability_id": "2506121344130999759", "create_time": "18/7/2025 17:30:22", "update_time": "18/7/2025 17:30:22", "version": 1752831022195}, {"opgw_product_key": "mockNumberVerificat", "opgw_capability_key": "mockMobilePhone", "product_id": "2506121348420569771", "old_capability_id": "2506121342434789756", "create_time": "18/7/2025 17:30:22", "update_time": "18/7/2025 17:30:22", "version": 1752831022195}, {"opgw_product_key": "mockOTPvalidation", "opgw_capability_key": "mockGetDscp", "product_id": "2506121434015760019", "old_capability_id": "2506121440369190058", "create_time": "12/6/2025 14:43:40", "update_time": "12/6/2025 14:43:40", "version": 1749710620457}, {"opgw_product_key": "mockOTPvalidation", "opgw_capability_key": "mockGetValidateCode", "product_id": "2506121434015760019", "old_capability_id": "2506121443187530061", "create_time": "12/6/2025 14:43:40", "update_time": "12/6/2025 14:43:40", "version": 1749710620457}, {"opgw_product_key": "mockRegionUserCount", "opgw_capability_key": "mockRegionUserCount", "product_id": "2506121430236949983", "old_capability_id": "2506121429013379980", "create_time": "13/6/2025 19:22:01", "update_time": "13/6/2025 19:22:01", "version": 1749813721236}, {"opgw_product_key": "mockShortMessageServ", "opgw_capability_key": "mockSendMessages", "product_id": "2506121411274049900", "old_capability_id": "2506131734305399124", "create_time": "18/7/2025 17:48:06", "update_time": "18/7/2025 17:48:06", "version": 1752832085637}, {"opgw_product_key": "mockVoiceCode", "opgw_capability_key": "mockVoiceCode", "product_id": "2506121445437850067", "old_capability_id": "2506121448504570103", "create_time": "13/6/2025 19:15:09", "update_time": "13/6/2025 19:15:09", "version": 1749813308639}, {"opgw_product_key": "mockVoiceNotifySw", "opgw_capability_key": "mockVoiceNotifySw", "product_id": "2506121121514439714", "old_capability_id": "2506121126550369750", "create_time": "13/6/2025 18:20:17", "update_time": "13/6/2025 18:20:17", "version": 1749810016596}, {"opgw_product_key": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "opgw_capability_key": "jkProjin1", "product_id": "2503261743272919004", "old_capability_id": "2503261425506319034", "create_time": "23/6/2025 11:04:58", "update_time": "23/6/2025 11:04:58", "version": 1750647897736}, {"opgw_product_key": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "opgw_capability_key": "jkProjin2", "product_id": "2503261743272919004", "old_capability_id": "2503261425506759037", "create_time": "23/6/2025 11:04:58", "update_time": "23/6/2025 11:04:58", "version": 1750647897736}, {"opgw_product_key": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "opgw_capability_key": "jkProjin3", "product_id": "2503261743272919004", "old_capability_id": "2503261425507119040", "create_time": "23/6/2025 11:04:58", "update_time": "23/6/2025 11:04:58", "version": 1750647897736}, {"opgw_product_key": "nlbtest", "opgw_capability_key": "sksendcode", "product_id": "2506111421576159549", "old_capability_id": "2505292216186539348", "create_time": "16/6/2025 17:42:09", "update_time": "16/6/2025 17:42:09", "version": 1750066928647}, {"opgw_product_key": "nlbtest", "opgw_capability_key": "skverify", "product_id": "2506111421576159549", "old_capability_id": "2506031205597039428", "create_time": "16/6/2025 17:42:09", "update_time": "16/6/2025 17:42:09", "version": 1750066928647}, {"opgw_product_key": "<PERSON><PERSON><PERSON>", "opgw_capability_key": "achenceshi1", "product_id": "2502071721562869002", "old_capability_id": "2502061731365159001", "create_time": "23/6/2025 11:27:35", "update_time": "23/6/2025 11:27:35", "version": 1750649254503}, {"opgw_product_key": "Nonlinkage", "opgw_capability_key": "callForwardings", "product_id": "2412161042390889032", "old_capability_id": "2412131134499369001", "create_time": "23/6/2025 14:33:08", "update_time": "23/6/2025 14:33:08", "version": 1750660387615}, {"opgw_product_key": "Nonlinkage", "opgw_capability_key": "checkNetworkQuality", "product_id": "2412161042390889032", "old_capability_id": "2412172210047479061", "create_time": "23/6/2025 14:33:08", "update_time": "23/6/2025 14:33:08", "version": 1750660387615}, {"opgw_product_key": "Nonlinkage", "opgw_capability_key": "checkSimSwap", "product_id": "2412161042390889032", "old_capability_id": "2412172210044689028", "create_time": "23/6/2025 14:33:08", "update_time": "23/6/2025 14:33:08", "version": 1750660387615}, {"opgw_product_key": "Nonlinkage", "opgw_capability_key": "createApplicationProfiles", "product_id": "2412161042390889032", "old_capability_id": "2412172210046399049", "create_time": "23/6/2025 14:33:08", "update_time": "23/6/2025 14:33:08", "version": 1750660387615}, {"opgw_product_key": "Nonlinkage", "opgw_capability_key": "deleteApplicationProfiles", "product_id": "2412161042390889032", "old_capability_id": "2412172210047169058", "create_time": "23/6/2025 14:33:08", "update_time": "23/6/2025 14:33:08", "version": 1750660387615}, {"opgw_product_key": "Nonlinkage", "opgw_capability_key": "getAllProfiles", "product_id": "2412161042390889032", "old_capability_id": "2412172210049379078", "create_time": "23/6/2025 14:33:08", "update_time": "23/6/2025 14:33:08", "version": 1750660387615}, {"opgw_product_key": "Nonlinkage", "opgw_capability_key": "getApplicationProfiles", "product_id": "2412161042390889032", "old_capability_id": "2412172210046929055", "create_time": "23/6/2025 14:33:08", "update_time": "23/6/2025 14:33:08", "version": 1750660387615}, {"opgw_product_key": "Nonlinkage", "opgw_capability_key": "getCallForwardings", "product_id": "2412161042390889032", "old_capability_id": "2412172210051819105", "create_time": "23/6/2025 14:33:08", "update_time": "23/6/2025 14:33:08", "version": 1750660387615}, {"opgw_product_key": "Nonlinkage", "opgw_capability_key": "getDeviceCount", "product_id": "2412161042390889032", "old_capability_id": "2412171932106579001", "create_time": "23/6/2025 14:33:08", "update_time": "23/6/2025 14:33:08", "version": 1750660387615}, {"opgw_product_key": "Nonlinkage", "opgw_capability_key": "getDeviceType", "product_id": "2412161042390889032", "old_capability_id": "2412161900195589001", "create_time": "23/6/2025 14:33:08", "update_time": "23/6/2025 14:33:08", "version": 1750660387615}, {"opgw_product_key": "Nonlinkage", "opgw_capability_key": "getHighSpeedRealtimeC", "product_id": "2412161042390889032", "old_capability_id": "2412161038197989026", "create_time": "23/6/2025 14:33:08", "update_time": "23/6/2025 14:33:08", "version": 1750660387615}, {"opgw_product_key": "Nonlinkage", "opgw_capability_key": "getLocationFeatureQue", "product_id": "2412161042390889032", "old_capability_id": "2412131134548209004", "create_time": "23/6/2025 14:33:08", "update_time": "23/6/2025 14:33:08", "version": 1750660387615}, {"opgw_product_key": "Nonlinkage", "opgw_capability_key": "getNumbercarrierattri", "product_id": "2412161042390889032", "old_capability_id": "2412172210052369111", "create_time": "23/6/2025 14:33:08", "update_time": "23/6/2025 14:33:08", "version": 1750660387615}, {"opgw_product_key": "Nonlinkage", "opgw_capability_key": "getNumberRiskIdentifi", "product_id": "2412161042390889032", "old_capability_id": "2412161038197039020", "create_time": "23/6/2025 14:33:08", "update_time": "23/6/2025 14:33:08", "version": 1750660387615}, {"opgw_product_key": "Nonlinkage", "opgw_capability_key": "getProfilesByName", "product_id": "2412161042390889032", "old_capability_id": "2412172210049609081", "create_time": "23/6/2025 14:33:08", "update_time": "23/6/2025 14:33:08", "version": 1750660387615}, {"opgw_product_key": "Nonlinkage", "opgw_capability_key": "getScalperdeterminati", "product_id": "2412161042390889032", "old_capability_id": "2412161038197399023", "create_time": "23/6/2025 14:33:08", "update_time": "23/6/2025 14:33:08", "version": 1750660387615}, {"opgw_product_key": "Nonlinkage", "opgw_capability_key": "getSNumbercarrierattri", "product_id": "2412161042390889032", "old_capability_id": "2412161038196699017", "create_time": "23/6/2025 14:33:08", "update_time": "23/6/2025 14:33:08", "version": 1750660387615}, {"opgw_product_key": "Nonlinkage", "opgw_capability_key": "locationRetrieval", "product_id": "2412161042390889032", "old_capability_id": "2412172210049149075", "create_time": "23/6/2025 14:33:08", "update_time": "23/6/2025 14:33:08", "version": 1750660387615}, {"opgw_product_key": "Nonlinkage", "opgw_capability_key": "locationVerificat", "product_id": "2412161042390889032", "old_capability_id": "2412172019453309005", "create_time": "23/6/2025 14:33:08", "update_time": "23/6/2025 14:33:08", "version": 1750660387615}, {"opgw_product_key": "Nonlinkage", "opgw_capability_key": "obtainDeviceInformation", "product_id": "2412161042390889032", "old_capability_id": "2412172210052939114", "create_time": "23/6/2025 14:33:08", "update_time": "23/6/2025 14:33:08", "version": 1750660387615}, {"opgw_product_key": "Nonlinkage", "opgw_capability_key": "queryCityCode", "product_id": "2412161042390889032", "old_capability_id": "2412161038198429029", "create_time": "23/6/2025 14:33:08", "update_time": "23/6/2025 14:33:08", "version": 1750660387615}, {"opgw_product_key": "Nonlinkage", "opgw_capability_key": "queryDeviceInformation", "product_id": "2412161042390889032", "old_capability_id": "2412161345420639130", "create_time": "23/6/2025 14:33:08", "update_time": "23/6/2025 14:33:08", "version": 1750660387615}, {"opgw_product_key": "Nonlinkage", "opgw_capability_key": "queryDeviceLocation", "product_id": "2412161042390889032", "old_capability_id": "2412172210053249117", "create_time": "23/6/2025 14:33:08", "update_time": "23/6/2025 14:33:08", "version": 1750660387615}, {"opgw_product_key": "Nonlinkage", "opgw_capability_key": "queryUnconditionalCall", "product_id": "2412161042390889032", "old_capability_id": "2412172210052089108", "create_time": "23/6/2025 14:33:08", "update_time": "23/6/2025 14:33:08", "version": 1750660387615}, {"opgw_product_key": "Nonlinkage", "opgw_capability_key": "retrieveReachabilityStatus", "product_id": "2412161042390889032", "old_capability_id": "2412172210044929031", "create_time": "23/6/2025 14:33:08", "update_time": "23/6/2025 14:33:08", "version": 1750660387615}, {"opgw_product_key": "Nonlinkage", "opgw_capability_key": "retrieveRoamingStatus", "product_id": "2412161042390889032", "old_capability_id": "2412172210045189034", "create_time": "23/6/2025 14:33:08", "update_time": "23/6/2025 14:33:08", "version": 1750660387615}, {"opgw_product_key": "Nonlinkage", "opgw_capability_key": "retrieveSimSwap", "product_id": "2412161042390889032", "old_capability_id": "2412172210044439025", "create_time": "23/6/2025 14:33:08", "update_time": "23/6/2025 14:33:08", "version": 1750660387615}, {"opgw_product_key": "Nonlinkage", "opgw_capability_key": "sendVoiceNotification", "product_id": "2412161042390889032", "old_capability_id": "2412172210051549102", "create_time": "23/6/2025 14:33:08", "update_time": "23/6/2025 14:33:08", "version": 1750660387615}, {"opgw_product_key": "Nonlinkage", "opgw_capability_key": "sendVoiceVerification", "product_id": "2412161042390889032", "old_capability_id": "2412172210051209099", "create_time": "23/6/2025 14:33:08", "update_time": "23/6/2025 14:33:08", "version": 1750660387615}, {"opgw_product_key": "Nonlinkage", "opgw_capability_key": "shortMessage", "product_id": "2412161042390889032", "old_capability_id": "2412172210047739064", "create_time": "23/6/2025 14:33:08", "update_time": "23/6/2025 14:33:08", "version": 1750660387615}, {"opgw_product_key": "Nonlinkage", "opgw_capability_key": "uncondlCallForward", "product_id": "2412161042390889032", "old_capability_id": "2412161038195899014", "create_time": "23/6/2025 14:33:08", "update_time": "23/6/2025 14:33:08", "version": 1750660387615}, {"opgw_product_key": "Nonlinkage", "opgw_capability_key": "updateApplicationProfiles", "product_id": "2412161042390889032", "old_capability_id": "2412172210046659052", "create_time": "23/6/2025 14:33:08", "update_time": "23/6/2025 14:33:08", "version": 1750660387615}, {"opgw_product_key": "NumberVerification", "opgw_capability_key": "getMobilePhone", "product_id": "2505151607121479057", "old_capability_id": "2505151606424229054", "create_time": "20/6/2025 02:33:09", "update_time": "20/6/2025 02:33:09", "version": 1750357989463}, {"opgw_product_key": "NumberVerification", "opgw_capability_key": "verifyMobilePhone", "product_id": "2505151607121479057", "old_capability_id": "2505151603496989049", "create_time": "20/6/2025 02:33:09", "update_time": "20/6/2025 02:33:09", "version": 1750357989463}, {"opgw_product_key": "OIDCJINTAO", "opgw_capability_key": "CreateDisk6", "product_id": "2503131353488389061", "old_capability_id": "2501161638451069041", "create_time": "23/6/2025 10:59:36", "update_time": "23/6/2025 10:59:36", "version": 1750647575526}, {"opgw_product_key": "OIDCJINTAO", "opgw_capability_key": "JINTjosn", "product_id": "2503131353488389061", "old_capability_id": "2503131140339849014", "create_time": "23/6/2025 10:59:36", "update_time": "23/6/2025 10:59:36", "version": 1750647575526}, {"opgw_product_key": "opgwJtG2X0", "opgw_capability_key": "opgw6Xzf8C", "product_id": "2406051523292449031", "old_capability_id": "2405272144290119004", "create_time": "26/11/2024 09:59:01", "update_time": "26/11/2024 09:59:01", "version": 1732586341130}, {"opgw_product_key": "opgwNZC", "opgw_capability_key": "opgwCJHM", "product_id": "2411041715251849038", "old_capability_id": "2410231729666997080", "create_time": "23/6/2025 15:43:14", "update_time": "23/6/2025 15:43:14", "version": 1750664593861}, {"opgw_product_key": "opgwNZC", "opgw_capability_key": "opgwCZCS", "product_id": "2411041715251849038", "old_capability_id": "2410231729666952070", "create_time": "23/6/2025 15:43:14", "update_time": "23/6/2025 15:43:14", "version": 1750664593861}, {"opgw_product_key": "opgwNZC", "opgw_capability_key": "opgwDSBM", "product_id": "2411041715251849038", "old_capability_id": "2410231729666815077", "create_time": "23/6/2025 15:43:14", "update_time": "23/6/2025 15:43:14", "version": 1750664593861}, {"opgw_product_key": "opgwNZC", "opgw_capability_key": "opgwECQL", "product_id": "2411041715251849038", "old_capability_id": "2410231729666924080", "create_time": "23/6/2025 15:43:14", "update_time": "23/6/2025 15:43:14", "version": 1750664593861}, {"opgw_product_key": "opgwNZC", "opgw_capability_key": "opgwGHSY", "product_id": "2411041715251849038", "old_capability_id": "2410231729666937070", "create_time": "23/6/2025 15:43:14", "update_time": "23/6/2025 15:43:14", "version": 1750664593861}, {"opgw_product_key": "opgwNZC", "opgw_capability_key": "opgwGHZT", "product_id": "2411041715251849038", "old_capability_id": "2410231729666943070", "create_time": "23/6/2025 15:43:14", "update_time": "23/6/2025 15:43:14", "version": 1750664593861}, {"opgw_product_key": "opgwNZC", "opgw_capability_key": "opgwGHZW", "product_id": "2411041715251849038", "old_capability_id": "2410231729666968070", "create_time": "23/6/2025 15:43:14", "update_time": "23/6/2025 15:43:14", "version": 1750664593861}, {"opgw_product_key": "opgwNZC", "opgw_capability_key": "opgwGZD", "product_id": "2411041715251849038", "old_capability_id": "2410231729666806070", "create_time": "23/6/2025 15:43:14", "update_time": "23/6/2025 15:43:14", "version": 1750664593861}, {"opgw_product_key": "opgwNZC", "opgw_capability_key": "opgwHMDC", "product_id": "2411041715251849038", "old_capability_id": "2410231729666899072", "create_time": "23/6/2025 15:43:14", "update_time": "23/6/2025 15:43:14", "version": 1750664593861}, {"opgw_product_key": "opgwNZC", "opgw_capability_key": "opgwHYSF", "product_id": "2411041715251849038", "old_capability_id": "2410231729666978070", "create_time": "23/6/2025 15:43:14", "update_time": "23/6/2025 15:43:14", "version": 1750664593861}, {"opgw_product_key": "opgwNZC", "opgw_capability_key": "opgwJKPF", "product_id": "2411041715251849038", "old_capability_id": "2410231729666888078", "create_time": "23/6/2025 15:43:14", "update_time": "23/6/2025 15:43:14", "version": 1750664593861}, {"opgw_product_key": "opgwNZC", "opgw_capability_key": "opgwJTSF", "product_id": "2411041715251849038", "old_capability_id": "2410231729666986071", "create_time": "23/6/2025 15:43:14", "update_time": "23/6/2025 15:43:14", "version": 1750664593861}, {"opgw_product_key": "opgwNZC", "opgw_capability_key": "opgwJZD", "product_id": "2411041715251849038", "old_capability_id": "2410231729666800072", "create_time": "23/6/2025 15:43:14", "update_time": "23/6/2025 15:43:14", "version": 1750664593861}, {"opgw_product_key": "opgwNZC", "opgw_capability_key": "opgwLSWZ", "product_id": "2411041715251849038", "old_capability_id": "2410231729666832071", "create_time": "23/6/2025 15:43:14", "update_time": "23/6/2025 15:43:14", "version": 1750664593861}, {"opgw_product_key": "opgwNZC", "opgw_capability_key": "opgwNFXC", "product_id": "2411041715251849038", "old_capability_id": "2410231729666848071", "create_time": "23/6/2025 15:43:14", "update_time": "23/6/2025 15:43:14", "version": 1750664593861}, {"opgw_product_key": "opgwNZC", "opgw_capability_key": "opgwNGSC", "product_id": "2411041715251849038", "old_capability_id": "2410231729666722078", "create_time": "23/6/2025 15:43:14", "update_time": "23/6/2025 15:43:14", "version": 1750664593861}, {"opgw_product_key": "opgwNZC", "opgw_capability_key": "opgwNSMC", "product_id": "2411041715251849038", "old_capability_id": "2410231729666860069", "create_time": "23/6/2025 15:43:14", "update_time": "23/6/2025 15:43:14", "version": 1750664593861}, {"opgw_product_key": "opgwNZC", "opgw_capability_key": "opgwNXC", "product_id": "2411041715251849038", "old_capability_id": "2410231729666713070", "create_time": "23/6/2025 15:43:14", "update_time": "23/6/2025 15:43:14", "version": 1750664593861}, {"opgw_product_key": "opgwNZC", "opgw_capability_key": "opgwNZC", "product_id": "2411041715251849038", "old_capability_id": "2410231729666701076", "create_time": "23/6/2025 15:43:14", "update_time": "23/6/2025 15:43:14", "version": 1750664593861}, {"opgw_product_key": "opgwNZC", "opgw_capability_key": "opgwNZTS", "product_id": "2411041715251849038", "old_capability_id": "2410231729666728070", "create_time": "23/6/2025 15:43:14", "update_time": "23/6/2025 15:43:14", "version": 1750664593861}, {"opgw_product_key": "opgwNZC", "opgw_capability_key": "opgwRHSF", "product_id": "2411041715251849038", "old_capability_id": "2410231729666992069", "create_time": "23/6/2025 15:43:14", "update_time": "23/6/2025 15:43:14", "version": 1750664593861}, {"opgw_product_key": "opgwNZC", "opgw_capability_key": "opgwSSCS", "product_id": "2411041715251849038", "old_capability_id": "2410231729666823077", "create_time": "23/6/2025 15:43:14", "update_time": "23/6/2025 15:43:14", "version": 1750664593861}, {"opgw_product_key": "opgwNZC", "opgw_capability_key": "opgwSSP", "product_id": "2411041715251849038", "old_capability_id": "2410231729666793073", "create_time": "23/6/2025 15:43:14", "update_time": "23/6/2025 15:43:14", "version": 1750664593861}, {"opgw_product_key": "opgwNZC", "opgw_capability_key": "opgwsySyZ", "product_id": "2411041715251849038", "old_capability_id": "2410231729650739968", "create_time": "23/6/2025 15:43:14", "update_time": "23/6/2025 15:43:14", "version": 1750664593861}, {"opgw_product_key": "opgwNZC", "opgw_capability_key": "opgwUNS", "product_id": "2411041715251849038", "old_capability_id": "2410231729666854075", "create_time": "23/6/2025 15:43:14", "update_time": "23/6/2025 15:43:14", "version": 1750664593861}, {"opgw_product_key": "opgwNZC", "opgw_capability_key": "opgwWZDB", "product_id": "2411041715251849038", "old_capability_id": "2410231729666840077", "create_time": "23/6/2025 15:43:14", "update_time": "23/6/2025 15:43:14", "version": 1750664593861}, {"opgw_product_key": "opgwNZC", "opgw_capability_key": "opgwYHYZ", "product_id": "2411041715251849038", "old_capability_id": "2410231729666959070", "create_time": "23/6/2025 15:43:14", "update_time": "23/6/2025 15:43:14", "version": 1750664593861}, {"opgw_product_key": "opgwNZC", "opgw_capability_key": "opgwYMDC", "product_id": "2411041715251849038", "old_capability_id": "2410231729666931071", "create_time": "23/6/2025 15:43:14", "update_time": "23/6/2025 15:43:14", "version": 1750664593861}, {"opgw_product_key": "opgwNZC", "opgw_capability_key": "opgwZJHY", "product_id": "2411041715251849038", "old_capability_id": "2410231729666871074", "create_time": "23/6/2025 15:43:14", "update_time": "23/6/2025 15:43:14", "version": 1750664593861}, {"opgw_product_key": "opgwNZC", "opgw_capability_key": "opgwZJZD", "product_id": "2411041715251849038", "old_capability_id": "2410231729666881078", "create_time": "23/6/2025 15:43:14", "update_time": "23/6/2025 15:43:14", "version": 1750664593861}, {"opgw_product_key": "opgwNZC", "opgw_capability_key": "opgwZWHM", "product_id": "2411041715251849038", "old_capability_id": "2410231729667005070", "create_time": "23/6/2025 15:43:14", "update_time": "23/6/2025 15:43:14", "version": 1750664593861}, {"opgw_product_key": "opgwNZC", "opgw_capability_key": "opgwZWS", "product_id": "2411041715251849038", "old_capability_id": "2410231729666786076", "create_time": "23/6/2025 15:43:14", "update_time": "23/6/2025 15:43:14", "version": 1750664593861}, {"opgw_product_key": "OTPvalidation", "opgw_capability_key": "getDscp", "product_id": "2405111917056969017", "old_capability_id": "2405101757208058798", "create_time": "23/6/2025 09:49:51", "update_time": "23/6/2025 09:49:51", "version": 1750643390823}, {"opgw_product_key": "OTPvalidation", "opgw_capability_key": "getValidateCode", "product_id": "2405111917056969017", "old_capability_id": "2405101757228408801", "create_time": "23/6/2025 09:49:51", "update_time": "23/6/2025 09:49:51", "version": 1750643390823}, {"opgw_product_key": "postMockJINTAOmore", "opgw_capability_key": "addInterfaceSIM", "product_id": "2503201353414949453", "old_capability_id": "2503201545550309491", "create_time": "23/6/2025 10:57:03", "update_time": "23/6/2025 10:57:03", "version": 1750647422588}, {"opgw_product_key": "postMockJINTAOmore", "opgw_capability_key": "interfaceIDmore", "product_id": "2503201353414949453", "old_capability_id": "2503201353361819450", "create_time": "23/6/2025 10:57:03", "update_time": "23/6/2025 10:57:03", "version": 1750647422588}, {"opgw_product_key": "postmuck", "opgw_capability_key": "postMock", "product_id": "2504170959008679029", "old_capability_id": "2504170958415459026", "create_time": "17/4/2025 10:00:35", "update_time": "17/4/2025 10:00:35", "version": 1744855235252}, {"opgw_product_key": "QOD", "opgw_capability_key": "opgwNFXC", "product_id": "2411111948537569089", "old_capability_id": "2410231729666848071", "create_time": "11/11/2024 20:21:28", "update_time": "11/11/2024 20:21:28", "version": 1731327687530}, {"opgw_product_key": "QODCapacity", "opgw_capability_key": "deleteQOS", "product_id": "2502260944460609013", "old_capability_id": "2502261054295049069", "create_time": "26/2/2025 10:58:09", "update_time": "26/2/2025 10:58:09", "version": 1740538688837}, {"opgw_product_key": "QODCapacity", "opgw_capability_key": "nameqosprofiles", "product_id": "2502260944460609013", "old_capability_id": "2502261035091999062", "create_time": "26/2/2025 10:58:09", "update_time": "26/2/2025 10:58:09", "version": 1740538688837}, {"opgw_product_key": "QODCapacity", "opgw_capability_key": "QODDsys", "product_id": "2502260944460609013", "old_capability_id": "2502260944231779009", "create_time": "26/2/2025 10:58:09", "update_time": "26/2/2025 10:58:09", "version": 1740538688837}, {"opgw_product_key": "QODCapacity", "opgw_capability_key": "qosprofiles", "product_id": "2502260944460609013", "old_capability_id": "2502261033532189058", "create_time": "26/2/2025 10:58:09", "update_time": "26/2/2025 10:58:09", "version": 1740538688837}, {"opgw_product_key": "QODCapacity", "opgw_capability_key": "selectqos", "product_id": "2502260944460609013", "old_capability_id": "2502261030491699052", "create_time": "26/2/2025 10:58:09", "update_time": "26/2/2025 10:58:09", "version": 1740538688837}, {"opgw_product_key": "QODCapacity", "opgw_capability_key": "updateQOS", "product_id": "2502260944460609013", "old_capability_id": "2502261056216869073", "create_time": "26/2/2025 10:58:09", "update_time": "26/2/2025 10:58:09", "version": 1740538688837}, {"opgw_product_key": "qqqqqqqq", "opgw_capability_key": "CreateDis599", "product_id": "2503121019103139013", "old_capability_id": "2501171346034229033", "create_time": "12/3/2025 10:21:29", "update_time": "12/3/2025 10:21:29", "version": 1741746088767}, {"opgw_product_key": "qqqqqqqq", "opgw_capability_key": "CreateDis601", "product_id": "2503121019103139013", "old_capability_id": "2501171427563459048", "create_time": "12/3/2025 10:21:29", "update_time": "12/3/2025 10:21:29", "version": 1741746088767}, {"opgw_product_key": "qqqqqqqq", "opgw_capability_key": "CreateDis602", "product_id": "2503121019103139013", "old_capability_id": "2501171435168149052", "create_time": "12/3/2025 10:21:29", "update_time": "12/3/2025 10:21:29", "version": 1741746088767}, {"opgw_product_key": "qqqqqqqq", "opgw_capability_key": "CreateDisk299", "product_id": "2503121019103139013", "old_capability_id": "2501161711509109166", "create_time": "12/3/2025 10:21:29", "update_time": "12/3/2025 10:21:29", "version": 1741746088767}, {"opgw_product_key": "qqqqqqqqqqq", "opgw_capability_key": "JINTjosntest", "product_id": "2503132024554969001", "old_capability_id": "2503131142259109020", "create_time": "13/3/2025 20:26:21", "update_time": "13/3/2025 20:26:21", "version": 1741868781296}, {"opgw_product_key": "qqqq<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>qqq", "opgw_capability_key": "CreateDis599", "product_id": "2503121023381299086", "old_capability_id": "2501171346034229033", "create_time": "23/6/2025 10:55:09", "update_time": "23/6/2025 10:55:09", "version": 1750647308796}, {"opgw_product_key": "QualityOnDemand", "opgw_capability_key": "createQosSessions", "product_id": "2405111922587389031", "old_capability_id": "2412172210049839084", "create_time": "23/6/2025 09:56:20", "update_time": "23/6/2025 09:56:20", "version": 1750643779810}, {"opgw_product_key": "QualityOnDemand", "opgw_capability_key": "deletQosSessionId", "product_id": "2405111922587389031", "old_capability_id": "2412172210050529093", "create_time": "23/6/2025 09:56:20", "update_time": "23/6/2025 09:56:20", "version": 1750643779810}, {"opgw_product_key": "QualityOnDemand", "opgw_capability_key": "extendQosSessionDuration", "product_id": "2405111922587389031", "old_capability_id": "2412172210050759096", "create_time": "23/6/2025 09:56:20", "update_time": "23/6/2025 09:56:20", "version": 1750643779810}, {"opgw_product_key": "QualityOnDemand", "opgw_capability_key": "getQosSessions", "product_id": "2405111922587389031", "old_capability_id": "2412172210050299090", "create_time": "23/6/2025 09:56:20", "update_time": "23/6/2025 09:56:20", "version": 1750643779810}, {"opgw_product_key": "QualityOnDemand", "opgw_capability_key": "retrieveSessions", "product_id": "2405111922587389031", "old_capability_id": "2412172210050069087", "create_time": "23/6/2025 09:56:20", "update_time": "23/6/2025 09:56:20", "version": 1750643779810}, {"opgw_product_key": "RealtimeLocation", "opgw_capability_key": "batchGetResidentialUsers", "product_id": "2205131527593869341", "old_capability_id": "2405241534271529270", "create_time": "23/6/2025 15:38:26", "update_time": "23/6/2025 15:38:26", "version": 1750664305676}, {"opgw_product_key": "RealtimeLocation", "opgw_capability_key": "batchGetUserByCell", "product_id": "2205131527593869341", "old_capability_id": "2405241446360949240", "create_time": "23/6/2025 15:38:26", "update_time": "23/6/2025 15:38:26", "version": 1750664305676}, {"opgw_product_key": "RealtimeLocation", "opgw_capability_key": "getCellLocation", "product_id": "2205131527593869341", "old_capability_id": "2405241450024689091", "create_time": "23/6/2025 15:38:26", "update_time": "23/6/2025 15:38:26", "version": 1750664305676}, {"opgw_product_key": "RealtimeLocation", "opgw_capability_key": "getGridUserCntByArea", "product_id": "2205131527593869341", "old_capability_id": "2405241535322539274", "create_time": "23/6/2025 15:38:26", "update_time": "23/6/2025 15:38:26", "version": 1750664305676}, {"opgw_product_key": "RealtimeLocation", "opgw_capability_key": "getRegionalUserCounts", "product_id": "2205131527593869341", "old_capability_id": "2405241533505689266", "create_time": "23/6/2025 15:38:26", "update_time": "23/6/2025 15:38:26", "version": 1750664305676}, {"opgw_product_key": "RealtimeLocation", "opgw_capability_key": "getUserByArea", "product_id": "2205131527593869341", "old_capability_id": "2405242042271559446", "create_time": "23/6/2025 15:38:26", "update_time": "23/6/2025 15:38:26", "version": 1750664305676}, {"opgw_product_key": "RealtimeLocation", "opgw_capability_key": "getUserCntByCells", "product_id": "2205131527593869341", "old_capability_id": "2405241400544279081", "create_time": "23/6/2025 15:38:26", "update_time": "23/6/2025 15:38:26", "version": 1750664305676}, {"opgw_product_key": "RealtimeLocation", "opgw_capability_key": "getUserCountByArea", "product_id": "2205131527593869341", "old_capability_id": "2405241454126599094", "create_time": "23/6/2025 15:38:26", "update_time": "23/6/2025 15:38:26", "version": 1750664305676}, {"opgw_product_key": "RealtimeUserState", "opgw_capability_key": "mockgetUserLocationG", "product_id": "2206010914466389004", "old_capability_id": "2506131717391859074", "create_time": "23/6/2025 15:41:03", "update_time": "23/6/2025 15:41:03", "version": 1750664462800}, {"opgw_product_key": "RegionUserCount", "opgw_capability_key": "opgw64wH6K", "product_id": "2405111922597629047", "old_capability_id": "2304071004473312929", "create_time": "23/6/2025 09:53:38", "update_time": "23/6/2025 09:53:38", "version": 1750643618108}, {"opgw_product_key": "Riskvalidation", "opgw_capability_key": "opgwhmfxSb", "product_id": "2410211127077219139", "old_capability_id": "2407151511088480027", "create_time": "23/6/2025 10:27:26", "update_time": "23/6/2025 10:27:26", "version": 1750645645521}, {"opgw_product_key": "SCDSsimswap", "opgw_capability_key": "SCcheckSimSwap", "product_id": "2503181517287679337", "old_capability_id": "2503181514181759331", "create_time": "23/6/2025 10:11:18", "update_time": "23/6/2025 10:11:18", "version": 1750644678121}, {"opgw_product_key": "SCDSsimswap", "opgw_capability_key": "SCretrieveSimSwap", "product_id": "2503181517287679337", "old_capability_id": "2503181517056759334", "create_time": "23/6/2025 10:11:18", "update_time": "23/6/2025 10:11:18", "version": 1750644678121}, {"opgw_product_key": "sendMessageBackend", "opgw_capability_key": "sendMessageBackend", "product_id": "2506162034019299473", "old_capability_id": "2506162033490139470", "create_time": "23/6/2025 10:18:16", "update_time": "23/6/2025 10:18:16", "version": 1750645095946}, {"opgw_product_key": "ShortMessageService", "opgw_capability_key": "sendMessage", "product_id": "2409121733153109133", "old_capability_id": "2307040931130159582", "create_time": "18/7/2025 17:35:35", "update_time": "18/7/2025 17:35:35", "version": 1752831335145}, {"opgw_product_key": "skcode", "opgw_capability_key": "sksendcode", "product_id": "2505302110267159369", "old_capability_id": "2505292216186539348", "create_time": "3/6/2025 12:06:31", "update_time": "3/6/2025 12:06:31", "version": 1748923590501}, {"opgw_product_key": "skcode", "opgw_capability_key": "skverify", "product_id": "2505302110267159369", "old_capability_id": "2506031205597039428", "create_time": "3/6/2025 12:06:31", "update_time": "3/6/2025 12:06:31", "version": 1748923590501}, {"opgw_product_key": "ssssssssss", "opgw_capability_key": "jkProjin2", "product_id": "2503261704043359008", "old_capability_id": "2503261425506759037", "create_time": "23/6/2025 11:37:43", "update_time": "23/6/2025 11:37:43", "version": 1750649863347}, {"opgw_product_key": "<PERSON><PERSON><PERSON>", "opgw_capability_key": "opgwSbLWzT", "product_id": "2411141625174159096", "old_capability_id": "2411131722278439067", "create_time": "16/11/2024 21:58:15", "update_time": "16/11/2024 21:58:15", "version": 1731765495222}, {"opgw_product_key": "test0925test0925", "opgw_capability_key": "add<PERSON><PERSON>", "product_id": "2409251137029979005", "old_capability_id": "2405241714480809188", "create_time": "16/1/2025 14:05:59", "update_time": "16/1/2025 14:05:59", "version": 1737007559329}, {"opgw_product_key": "test0925test0925", "opgw_capability_key": "AttachDisk", "product_id": "2409251137029979005", "old_capability_id": "2501101538039279003", "create_time": "16/1/2025 14:05:59", "update_time": "16/1/2025 14:05:59", "version": 1737007559329}, {"opgw_product_key": "test0925test0925", "opgw_capability_key": "createFence", "product_id": "2409251137029979005", "old_capability_id": "2405241722419889195", "create_time": "16/1/2025 14:05:59", "update_time": "16/1/2025 14:05:59", "version": 1737007559329}, {"opgw_product_key": "test0925test0925", "opgw_capability_key": "deletePerson", "product_id": "2409251137029979005", "old_capability_id": "2405241709376069315", "create_time": "16/1/2025 14:05:59", "update_time": "16/1/2025 14:05:59", "version": 1737007559329}, {"opgw_product_key": "test0925test0925", "opgw_capability_key": "fenceInfo", "product_id": "2409251137029979005", "old_capability_id": "2405241724462979336", "create_time": "16/1/2025 14:05:59", "update_time": "16/1/2025 14:05:59", "version": 1737007559329}, {"opgw_product_key": "test0925test0925", "opgw_capability_key": "opgw0qHVVq", "product_id": "2409251137029979005", "old_capability_id": "2407151520410920139", "create_time": "16/1/2025 14:05:59", "update_time": "16/1/2025 14:05:59", "version": 1737007559329}, {"opgw_product_key": "test0925test0925", "opgw_capability_key": "opgw6Gc31T", "product_id": "2409251137029979005", "old_capability_id": "2407151511472319465", "create_time": "16/1/2025 14:05:59", "update_time": "16/1/2025 14:05:59", "version": 1737007559329}, {"opgw_product_key": "test0925test0925", "opgw_capability_key": "opgw6Xzf8C", "product_id": "2409251137029979005", "old_capability_id": "2405272144290119004", "create_time": "16/1/2025 14:05:59", "update_time": "16/1/2025 14:05:59", "version": 1737007559329}, {"opgw_product_key": "test0925test0925", "opgw_capability_key": "opgw8BPFXo", "product_id": "2409251137029979005", "old_capability_id": "2407151427502249451", "create_time": "16/1/2025 14:05:59", "update_time": "16/1/2025 14:05:59", "version": 1737007559329}, {"opgw_product_key": "test0925test0925", "opgw_capability_key": "opgw9lLBHT", "product_id": "2409251137029979005", "old_capability_id": "2407151511088480097", "create_time": "16/1/2025 14:05:59", "update_time": "16/1/2025 14:05:59", "version": 1737007559329}, {"opgw_product_key": "test0925test0925", "opgw_capability_key": "opgwbMDIeN", "product_id": "2409251137029979005", "old_capability_id": "2407151520197490135", "create_time": "16/1/2025 14:05:59", "update_time": "16/1/2025 14:05:59", "version": 1737007559329}, {"opgw_product_key": "test0925test0925", "opgw_capability_key": "opgwCr4RBp", "product_id": "2409251137029979005", "old_capability_id": "2407151512341590115", "create_time": "16/1/2025 14:05:59", "update_time": "16/1/2025 14:05:59", "version": 1737007559329}, {"opgw_product_key": "test0925test0925", "opgw_capability_key": "opgwFjf4zo", "product_id": "2409251137029979005", "old_capability_id": "2407151512031980102", "create_time": "16/1/2025 14:05:59", "update_time": "16/1/2025 14:05:59", "version": 1737007559329}, {"opgw_product_key": "test0925test0925", "opgw_capability_key": "opgwgKSEER", "product_id": "2409251137029979005", "old_capability_id": "2407151428329839455", "create_time": "16/1/2025 14:05:59", "update_time": "16/1/2025 14:05:59", "version": 1737007559329}, {"opgw_product_key": "test0925test0925", "opgw_capability_key": "opgwhmfxSb", "product_id": "2409251137029979005", "old_capability_id": "2407151511088480027", "create_time": "16/1/2025 14:05:59", "update_time": "16/1/2025 14:05:59", "version": 1737007559329}, {"opgw_product_key": "test0925test0925", "opgw_capability_key": "opgwkWRlHx", "product_id": "2409251137029979005", "old_capability_id": "2407151432159759459", "create_time": "16/1/2025 14:05:59", "update_time": "16/1/2025 14:05:59", "version": 1737007559329}, {"opgw_product_key": "test0925test0925", "opgw_capability_key": "opgwV5xiE6", "product_id": "2409251137029979005", "old_capability_id": "2407151520022690131", "create_time": "16/1/2025 14:05:59", "update_time": "16/1/2025 14:05:59", "version": 1737007559329}, {"opgw_product_key": "test0925test0925", "opgw_capability_key": "opgwwm51JM", "product_id": "2409251137029979005", "old_capability_id": "2407151511271119462", "create_time": "16/1/2025 14:05:59", "update_time": "16/1/2025 14:05:59", "version": 1737007559329}, {"opgw_product_key": "test0925test0925", "opgw_capability_key": "sendMessage", "product_id": "2409251137029979005", "old_capability_id": "2307040931130159582", "create_time": "16/1/2025 14:05:59", "update_time": "16/1/2025 14:05:59", "version": 1737007559329}, {"opgw_product_key": "test0925test0925", "opgw_capability_key": "showRule", "product_id": "2409251137029979005", "old_capability_id": "2405241716370179192", "create_time": "16/1/2025 14:05:59", "update_time": "16/1/2025 14:05:59", "version": 1737007559329}, {"opgw_product_key": "test0925test0925", "opgw_capability_key": "voiceCode", "product_id": "2409251137029979005", "old_capability_id": "2111121107280289448", "create_time": "16/1/2025 14:05:59", "update_time": "16/1/2025 14:05:59", "version": 1737007559329}, {"opgw_product_key": "test0925test0925", "opgw_capability_key": "voiceNotifySw", "product_id": "2409251137029979005", "old_capability_id": "2111121109337999450", "create_time": "16/1/2025 14:05:59", "update_time": "16/1/2025 14:05:59", "version": 1737007559329}, {"opgw_product_key": "testcreatemock", "opgw_capability_key": "jkMockvv1", "product_id": "2506111649314329568", "old_capability_id": "2503221202087999703", "create_time": "11/6/2025 16:52:01", "update_time": "11/6/2025 16:52:01", "version": 1749631920839}, {"opgw_product_key": "testMockBindApp", "opgw_capability_key": "mockqueryUncondition", "product_id": "2506122022387940164", "old_capability_id": "2506121359584149848", "create_time": "12/6/2025 20:24:46", "update_time": "12/6/2025 20:24:46", "version": 1749731085655}, {"opgw_product_key": "testMockProductSync", "opgw_capability_key": "mockqueryUncondition", "product_id": "2506121743591650116", "old_capability_id": "2506121359584149848", "create_time": "12/6/2025 18:19:20", "update_time": "12/6/2025 18:19:20", "version": 1749723559629}, {"opgw_product_key": "testtesttest", "opgw_capability_key": "JNtoken", "product_id": "2504031447024899002", "old_capability_id": "2504081509224389023", "create_time": "8/4/2025 15:10:39", "update_time": "8/4/2025 15:10:39", "version": 1744096238683}, {"opgw_product_key": "testtesttesttest", "opgw_capability_key": "putMock", "product_id": "2504031447582869039", "old_capability_id": "2504032208389249005", "create_time": "4/6/2025 20:37:39", "update_time": "4/6/2025 20:37:39", "version": 1749040659288}, {"opgw_product_key": "Ucfsi", "opgw_capability_key": "Adiskca", "product_id": "2411131146593049018", "old_capability_id": "2411131146381189015", "create_time": "23/6/2025 15:39:11", "update_time": "23/6/2025 15:39:11", "version": 1750664351302}, {"opgw_product_key": "UDMSigning", "opgw_capability_key": "getCFInfo", "product_id": "2207121555345619129", "old_capability_id": "2405241602306689282", "create_time": "26/11/2024 10:56:48", "update_time": "26/11/2024 10:56:48", "version": 1732589808194}, {"opgw_product_key": "UDMSigning", "opgw_capability_key": "getNoAnswerCF", "product_id": "2207121555345619129", "old_capability_id": "2405241647493389167", "create_time": "26/11/2024 10:56:48", "update_time": "26/11/2024 10:56:48", "version": 1732589808194}, {"opgw_product_key": "voiceCode", "opgw_capability_key": "voiceCode", "product_id": "2409121726224979076", "old_capability_id": "2111121107280289448", "create_time": "23/6/2025 10:17:16", "update_time": "23/6/2025 10:17:16", "version": 1750645035677}, {"opgw_product_key": "voiceNotifySw", "opgw_capability_key": "voiceNotifySw", "product_id": "2409121729564579104", "old_capability_id": "2111121109337999450", "create_time": "23/6/2025 10:19:17", "update_time": "23/6/2025 10:19:17", "version": 1750645157020}, {"opgw_product_key": "VolteasSigning", "opgw_capability_key": "getBCFStatus", "product_id": "2206061707376370000", "old_capability_id": "2405241550404229119", "create_time": "26/11/2024 10:54:17", "update_time": "26/11/2024 10:54:17", "version": 1732589656814}, {"opgw_product_key": "VolteasSigning", "opgw_capability_key": "getCFSetting", "product_id": "2206061707376370000", "old_capability_id": "2405241645282989163", "create_time": "26/11/2024 10:54:17", "update_time": "26/11/2024 10:54:17", "version": 1732589656814}, {"opgw_product_key": "VolteasSigning", "opgw_capability_key": "getNCFStatus", "product_id": "2206061707376370000", "old_capability_id": "2405241556554679122", "create_time": "26/11/2024 10:54:17", "update_time": "26/11/2024 10:54:17", "version": 1732589656814}, {"opgw_product_key": "VolteasSigning", "opgw_capability_key": "getUCFStatus", "product_id": "2206061707376370000", "old_capability_id": "2405241548369149116", "create_time": "26/11/2024 10:54:17", "update_time": "26/11/2024 10:54:17", "version": 1732589656814}, {"opgw_product_key": "VolteasSigning", "opgw_capability_key": "getURCFStatus", "product_id": "2206061707376370000", "old_capability_id": "2405241600551459125", "create_time": "26/11/2024 10:54:17", "update_time": "26/11/2024 10:54:17", "version": 1732589656814}, {"opgw_product_key": "wmzTestEnglish1", "opgw_capability_key": "wmzTestCallBack1", "product_id": "2406131711088259005", "old_capability_id": "2410311838214309001", "create_time": "26/6/2025 18:52:18", "update_time": "26/6/2025 18:52:18", "version": 1750935137805}, {"opgw_product_key": "wmzTestEnglish1", "opgw_capability_key": "wmzTestCallBack2", "product_id": "2406131711088259005", "old_capability_id": "2410311838216219004", "create_time": "26/6/2025 18:52:18", "update_time": "26/6/2025 18:52:18", "version": 1750935137805}, {"opgw_product_key": "www", "opgw_capability_key": "CreateDisk215", "product_id": "2503121022392789050", "old_capability_id": "2501161650215449127", "create_time": "12/3/2025 10:23:36", "update_time": "12/3/2025 10:23:36", "version": 1741746216352}, {"opgw_product_key": "www", "opgw_capability_key": "CreateDisk216", "product_id": "2503121022392789050", "old_capability_id": "2501161650215859130", "create_time": "12/3/2025 10:23:36", "update_time": "12/3/2025 10:23:36", "version": 1741746216352}, {"opgw_product_key": "xin", "opgw_capability_key": "opgwwm51JM", "product_id": "2407160933290529011", "old_capability_id": "2407151511271119462", "create_time": "16/7/2024 09:35:39", "update_time": "16/7/2024 09:35:39", "version": 1721093738892}, {"opgw_product_key": "yhwkes", "opgw_capability_key": "0108test11", "product_id": "2406081542347239015", "old_capability_id": "2501081108373919011", "create_time": "8/1/2025 11:39:00", "update_time": "8/1/2025 11:39:00", "version": 1736307540310}, {"opgw_product_key": "yhwkes", "opgw_capability_key": "0108test22", "product_id": "2406081542347239015", "old_capability_id": "2501081138418679019", "create_time": "8/1/2025 11:39:00", "update_time": "8/1/2025 11:39:00", "version": 1736307540310}, {"opgw_product_key": "yhwkes", "opgw_capability_key": "opgw6Xzf8C", "product_id": "2406081542347239015", "old_capability_id": "2405272144290119004", "create_time": "8/1/2025 11:39:00", "update_time": "8/1/2025 11:39:00", "version": 1736307540310}, {"opgw_product_key": "y<PERSON><PERSON><PERSON>zheng", "opgw_capability_key": "opgwFjf4zo", "product_id": "2409121714481839021", "old_capability_id": "2407151512031980102", "create_time": "23/6/2025 10:46:23", "update_time": "23/6/2025 10:46:23", "version": 1750646782990}]