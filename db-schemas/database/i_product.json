[{"opgw_product_key": "1111", "product_id": "2506251738377929209", "product_name": "0804", "product_name_en": "1111", "product_desc": "111111", "product_desc_en": "11111111", "status": "offline", "classify": 42, "classify_two": 42, "product_label": null, "product_setting": "order", "open_range": "all", "create_time": "25/6/2025 17:59:01", "update_time": "1/8/2025 17:17:20", "online_time": "25/6/2025 17:59:01", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1754039840466, "doc_id": "2506251759012369305", "en_doc_id": "2506251759012419320", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 42, "operate_id_list": "ChinaTelecom,ChinaMobile,achentest,ChinaTelecomChinaTelecomChinaTelecom"}, {"opgw_product_key": "11111111111111", "product_id": "2407151040106749051", "product_name": "基站34", "product_name_en": "Base station number of users query", "product_desc": "查询基站范围内存在的用户数量的能力", "product_desc_en": "The ability to query the number of users that exist within the scope of a base station", "status": "offline", "classify": 2, "classify_two": 14, "product_label": null, "product_setting": "show", "open_range": "all", "create_time": "15/7/2024 10:41:38", "update_time": "26/9/2024 18:30:49", "online_time": "12/9/2024 17:24:34", "product_score": null, "product_score_sub_item_json": null, "userid": 175, "username": "guoyq28", "realname": "郭永强", "orgid": 42, "org_name": "平顶山市郏县分公司", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1727346649247, "doc_id": "2407151041377399062", "en_doc_id": "2411211117433419001", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 42, "operate_id_list": null}, {"opgw_product_key": "11111111111111111111", "product_id": "2406041450128599062", "product_name": "2222222222222222222222222", "product_name_en": "default name", "product_desc": "******************************************", "product_desc_en": "default description", "status": "deleted", "classify": 2, "classify_two": 11, "product_label": "101", "product_setting": "show", "open_range": "inner", "create_time": "4/6/2024 14:56:22", "update_time": "5/6/2024 15:10:55", "online_time": "4/6/2024 17:55:36", "product_score": null, "product_score_sub_item_json": null, "userid": 116, "username": "yangf94", "realname": "杨帆", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1717571455430, "doc_id": "2406041456220609083", "en_doc_id": "2411211117433779016", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 44, "operate_id_list": null}, {"opgw_product_key": "12121111111111111", "product_id": "2411171833428469006", "product_name": "1111111111111111111111", "product_name_en": "12121111111111111", "product_desc": "5G虚拟专网产品通过网络切片、无线增强、融合大规模使用UPF/MEC等技术，针对时延敏感、算力需求大、需快速部署、但物理隔离/私密性要求不苛刻的场景，面向多个客户提供\"多租户“业务隔离服务,满足低时延和业务隔离的诉求5G虚拟专网产品通过网络切片、无线增强、融合大规模使用UPF/MEC等技术，针对时延敏感、算力需求大、需快速部署、但物理隔离/私密性要求不苛刻的场景，面向多个客户提供\"多租户“业务隔离", "product_desc_en": "12121111111111111", "status": "deleted", "classify": 36, "classify_two": 37, "product_label": "101", "product_setting": "show", "open_range": "all", "create_time": "17/11/2024 18:34:16", "update_time": "23/6/2025 11:16:49", "online_time": "18/11/2024 10:17:39", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1750648608768, "doc_id": "2411171834163289011", "en_doc_id": "2411211117434009031", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 37, "operate_id_list": null}, {"opgw_product_key": "1212121212", "product_id": "2405311551328929035", "product_name": "http://**************:38081/login", "product_name_en": "default name", "product_desc": "基于OTPValidation API的即时验证技术，助力企业快速、准确完成用户鉴权，确保交易安全，促进了企业服务策略的精准制定与风险管理优化。", "product_desc_en": "default description", "status": "deleted", "classify": 1, "classify_two": 10, "product_label": "101", "product_setting": "show", "open_range": "all", "create_time": "31/5/2024 15:52:38", "update_time": "5/6/2024 15:11:05", "online_time": "3/6/2024 10:40:29", "product_score": null, "product_score_sub_item_json": null, "userid": 98, "username": "huzy39", "realname": "胡泽妍", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1717571465491, "doc_id": "2405311552382799038", "en_doc_id": "2411211117434409046", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 42, "operate_id_list": null}, {"opgw_product_key": "22222222222222221", "product_id": "2411141451218609025", "product_name": "1111111", "product_name_en": "2222222222222222222", "product_desc": "5G虚拟专网产品通过网络切片、无线增强、融合大规模使用UPF/MEC等技术，针对时延敏感、算力需求大、需快速部署、但物理隔离/私密性要求不苛刻的场景，面向多个客户提供\"多租户“业务隔离服务,满足低时延和业务隔离的诉求5G虚拟专网产品通过网络切片、无线增强、融合大规模使用UPF/MEC等技术，针对时延敏感、算力需求大、需快速部署、但物理隔离/私密性要求不苛刻的场景，面向多个客户提供\"多租户“业务隔离", "product_desc_en": "****************************", "status": "deleted", "classify": 36, "classify_two": 37, "product_label": "101", "product_setting": "show", "open_range": "all", "create_time": "14/11/2024 14:59:00", "update_time": "23/6/2025 11:24:20", "online_time": "18/11/2024 10:17:18", "product_score": null, "product_score_sub_item_json": null, "userid": 104, "username": "wangmz90", "realname": "王孟哲", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1750649060229, "doc_id": "2411141459003069039", "en_doc_id": "2411211117434819061", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 37, "operate_id_list": null}, {"opgw_product_key": "22222222222222222", "product_id": "2411141853440079022", "product_name": "22222222222222222", "product_name_en": "22222222222222222", "product_desc": "5G虚拟专网产品通过网络切片、无线增强、融合大规模使用UPF/MEC等技术，针对时延敏感、算力需求大、需快速部署、但物理隔离/私密性要求不苛刻的场景，面向多个客户提供\"多租户“业务隔离服务,满足低时延和业务隔离的诉求5G虚拟专网产品通过网络切片、无线增强、融合大规模使用UPF/MEC等技术，针对时延敏感、算力需求大、需快速部署、但物理隔离/私密性要求不苛刻的场景，面向多个客户提供\"多租户“业务隔离", "product_desc_en": "22222222222222222", "status": "deleted", "classify": 36, "classify_two": 37, "product_label": "101", "product_setting": "show", "open_range": "all", "create_time": "14/11/2024 18:54:21", "update_time": "23/6/2025 11:17:32", "online_time": "18/11/2024 10:17:29", "product_score": null, "product_score_sub_item_json": null, "userid": 98, "username": "huzy39", "realname": "胡泽妍", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1750648652173, "doc_id": "2411141854214929027", "en_doc_id": "2411211117435119076", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 37, "operate_id_list": null}, {"opgw_product_key": "33333333333333333333", "product_id": "2406041501123799091", "product_name": "******************************************11111111", "product_name_en": "default name", "product_desc": "*********************************", "product_desc_en": "default description", "status": "deleted", "classify": 2, "classify_two": 11, "product_label": null, "product_setting": "show", "open_range": "inner", "create_time": "4/6/2024 15:02:13", "update_time": "5/6/2024 15:10:53", "online_time": "4/6/2024 16:38:28", "product_score": null, "product_score_sub_item_json": null, "userid": 98, "username": "huzy39", "realname": "胡泽妍", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1717571452897, "doc_id": "2406041502126359102", "en_doc_id": "2411211117435339091", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 44, "operate_id_list": null}, {"opgw_product_key": "444444444444444444", "product_id": "2411141855317089045", "product_name": "11111111111111111111", "product_name_en": "11111111111111111111", "product_desc": "5G虚拟专网产品通过网络切片、无线增强、融合大规模使用UPF/MEC等技术，针对时延敏感、算力需求大、需快速部署、但物理隔离/私密性要求不苛刻的场景，面向多个客户提供\"多租户“业务隔离服务,满足低时延和业务隔离的诉求5G虚拟专网产品通过网络切片、无线增强、融合大规模使用UPF/MEC等技术，针对时延敏感、算力需求大、需快速部署、但物理隔离/私密性要求不苛刻的场景，面向多个客户提供\"多租户“业务隔离", "product_desc_en": "11111111111111111111", "status": "deleted", "classify": 36, "classify_two": 37, "product_label": "101", "product_setting": "show", "open_range": "all", "create_time": "14/11/2024 19:04:53", "update_time": "23/6/2025 11:17:30", "online_time": "18/11/2024 10:17:50", "product_score": null, "product_score_sub_item_json": null, "userid": 98, "username": "huzy39", "realname": "胡泽妍", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1750648649973, "doc_id": "2411141904529299056", "en_doc_id": "2411211117435529106", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 37, "operate_id_list": null}, {"opgw_product_key": "5GSlicing", "product_id": "2411111938291819030", "product_name": "5G切片", "product_name_en": "5G Network Slicing", "product_desc": "5G虚拟专网产品通过网络切片、无线增强、融合大规模使用UPF/MEC等技术，针对时延敏感、算力需求大、需快速部署、但物理隔离/私密性要求不苛刻的场景，面向多个客户提供\"多租户“业务隔离服务,满足低时延和业务隔离的诉求", "product_desc_en": "The 5G Virtual Private Network (VPN) product leverages network slicing, radio enhancements, UPF/MEC technologies to cater to scenarios that are sensitive to latency, require substantial computational power, demand rapid deployment, yet have less stringent requirements for physical isolation or confidentiality. It provides \"multi-tenant\" service isolation to multiple clients, fulfilling the needs for low latency and service segregation.", "status": "online", "classify": 38, "classify_two": 38, "product_label": null, "product_setting": "order", "open_range": "all", "create_time": "11/11/2024 19:43:42", "update_time": "23/6/2025 15:42:15", "online_time": "23/6/2025 15:42:15", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1750664535028, "doc_id": "2411111943422999041", "en_doc_id": "2411211117435749121", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 38, "operate_id_list": null}, {"opgw_product_key": "66666666666666666", "product_id": "2406051655541519231", "product_name": "66666666666666666", "product_name_en": "default name", "product_desc": "66666666666666666", "product_desc_en": "default description", "status": "deleted", "classify": 2, "classify_two": 11, "product_label": "101", "product_setting": "show", "open_range": "all", "create_time": "5/6/2024 16:56:53", "update_time": "5/6/2024 18:17:30", "online_time": "5/6/2024 17:21:31", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1717582650470, "doc_id": "2406051656530609239", "en_doc_id": "2411211117435949136", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 44, "operate_id_list": null}, {"opgw_product_key": "77777777777777", "product_id": "2406051657435999261", "product_name": "77777777777777", "product_name_en": "default name", "product_desc": "7777777777777777777777777777", "product_desc_en": "default description", "status": "deleted", "classify": 2, "classify_two": 11, "product_label": "101", "product_setting": "show", "open_range": "all", "create_time": "5/6/2024 16:58:40", "update_time": "5/6/2024 18:17:22", "online_time": null, "product_score": null, "product_score_sub_item_json": null, "userid": 98, "username": "huzy39", "realname": "胡泽妍", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1717582641936, "doc_id": "2406051658395819273", "en_doc_id": "2411211117436159151", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 44, "operate_id_list": null}, {"opgw_product_key": "aaaaaa", "product_id": "2411201728379779172", "product_name": "测试能力文档", "product_name_en": "ssss", "product_desc": "测试能力文档", "product_desc_en": "ssssss", "status": "deleted", "classify": 36, "classify_two": 37, "product_label": "101", "product_setting": "show", "open_range": "all", "create_time": "20/11/2024 17:29:21", "update_time": "23/6/2025 11:16:30", "online_time": "20/11/2024 17:29:21", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1750648590494, "doc_id": "2411201729206229177", "en_doc_id": "2411211117436349166", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 37, "operate_id_list": null}, {"opgw_product_key": "aaaaaaa", "product_id": "2504031417464359006", "product_name": "测试0403", "product_name_en": "aaaa", "product_desc": "aaaaaaaaaaaaaaaaaaaaaaaaa", "product_desc_en": "aaaaaaaaaaaaaaaaaaaaaaaaa", "status": "draft", "classify": 42, "classify_two": 42, "product_label": "101", "product_setting": "show", "open_range": "all", "create_time": "3/4/2025 14:18:35", "update_time": "14/6/2025 03:05:34", "online_time": null, "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1749841534375, "doc_id": "2504031418349959011", "en_doc_id": "2504031418351449026", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 42, "operate_id_list": null}, {"opgw_product_key": "aaaaaaaaaa", "product_id": "2409262039185929252", "product_name": "位置验证", "product_name_en": "location-verification", "product_desc": "API通过传入地理上的区域以及手机号验证设备是否在指定区域内，精度范围在2至200公里内。传入用户手机号和区域，返回是否在传入区域内以及置信度", "product_desc_en": "The API verifies whether the device is within a specified geographic area by passing the geographical region and the phone number. The accuracy range is from 2 to 200 kilometers. It takes the user's phone number and the region as input, and returns whether the device is within the given area along with the confidence level.", "status": "offline", "classify": 36, "classify_two": 37, "product_label": "101", "product_setting": "order", "open_range": "all", "create_time": "26/9/2024 20:40:23", "update_time": "18/12/2024 15:41:15", "online_time": "11/11/2024 17:08:16", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1734507674683, "doc_id": "2409262040233659263", "en_doc_id": "2411211117436529181", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 37, "operate_id_list": null}, {"opgw_product_key": "aaaaaaaaaaaaaaaaaa", "product_id": "2409262008220669216", "product_name": "金涛(暂停使用)", "product_name_en": "aaaaaaaaaaaaaaaaa", "product_desc": "aaaaaaaaaaaaaaaaaaaaaa", "product_desc_en": "aaaaaaaaaaaaaaaaaaaaaaa", "status": "online", "classify": 1, "classify_two": 10, "product_label": "101", "product_setting": "order", "open_range": "outer", "create_time": "26/9/2024 20:09:36", "update_time": "18/10/2024 14:07:39", "online_time": "18/10/2024 14:07:39", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1729231659475, "doc_id": "2409262009364579228", "en_doc_id": "2411211117436769196", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 42, "operate_id_list": null}, {"opgw_product_key": "Aaaaaaaaaaaaaaaaaaa", "product_id": "2411181004251889035", "product_name": "Aaaaaaaaaaaaaaaaaaa", "product_name_en": "Aaaaaaaaaaaaaaaaaaa", "product_desc": "5G虚拟专网产品通过网络切片、无线增强、融合大规模使用UPF/MEC等技术，针对时延敏感、算力需求大、需快速部署、但物理隔离/私密性要求不苛刻的场景，面向多个客户提供\"多租户“业务隔离服务,满足低时延和业务隔离的诉求5G虚拟专网产品通过网络切片、无线增强、融合大规模使用UPF/MEC等技术，针对时延敏感、算力需求大、需快速部署、但物理隔离/私密性要求不苛刻的场景，面向多个客户提供\"多租户“业务隔离", "product_desc_en": "Aaaaaaaaaaaaaaaaaaa", "status": "deleted", "classify": 36, "classify_two": 37, "product_label": "101", "product_setting": "show", "open_range": "all", "create_time": "18/11/2024 10:05:04", "update_time": "23/6/2025 11:16:47", "online_time": "18/11/2024 10:17:05", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1750648606757, "doc_id": "2411181005036609040", "en_doc_id": "2411211117436959211", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 37, "operate_id_list": null}, {"opgw_product_key": "Aaaaaaaaaaaaaaaaaaaa", "product_id": "2411181005141599056", "product_name": "Aaaaaaaaaaaaaaaaaaa", "product_name_en": "Aaaaaaaaaaaaaaaaaaa", "product_desc": "5G虚拟专网产品通过网络切片、无线增强、融合大规模使用UPF/MEC等技术，针对时延敏感、算力需求大、需快速部署、但物理隔离/私密性要求不苛刻的场景，面向多个客户提供\"多租户“业务隔离服务,满足低时延和业务隔离的诉求5G虚拟专网产品通过网络切片、无线增强、融合大规模使用UPF/MEC等技术，针对时延敏感、算力需求大、需快速部署、但物理隔离/私密性要求不苛刻的场景，面向多个客户提供\"多租户“业务隔离", "product_desc_en": "Aaaaaaaaaaaaaaaaaaa", "status": "deleted", "classify": 36, "classify_two": 39, "product_label": "101", "product_setting": "show", "open_range": "all", "create_time": "18/11/2024 10:05:45", "update_time": "23/6/2025 11:16:42", "online_time": "18/11/2024 10:16:38", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1750648602222, "doc_id": "2411181005454199061", "en_doc_id": "2411211117437149226", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 39, "operate_id_list": null}, {"opgw_product_key": "aaaaaaaaaaas", "product_id": "2503121620421479016", "product_name": "测试能力（前端流）", "product_name_en": "aaaaaaaaaaaa", "product_desc": "测试能力（前端流）测试能力（前端流）测试能力（前端流）", "product_desc_en": "aaaaaaaaaaaaaa", "status": "deleted", "classify": 36, "classify_two": 37, "product_label": "101", "product_setting": "order", "open_range": "all", "create_time": "12/3/2025 16:22:18", "update_time": "23/6/2025 11:16:14", "online_time": "12/3/2025 16:22:18", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1750648574274, "doc_id": "2503121622184239021", "en_doc_id": "2503121622184409036", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": "1", "auth_methods": "101", "classify_new": 37, "operate_id_list": null}, {"opgw_product_key": "addCapacityapitiaoce", "product_id": "2503171009595319081", "product_name": "生产能力-用于测试api调测（前后）金涛-用作门户展示与演示 IPv6理论基础-CSDN技术社区【计算机网络】网络层 : IPv6 协议 ( IPv6 数据包格式 | IPv6 地址表示 | IPv6", "product_name_en": "addCapacityapitiaoce", "product_desc": "addCapacityapitiaoce 生产能力-用于测试api调测（前后）金涛-用作门户展示与演示 冒分十六进制表示法格式为X:X:X:X:X:X:X:X，每个X代表16位，用十六进制表示13。例如：ABCD:EF01:2345:6789:ABCD:EF01:2345:678923。可省略每个X的前导0，如2001:0DB8:0000:0023:0008:0800:200C:417A可【计算机", "product_desc_en": "addCapacityapitiaoceaddCapacityapitiaoce", "status": "online", "classify": 42, "classify_two": 42, "product_label": null, "product_setting": "order", "open_range": "all", "create_time": "17/3/2025 10:40:28", "update_time": "26/6/2025 14:12:57", "online_time": "26/6/2025 14:12:57", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1750918376939, "doc_id": "2503171040275489086", "en_doc_id": "2503171040275629101", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 42, "operate_id_list": "ChinaTelecom,ChinaMobile,Singtel,HongkongHKT"}, {"opgw_product_key": "addCapacitykehuduan", "product_id": "2503171140416759129", "product_name": "生产能力（客户端，api调测）金涛", "product_name_en": "addCapacity", "product_desc": "生产能力（客户端，api调测）", "product_desc_en": "addCapacitykehuduan", "status": "online", "classify": 42, "classify_two": 42, "product_label": null, "product_setting": "order", "open_range": "all", "create_time": "17/3/2025 11:41:54", "update_time": "23/6/2025 11:01:47", "online_time": "23/6/2025 11:01:47", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1750647707196, "doc_id": "2503171141538809135", "en_doc_id": "2503171141538879150", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 42, "operate_id_list": null}, {"opgw_product_key": "addCapacitypost", "product_id": "2507241141312429148", "product_name": "0724普通能力新增", "product_name_en": "addCapacitypost", "product_desc": "addCapacitypost", "product_desc_en": "addCapacitypost", "status": "online", "classify": 47, "classify_two": 47, "product_label": null, "product_setting": "order", "open_range": "all", "create_time": "24/7/2025 11:45:54", "update_time": "24/7/2025 11:45:54", "online_time": "24/7/2025 11:45:54", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1753328753662, "doc_id": "2507241145536669153", "en_doc_id": "2507241145538019168", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 5, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 47, "operate_id_list": "10999,home"}, {"opgw_product_key": "allupset", "product_id": "2406031759538499004", "product_name": "可修改能力（更改一次1）", "product_name_en": "default name", "product_desc": "用来评测能力操作的样本1", "product_desc_en": "default description", "status": "offline", "classify": 1, "classify_two": 10, "product_label": "101", "product_setting": "show", "open_range": "inner", "create_time": "3/6/2024 18:14:10", "update_time": "5/7/2024 15:29:43", "online_time": "6/6/2024 11:45:32", "product_score": null, "product_score_sub_item_json": null, "userid": 116, "username": "yangf94", "realname": "杨帆", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1720164583150, "doc_id": "2406031814102429017", "en_doc_id": "2411211117437459241", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 42, "operate_id_list": null}, {"opgw_product_key": "apikeyJIN", "product_id": "2503261436255659083", "product_name": "JIN生产能力apikey", "product_name_en": "apikeyJIN", "product_desc": "apikeyJIN", "product_desc_en": "apikeyJIN", "status": "online", "classify": 42, "classify_two": 42, "product_label": null, "product_setting": "order", "open_range": "all", "create_time": "26/3/2025 14:37:34", "update_time": "23/6/2025 11:02:38", "online_time": "23/6/2025 11:02:38", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1750647757525, "doc_id": "2503261437340139088", "en_doc_id": "2503261437340279103", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 42, "operate_id_list": null}, {"opgw_product_key": "appidbusiness", "product_id": "2503171715498069283", "product_name": "金涛测试类型能力appid", "product_name_en": "appidbusiness", "product_desc": "appidbusiness", "product_desc_en": "appidbusiness", "status": "online", "classify": 38, "classify_two": 38, "product_label": null, "product_setting": "order", "open_range": "all", "create_time": "17/3/2025 17:16:39", "update_time": "26/6/2025 10:42:12", "online_time": "26/6/2025 10:42:12", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1750905732409, "doc_id": "2503171716390219288", "en_doc_id": "2503171716390279303", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 1, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 38, "operate_id_list": "ChinaTelecom,ChinaMobile,Singtel,HongkongHKT,achentest,ChinaTelecomChinaTelecomChinaTelecom"}, {"opgw_product_key": "baonuanCapacity", "product_id": "2503131339247939023", "product_name": "测试类型保暖杯能力-通信服务质量", "product_name_en": "baonuanCapacity", "product_desc": "测试类型保暖杯能力能力概述是对能力所能解决的问题的简要中文描述,5-200个字符", "product_desc_en": "addCapacity baonuanCapacity", "status": "offline", "classify": 36, "classify_two": 38, "product_label": "101", "product_setting": "order", "open_range": "all", "create_time": "13/3/2025 13:48:47", "update_time": "23/6/2025 10:47:30", "online_time": "13/3/2025 13:48:47", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1750646850440, "doc_id": "2503131348468239030", "en_doc_id": "2503131348468409045", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": "", "auth_methods": null, "classify_new": 38, "operate_id_list": null}, {"opgw_product_key": "BulkCustomer", "product_id": "2502281414219959127", "product_name": "大客户能力", "product_name_en": "Bulk Customer Capability", "product_desc": "大客户能力，绑定六个接口", "product_desc_en": "Our Enterprise Customer Capability allows us to provide tailored services and advanced solutions to large organizations.", "status": "online", "classify": 45, "classify_two": 45, "product_label": null, "product_setting": "order", "open_range": "all", "create_time": "28/2/2025 14:17:31", "update_time": "23/6/2025 10:22:59", "online_time": "23/6/2025 10:22:59", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1750645379483, "doc_id": "2502281417309489132", "en_doc_id": "2502281417309599147", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 45, "operate_id_list": null}, {"opgw_product_key": "callforwardingsign", "product_id": "2505151629252459194", "product_name": "Call Forwarding Signal（呼转状态查询）", "product_name_en": "Call Forwarding Signal", "product_desc": "能够查询手机号的呼叫转移业务开通状态，该能力主要用来防止欺诈者使用呼叫转移服务进行诈骗行为。", "product_desc_en": "The ability to query the call forwarding service activation status of a phone number is mainly used to prevent fraudsters from using call forwarding services for scams.", "status": "online", "classify": 42, "classify_two": 42, "product_label": "101", "product_setting": "order", "open_range": "all", "create_time": "15/5/2025 16:30:41", "update_time": "22/7/2025 11:07:24", "online_time": "22/7/2025 11:07:24", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 1, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1753153644066, "doc_id": "2505151630409379199", "en_doc_id": "2505151630409429214", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 42, "operate_id_list": ""}, {"opgw_product_key": "capacity", "product_id": "2406131641483179002", "product_name": "最近联系人查询能力", "product_name_en": "Recent contact query capabilities", "product_desc": "进行最近联系人查询的能力", "product_desc_en": "Recent contact query capabilities", "status": "online", "classify": 45, "classify_two": 45, "product_label": null, "product_setting": "order", "open_range": "all", "create_time": "13/6/2024 16:43:18", "update_time": "23/6/2025 10:30:34", "online_time": "23/6/2025 10:30:34", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1750645834255, "doc_id": "2406131643183589013", "en_doc_id": "2411211117437669256", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 45, "operate_id_list": null}, {"opgw_product_key": "capacityJINTAO", "product_id": "2503171651432789232", "product_name": "金涛测试能力导入前后端", "product_name_en": "capacityJINTAO", "product_desc": "capacityJINTAO", "product_desc_en": "capacityJINTAO", "status": "offline", "classify": 36, "classify_two": 37, "product_label": null, "product_setting": "order", "open_range": "all", "create_time": "17/3/2025 16:52:42", "update_time": "23/6/2025 10:45:06", "online_time": "17/3/2025 16:52:42", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1750646706281, "doc_id": "2503171652419939237", "en_doc_id": "2503171652420019252", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": "1", "auth_methods": "101,102", "classify_new": 37, "operate_id_list": null}, {"opgw_product_key": "capacitykehuduan", "product_id": "2503171429386059179", "product_name": "金涛测试能力（客户端）", "product_name_en": "capacitykehuduan", "product_desc": "capacitykehuduan", "product_desc_en": "capacitykehuduan", "status": "offline", "classify": 36, "classify_two": 37, "product_label": null, "product_setting": "order", "open_range": "all", "create_time": "17/3/2025 14:30:38", "update_time": "23/6/2025 10:45:08", "online_time": "17/3/2025 14:33:00", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1750646708095, "doc_id": "2503171430377779184", "en_doc_id": "2503171430377839199", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 5, "is_mock": 1, "auth_processes": "", "auth_methods": null, "classify_new": 37, "operate_id_list": null}, {"opgw_product_key": "cccccccccccccccc", "product_id": "2411181009516559147", "product_name": "cccccccccccccccc", "product_name_en": "cccccccccccccccc", "product_desc": "5G虚拟专网产品通过网络切片、无线增强、融合大规模使用UPF/MEC等技术，针对时延敏感、算力需求大、需快速部署、但物理隔离/私密性要求不苛刻的场景，面向多个客户提供\"多租户“业务隔离服务,满足低时延和业务隔离的诉求5G虚拟专网产品通过网络切片、无线增强、融合大规模使用UPF/MEC等技术，针对时延敏感、算力需求大、需快速部署、但物理隔离/私密性要求不苛刻的场景，面向多个客户提供\"多租户“业务隔离", "product_desc_en": "cccccccccccccccc", "status": "deleted", "classify": 36, "classify_two": 37, "product_label": "101", "product_setting": "show", "open_range": "all", "create_time": "18/11/2024 10:10:18", "update_time": "23/6/2025 11:16:44", "online_time": "18/11/2024 10:16:43", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1750648604304, "doc_id": "2411181010182669152", "en_doc_id": "2411211117437919271", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 37, "operate_id_list": null}, {"opgw_product_key": "cloudCapacity", "product_id": "2411041753176179039", "product_name": "一次性短信验证码", "product_name_en": "one-time-password-sms", "product_desc": "通过短信（SMS）发送的一次性密码。这个密码是临时生成的，只在一次登录或交易中有效。", "product_desc_en": "one-time password sent via SMS. This password is temporarily generated and only valid for one login or transaction.", "status": "offline", "classify": 36, "classify_two": 38, "product_label": "101", "product_setting": "order", "open_range": "all", "create_time": "4/11/2024 17:54:58", "update_time": "18/12/2024 15:35:24", "online_time": "11/11/2024 17:04:49", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1734507323518, "doc_id": "2411041754580419050", "en_doc_id": "2411211117438139286", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 38, "operate_id_list": null}, {"opgw_product_key": "CorporateBusinessCard", "product_id": "2209130942236306204", "product_name": "企业名片能力", "product_name_en": "default name", "product_desc": "基于运营商独有的通信能力，提供用户通话过程中的附加信息传递的增值服务，包含主叫/被叫名片、挂机名片等多种类型，助力各企业提高商务沟通效率与营销精准度。", "product_desc_en": "default description", "status": "online", "classify": 42, "classify_two": 42, "product_label": "101", "product_setting": "order", "open_range": "all", "create_time": "26/4/2024 15:20:01", "update_time": "23/6/2025 10:15:36", "online_time": "23/6/2025 10:15:36", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1750644935867, "doc_id": "2405101017134329001", "en_doc_id": "2411211117438319301", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 42, "operate_id_list": null}, {"opgw_product_key": "cs1", "product_id": "2406051653576739227", "product_name": "1244444444", "product_name_en": "default name", "product_desc": "1111111111111111111111", "product_desc_en": "default description", "status": "deleted", "classify": 2, "classify_two": 11, "product_label": null, "product_setting": "show", "open_range": "all", "create_time": "5/6/2024 16:55:14", "update_time": "5/6/2024 18:17:42", "online_time": "5/6/2024 16:55:46", "product_score": null, "product_score_sub_item_json": null, "userid": 98, "username": "huzy39", "realname": "胡泽妍", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1717582661743, "doc_id": "2406051655135959234", "en_doc_id": "2411211117438489316", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 44, "operate_id_list": null}, {"opgw_product_key": "cs3", "product_id": "2406091823303839032", "product_name": "大赛测试能力4", "product_name_en": "match test 4", "product_desc": "11111111111111", "product_desc_en": "default description", "status": "offline", "classify": 36, "classify_two": 38, "product_label": null, "product_setting": "order", "open_range": "all", "create_time": "9/6/2024 18:25:00", "update_time": "23/6/2025 10:45:30", "online_time": "18/11/2024 10:03:58", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1750646730203, "doc_id": "2406091825003229038", "en_doc_id": "2411211117438669331", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 38, "operate_id_list": null}, {"opgw_product_key": "css", "product_id": "2406061138250819333", "product_name": "阿萨等等", "product_name_en": "default name", "product_desc": "111111111111111", "product_desc_en": "default description", "status": "deleted", "classify": 2, "classify_two": 11, "product_label": "101", "product_setting": "show", "open_range": "inner", "create_time": "6/6/2024 11:39:35", "update_time": "6/6/2024 11:39:59", "online_time": "6/6/2024 11:39:35", "product_score": null, "product_score_sub_item_json": null, "userid": 98, "username": "huzy39", "realname": "胡泽妍", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1717645199356, "doc_id": "2406061139349669344", "en_doc_id": "2411211117438839346", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 44, "operate_id_list": null}, {"opgw_product_key": "css1", "product_id": "2406061145515699372", "product_name": "陈述事实", "product_name_en": "default name", "product_desc": "1111111111111", "product_desc_en": "default description", "status": "deleted", "classify": 2, "classify_two": 11, "product_label": null, "product_setting": "show", "open_range": "outer", "create_time": "6/6/2024 11:47:08", "update_time": "6/6/2024 11:47:21", "online_time": null, "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1717645640962, "doc_id": "2406061147078419378", "en_doc_id": "2411211117439049361", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 44, "operate_id_list": null}, {"opgw_product_key": "css2", "product_id": "2406061506509809408", "product_name": "演示用能力A", "product_name_en": "default name", "product_desc": "线上业务办理范围较为有限。数字平台的便利程度与其所能办理的业务范围紧密相关。尤其对村务数字平台而言，线上业务办理范围越广，村民使用数字平台的频率就越高A", "product_desc_en": "default description", "status": "deleted", "classify": 2, "classify_two": 11, "product_label": null, "product_setting": "show", "open_range": "all", "create_time": "6/6/2024 15:13:42", "update_time": "6/6/2024 15:20:18", "online_time": "6/6/2024 15:18:47", "product_score": null, "product_score_sub_item_json": null, "userid": 99, "username": "jiangsd3", "realname": "蒋少东", "orgid": 39, "org_name": "联通智网创新中心本部-网络数据室", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1717658417818, "doc_id": "2406061513424049421", "en_doc_id": "2411211117439259376", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 44, "operate_id_list": null}, {"opgw_product_key": "csshi", "product_id": "2405301827020079007", "product_name": "能力存在订购", "product_name_en": "default name", "product_desc": "测试新建能力测试新建能力测试新建能力测试新建能力测试新建能力测试新建能力测试新建能力测试新建能力测试新建能力测试新建能力测试新建能力测试新建能力测试新建能力测", "product_desc_en": "default description", "status": "offline", "classify": 2, "classify_two": 12, "product_label": "101", "product_setting": "show", "open_range": "all", "create_time": "30/5/2024 18:28:15", "update_time": "5/7/2024 15:29:56", "online_time": "9/6/2024 17:56:40", "product_score": null, "product_score_sub_item_json": null, "userid": 175, "username": "guoyq28", "realname": "郭永强", "orgid": 42, "org_name": "平顶山市郏县分公司", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1720164596032, "doc_id": "2405301828148189018", "en_doc_id": "2411211117439429391", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 43, "operate_id_list": null}, {"opgw_product_key": "csshi11", "product_id": "2405301955328239050", "product_name": "测试新建能力22222", "product_name_en": "default name", "product_desc": "测试新建能力2测试新建能力2", "product_desc_en": "default description", "status": "deleted", "classify": 2, "classify_two": 11, "product_label": "101", "product_setting": "order", "open_range": "all", "create_time": "30/5/2024 19:56:37", "update_time": "31/5/2024 14:48:51", "online_time": "31/5/2024 14:48:45", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1717138131112, "doc_id": "2405301956368089089", "en_doc_id": "2411211117439619406", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 44, "operate_id_list": null}, {"opgw_product_key": "DCLocationFeatureQue", "product_id": "2412181409097249533", "product_name": "位置特征查询", "product_name_en": "DCLocationFeatureQue", "product_desc": "位置特征查询", "product_desc_en": "DCLocationFeatureQue", "status": "online", "classify": 37, "classify_two": 37, "product_label": null, "product_setting": "order", "open_range": "all", "create_time": "18/12/2024 14:09:57", "update_time": "23/6/2025 15:49:55", "online_time": "23/6/2025 15:49:55", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1750664994994, "doc_id": "2412181409570779538", "en_doc_id": "2412181409570849553", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 37, "operate_id_list": null}, {"opgw_product_key": "dddddddddddddddddddd", "product_id": "2406031021392199136", "product_name": "智慧乡村相关接口", "product_name_en": "default name", "product_desc": "关于智慧乡村的搭建，应用技术的支持性接口数据提供的能力", "product_desc_en": "default description", "status": "offline", "classify": 36, "classify_two": 39, "product_label": null, "product_setting": "show", "open_range": "all", "create_time": "3/6/2024 10:23:01", "update_time": "23/6/2025 11:01:50", "online_time": "26/11/2024 11:02:34", "product_score": null, "product_score_sub_item_json": null, "userid": 116, "username": "yangf94", "realname": "杨帆", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1750647709551, "doc_id": "2406031023009199142", "en_doc_id": "2411211117439799421", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 39, "operate_id_list": null}, {"opgw_product_key": "DescribeAvailability", "product_id": "2501091813452199009", "product_name": "可用区能力", "product_name_en": "DescribeAvailability", "product_desc": "查询所有的可用区。", "product_desc_en": "DescribeAvailability", "status": "online", "classify": 37, "classify_two": 37, "product_label": "101", "product_setting": "order", "open_range": "all", "create_time": "9/1/2025 18:15:26", "update_time": "23/6/2025 11:28:11", "online_time": "23/6/2025 11:28:11", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1750649291263, "doc_id": "2501091815255619015", "en_doc_id": "2501091815255789030", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 3, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 37, "operate_id_list": null}, {"opgw_product_key": "DeviceIdentifier", "product_id": "2405111917047399001", "product_name": "DeviceIdentifier（用户设备信息查询）", "product_name_en": "default name", "product_desc": "基于Device Identifier查询IMEI号的能力，实现了对移动设备唯一身份的精确识别与追踪，助力防范网络犯罪，增强用户数字生活安全感。", "product_desc_en": "default description", "status": "online", "classify": 46, "classify_two": 46, "product_label": "101", "product_setting": "order", "open_range": "all", "create_time": "11/5/2024 19:17:05", "update_time": "23/6/2025 09:54:42", "online_time": "23/6/2025 09:54:42", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 1, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1750643681748, "doc_id": "2405111917047499002", "en_doc_id": "2411211117439969436", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 46, "operate_id_list": null}, {"opgw_product_key": "DSapplicationprofile", "product_id": "2412181402402049382", "product_name": "网络连接状态应用配置文件管理", "product_name_en": "DSapplicationprofile", "product_desc": "网络连接状态应用配置文件管理", "product_desc_en": "DSapplicationprofile", "status": "online", "classify": 37, "classify_two": 37, "product_label": "101", "product_setting": "order", "open_range": "all", "create_time": "18/12/2024 14:04:35", "update_time": "23/6/2025 15:27:04", "online_time": "23/6/2025 15:27:04", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1750663624036, "doc_id": "2412181404350059387", "en_doc_id": "2412181404350119402", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 37, "operate_id_list": null}, {"opgw_product_key": "DScallforwardingsign", "product_id": "2412181348448919188", "product_name": "呼转状态查询", "product_name_en": "queryUnconditionalCall", "product_desc": "呼转状态查询", "product_desc_en": "queryUnconditionalCall", "status": "online", "classify": 37, "classify_two": 37, "product_label": "101", "product_setting": "order", "open_range": "all", "create_time": "18/12/2024 13:50:15", "update_time": "23/6/2025 14:16:15", "online_time": "23/6/2025 14:16:15", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1750659375083, "doc_id": "2412181350150079193", "en_doc_id": "2412181350150159208", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 37, "operate_id_list": null}, {"opgw_product_key": "DSconnecitivity", "product_id": "2412181419055569698", "product_name": "网络连接状态分析订阅", "product_name_en": "DSconnecitivity", "product_desc": "网络连接状态分析订阅", "product_desc_en": "DSconnecitivity", "status": "online", "classify": 37, "classify_two": 37, "product_label": "101", "product_setting": "order", "open_range": "all", "create_time": "18/12/2024 14:19:59", "update_time": "23/6/2025 15:25:22", "online_time": "23/6/2025 15:25:22", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1750663522441, "doc_id": "2412181419586649710", "en_doc_id": "2412181419586699725", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 37, "operate_id_list": null}, {"opgw_product_key": "DSconnectivityinsigh", "product_id": "2412181404447339418", "product_name": "网络连接状态分析", "product_name_en": "DSconnectivityinsigh", "product_desc": "网络连接状态分析", "product_desc_en": "DSconnectivityinsigh", "status": "online", "classify": 37, "classify_two": 37, "product_label": "101", "product_setting": "order", "open_range": "all", "create_time": "18/12/2024 14:06:01", "update_time": "23/6/2025 15:25:09", "online_time": "23/6/2025 15:25:09", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1750663509211, "doc_id": "2412181406010249423", "en_doc_id": "2412181406010309438", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 37, "operate_id_list": null}, {"opgw_product_key": "DSdevice", "product_id": "2412181415338149655", "product_name": "设备联网状态订阅", "product_name_en": "DSdevice", "product_desc": "设备联网状态订阅", "product_desc_en": "DSdevice", "status": "online", "classify": 37, "classify_two": 37, "product_label": "101", "product_setting": "order", "open_range": "all", "create_time": "18/12/2024 14:18:51", "update_time": "23/6/2025 15:23:54", "online_time": "23/6/2025 15:23:54", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1750663434445, "doc_id": "2412181418505999667", "en_doc_id": "2412181418506059682", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 37, "operate_id_list": null}, {"opgw_product_key": "DSdeviceeachability", "product_id": "2412181345270959109", "product_name": "设备联网状态", "product_name_en": "DSdeviceeachability", "product_desc": "设备联网状态", "product_desc_en": "DSdeviceeachability", "status": "online", "classify": 37, "classify_two": 37, "product_label": "101", "product_setting": "order", "open_range": "all", "create_time": "18/12/2024 13:46:45", "update_time": "23/6/2025 15:23:27", "online_time": "23/6/2025 15:23:27", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1750663407127, "doc_id": "2412181346451399121", "en_doc_id": "2412181346451479136", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 37, "operate_id_list": null}, {"opgw_product_key": "DSDeviceIdentifier", "product_id": "2412181420186959741", "product_name": "用户设备信息查询", "product_name_en": "DSDeviceIdentifier", "product_desc": "用户设备信息查询", "product_desc_en": "DSDeviceIdentifier", "status": "online", "classify": 37, "classify_two": 37, "product_label": "101", "product_setting": "order", "open_range": "all", "create_time": "18/12/2024 14:21:18", "update_time": "23/6/2025 15:25:56", "online_time": "23/6/2025 15:25:56", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1750663555911, "doc_id": "2412181421182679746", "en_doc_id": "2412181421183139761", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 37, "operate_id_list": null}, {"opgw_product_key": "DSdeviceroaming", "product_id": "2412181414162589612", "product_name": "设备漫游状态订阅", "product_name_en": "DSdeviceroaming", "product_desc": "设备漫游状态订阅", "product_desc_en": "DSdeviceroaming", "status": "online", "classify": 37, "classify_two": 37, "product_label": "101", "product_setting": "order", "open_range": "all", "create_time": "18/12/2024 14:15:13", "update_time": "23/6/2025 14:17:13", "online_time": "23/6/2025 14:17:13", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1750659433169, "doc_id": "2412181415130299617", "en_doc_id": "2412181415130429632", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 37, "operate_id_list": null}, {"opgw_product_key": "DSdeviceroamingstatu", "product_id": "2412181347224379152", "product_name": "设备漫游状态", "product_name_en": "DSdeviceroamingstatu", "product_desc": "设备漫游状态", "product_desc_en": "DSdeviceroamingstatu", "status": "online", "classify": 39, "classify_two": 39, "product_label": "101", "product_setting": "order", "open_range": "all", "create_time": "18/12/2024 13:48:31", "update_time": "23/6/2025 14:20:25", "online_time": "23/6/2025 14:20:25", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1750659625253, "doc_id": "2412181348313749157", "en_doc_id": "2412181348313879172", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 39, "operate_id_list": null}, {"opgw_product_key": "dsdsdsd", "product_id": "2409261836205399083", "product_name": "电子围栏", "product_name_en": "geo-fencing", "product_desc": "基于运营商独有的基站定位能力，配合自定义围栏圈定和实时事件监测功能，为各行业实现对特定区域内的用户活动进行精准管控、量化解析区域用户动态分布等功能。", "product_desc_en": "Based on the unique base station positioning capabilities of telecom operators, combined with custom geofencing and real-time event monitoring functions, it provides precise control and quantitative analysis of user activity dynamics within specific areas for various industries.", "status": "offline", "classify": 36, "classify_two": 37, "product_label": null, "product_setting": "order", "open_range": "all", "create_time": "26/9/2024 18:37:51", "update_time": "18/12/2024 15:39:30", "online_time": "11/11/2024 17:09:30", "product_score": null, "product_score_sub_item_json": null, "userid": 116, "username": "yangf94", "realname": "杨帆", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1734507569971, "doc_id": "2409261837514179095", "en_doc_id": "2411211117440129451", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 37, "operate_id_list": null}, {"opgw_product_key": "DSgeofencing", "product_id": "2412181336125709001", "product_name": "电子围栏", "product_name_en": "deleteGeoFencing", "product_desc": "电子围栏电子围栏", "product_desc_en": "deleteGeoFencing", "status": "online", "classify": 39, "classify_two": 39, "product_label": "101", "product_setting": "order", "open_range": "all", "create_time": "18/12/2024 13:39:09", "update_time": "23/6/2025 15:32:04", "online_time": "23/6/2025 15:32:04", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1750663923714, "doc_id": "2412181339090419006", "en_doc_id": "2412181339090609021", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 39, "operate_id_list": null}, {"opgw_product_key": "DSHighSpeedRealtimeC", "product_id": "2412181408054459497", "product_name": "高速实时人流分析", "product_name_en": "DSHighSpeedRealtimeC", "product_desc": "高速实时人流分析", "product_desc_en": "DSHighSpeedRealtimeC", "status": "online", "classify": 37, "classify_two": 37, "product_label": "101", "product_setting": "order", "open_range": "all", "create_time": "18/12/2024 14:08:59", "update_time": "23/6/2025 15:20:00", "online_time": "23/6/2025 15:20:00", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1750663200430, "doc_id": "2412181408585129502", "en_doc_id": "2412181408585189517", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 37, "operate_id_list": null}, {"opgw_product_key": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "product_id": "2412181110200029253", "product_name": "位置检索", "product_name_en": "createGeoFencing", "product_desc": "位置检索位置检索", "product_desc_en": "createGeoFencing", "status": "online", "classify": 39, "classify_two": 39, "product_label": "101", "product_setting": "order", "open_range": "all", "create_time": "18/12/2024 11:16:44", "update_time": "23/6/2025 15:30:30", "online_time": "23/6/2025 15:30:30", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1750663829636, "doc_id": "2412181116436859259", "en_doc_id": "2412181116436939274", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 26, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 39, "operate_id_list": null}, {"opgw_product_key": "DSlocationverificat", "product_id": "2412181046240079216", "product_name": "位置验证", "product_name_en": "DSlocationverificat", "product_desc": "DSlocationverificat", "product_desc_en": "DSlocationverificat", "status": "online", "classify": 37, "classify_two": 37, "product_label": "101", "product_setting": "order", "open_range": "all", "create_time": "18/12/2024 10:49:17", "update_time": "23/6/2025 14:15:37", "online_time": "23/6/2025 14:15:37", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1750659337388, "doc_id": "2412181049170219221", "en_doc_id": "2412181049170299236", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 1, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 37, "operate_id_list": null}, {"opgw_product_key": "DSNumbercarrierattri", "product_id": "2412181350227619224", "product_name": "号码运营商归属查询", "product_name_en": "DSNumbercarrierattri", "product_desc": "号码运营商归属查询", "product_desc_en": "DSNumbercarrierattri", "status": "online", "classify": 37, "classify_two": 37, "product_label": "101", "product_setting": "order", "open_range": "all", "create_time": "18/12/2024 13:51:37", "update_time": "23/6/2025 15:28:13", "online_time": "23/6/2025 15:28:13", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1750663692558, "doc_id": "2412181351371739229", "en_doc_id": "2412181351371799244", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 37, "operate_id_list": null}, {"opgw_product_key": "DSNumberRiskIdentifi", "product_id": "2412181353455809260", "product_name": "号码风险识别", "product_name_en": "DSNumberRiskIdentifi", "product_desc": "号码风险识别", "product_desc_en": "DSNumberRiskIdentifi", "status": "online", "classify": 37, "classify_two": 37, "product_label": "101", "product_setting": "order", "open_range": "all", "create_time": "18/12/2024 13:54:55", "update_time": "23/6/2025 15:29:27", "online_time": "23/6/2025 15:29:27", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1750663766669, "doc_id": "2412181354548309265", "en_doc_id": "2412181354548369280", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 37, "operate_id_list": null}, {"opgw_product_key": "DSnumberverification", "product_id": "2412181339296789037", "product_name": "号码验证", "product_name_en": "DSnumberverification", "product_desc": "号码验证号码验证", "product_desc_en": "DSnumberverification", "status": "online", "classify": 39, "classify_two": 39, "product_label": "101", "product_setting": "order", "open_range": "all", "create_time": "18/12/2024 13:41:19", "update_time": "23/6/2025 11:41:48", "online_time": "23/6/2025 11:41:48", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1750650107740, "doc_id": "2412181341190389042", "en_doc_id": "2412181341190529057", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 267, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 39, "operate_id_list": null}, {"opgw_product_key": "DSonetimebing", "product_id": "2503200922227829413", "product_name": "One Time Password Sms（一次性短信验证码）-bing", "product_name_en": "One Time Password Sms", "product_desc": "一次性短信验证码是一种安全机制，通过短信（SMS）发送一个临时生成的密码，该密码仅在一次登录或交易中有效。这项技术广泛应用于在线账户的登录验证、支付交易的安全确认、账户恢复和多因素认证等多个领域。", "product_desc_en": "One-time SMS verification codes are a security mechanism that sends a temporarily generated password via text message (SMS), which is only valid for a single login or transaction. This technology is widely used in various fields such as login verification for online accounts, secure confirmation of payment transactions, account recovery, and multi-factor authentication.", "status": "online", "classify": 45, "classify_two": 45, "product_label": "101", "product_setting": "order", "open_range": "all", "create_time": "20/3/2025 09:25:13", "update_time": "27/6/2025 10:18:27", "online_time": "27/6/2025 10:18:27", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 1, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1750990707131, "doc_id": "2503200925129099418", "en_doc_id": "2503200925129179433", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 45, "operate_id_list": "homee,10442,884455"}, {"opgw_product_key": "DSonetimepasswordsm", "product_id": "2412101621310289073", "product_name": "One Time Password Sms（一次性短信验证码）", "product_name_en": "One Time Password Sms", "product_desc": "一次性短信验证码是一种安全机制，通过短信（SMS）发送一个临时生成的密码，该密码仅在一次登录或交易中有效。这项技术广泛应用于在线账户的登录验证、支付交易的安全确认、账户恢复和多因素认证等多个领域。", "product_desc_en": "One-time SMS verification codes are a security mechanism that sends a temporarily generated password via text message (SMS), which is only valid for a single login or transaction. This technology is widely used in various fields such as login verification for online accounts, secure confirmation of payment transactions, account recovery, and multi-factor authentication.", "status": "online", "classify": 37, "classify_two": 37, "product_label": "101", "product_setting": "order", "open_range": "all", "create_time": "10/12/2024 16:23:35", "update_time": "23/6/2025 11:43:36", "online_time": "23/6/2025 11:43:36", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1750650215549, "doc_id": "2412101623352419078", "en_doc_id": "2412101623352929093", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": 1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 37, "operate_id_list": null}, {"opgw_product_key": "DSqodprovisioning", "product_id": "2412101044584369005", "product_name": "Qod Provisioning（QOD配置）", "product_name_en": "<PERSON><PERSON> Provisioning", "product_desc": "可将特定的网络服务质量（QoS）配置文件分配给特定的设备。", "product_desc_en": "Specific network quality of service (QoS) profiles can be assigned to specific devices.", "status": "online", "classify": 38, "classify_two": 38, "product_label": "101", "product_setting": "order", "open_range": "all", "create_time": "10/12/2024 10:49:35", "update_time": "23/6/2025 15:32:34", "online_time": "23/6/2025 15:32:34", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1750663953647, "doc_id": "2412101049353439010", "en_doc_id": "2412101049353599025", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": 1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 38, "operate_id_list": null}, {"opgw_product_key": "DSqosprofiles", "product_id": "2412181356579509339", "product_name": "qos配置文件", "product_name_en": "DSqosprofiles", "product_desc": "qos配置文件", "product_desc_en": "DSqosprofiles", "status": "online", "classify": 37, "classify_two": 37, "product_label": "101", "product_setting": "order", "open_range": "all", "create_time": "18/12/2024 13:58:37", "update_time": "23/6/2025 15:27:24", "online_time": "23/6/2025 15:27:24", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1750663643774, "doc_id": "2412181358369609344", "en_doc_id": "2412181358369669359", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 37, "operate_id_list": null}, {"opgw_product_key": "DSQualityOnDemand", "product_id": "2412101049579599041", "product_name": "Quality On Demand（差异化网络质量保障能力）", "product_name_en": "DSQualityOnDemand", "product_desc": "基于Quality On Demand API的按需优化能力，实现网络资源动态调配，为高要求应用场景提供定制化网络保障，显著提升用户体验与服务质量。", "product_desc_en": "The on-demand optimization capability enabled by Quality On Demand API realizes dynamic allocation of network resources, providing customized network assurance for high-demand applications, significantly enhancing user experience and service quality.", "status": "online", "classify": 38, "classify_two": 38, "product_label": "101", "product_setting": "order", "open_range": "all", "create_time": "10/12/2024 10:52:08", "update_time": "23/6/2025 13:57:36", "online_time": "23/6/2025 13:57:36", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1750658256327, "doc_id": "2412101052080119046", "en_doc_id": "2412101052080259061", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": 1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 38, "operate_id_list": null}, {"opgw_product_key": "DSRealtimequeryofcod", "product_id": "2412181456501999793", "product_name": "实时查询地市编码", "product_name_en": "DSRealtimequeryofcod", "product_desc": "实时查询地市编码", "product_desc_en": "DSRealtimequeryofcod", "status": "online", "classify": 37, "classify_two": 37, "product_label": "101", "product_setting": "order", "open_range": "all", "create_time": "18/12/2024 14:58:51", "update_time": "23/6/2025 15:37:47", "online_time": "23/6/2025 15:37:47", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1750664267428, "doc_id": "2412181458510549798", "en_doc_id": "2412181458510609813", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 3, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 37, "operate_id_list": null}, {"opgw_product_key": "DSRegionDeviceCount", "product_id": "2412181406375099454", "product_name": "区域设备数", "product_name_en": "DSRegionDeviceCount", "product_desc": "区域设备数", "product_desc_en": "DSRegionDeviceCount", "status": "offline", "classify": 37, "classify_two": 37, "product_label": "101", "product_setting": "order", "open_range": "all", "create_time": "18/12/2024 14:07:28", "update_time": "16/6/2025 09:27:01", "online_time": "18/12/2024 14:07:28", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1750037220927, "doc_id": "2412181407276879466", "en_doc_id": "2412181407276939481", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 37, "operate_id_list": null}, {"opgw_product_key": "DSScalperdeterminati", "product_id": "2412181355083889296", "product_name": "羊毛党判定", "product_name_en": "DSScalperdeterminati", "product_desc": "羊毛党判定", "product_desc_en": "DSScalperdeterminati", "status": "online", "classify": 37, "classify_two": 37, "product_label": "101", "product_setting": "order", "open_range": "all", "create_time": "18/12/2024 13:56:04", "update_time": "23/6/2025 15:26:50", "online_time": "23/6/2025 15:26:50", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1750663609885, "doc_id": "2412181356042679308", "en_doc_id": "2412181356042739323", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 37, "operate_id_list": null}, {"opgw_product_key": "DSShortMessageServ", "product_id": "2412101623539319109", "product_name": "Short Message Service（短信服务能力）", "product_name_en": "Short Message Service", "product_desc": "通过简单、高效、可靠的接口调用方式，向用户发送短信通知、验证码、告警信息等，是一种便捷的通信手段。", "product_desc_en": "By using a simple, efficient, and reliable interface calling method, sending SMS notifications, verification codes, alert messages, etc., to users is a convenient means of communication.", "status": "online", "classify": 40, "classify_two": 40, "product_label": "101", "product_setting": "order", "open_range": "all", "create_time": "10/12/2024 16:25:46", "update_time": "23/6/2025 15:37:23", "online_time": "23/6/2025 15:37:23", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1750664243417, "doc_id": "2412101625458539114", "en_doc_id": "2412101625459239129", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 6, "white_list_config": 1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 40, "operate_id_list": null}, {"opgw_product_key": "DSsimswap", "product_id": "2412181343092489073", "product_name": "SIM交换", "product_name_en": "DSsimswap", "product_desc": "SIM交换", "product_desc_en": "DSsimswap", "status": "online", "classify": 39, "classify_two": 39, "product_label": "101", "product_setting": "order", "open_range": "all", "create_time": "18/12/2024 13:45:04", "update_time": "23/6/2025 14:46:30", "online_time": "23/6/2025 14:46:30", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1750661190177, "doc_id": "2412181345042419078", "en_doc_id": "2412181345042669093", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 39, "operate_id_list": null}, {"opgw_product_key": "DSsimswapsubscriptio", "product_id": "2412181410160599569", "product_name": "SIM更换订阅", "product_name_en": "DSsimswapsubscriptio", "product_desc": "SIM更换订阅", "product_desc_en": "DSsimswapsubscriptio", "status": "online", "classify": 37, "classify_two": 37, "product_label": "101", "product_setting": "order", "open_range": "all", "create_time": "18/12/2024 14:12:23", "update_time": "23/6/2025 11:40:53", "online_time": "23/6/2025 11:40:53", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1750650052542, "doc_id": "2412181412233889574", "en_doc_id": "2412181412233959589", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 2, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 37, "operate_id_list": null}, {"opgw_product_key": "DSUserTrajectoryInqu", "product_id": "2412181459002169829", "product_name": "用户轨迹查询", "product_name_en": "DSUserTrajectoryInqu", "product_desc": "用户轨迹查询", "product_desc_en": "DSUserTrajectoryInqu", "status": "online", "classify": 39, "classify_two": 39, "product_label": null, "product_setting": "order", "open_range": "all", "create_time": "18/12/2024 14:59:50", "update_time": "23/6/2025 15:44:13", "online_time": "23/6/2025 15:44:13", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1750664652842, "doc_id": "2412181459500719834", "en_doc_id": "2412181459500779849", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 39, "operate_id_list": null}, {"opgw_product_key": "DSVoicenotification", "product_id": "2412101619210429037", "product_name": "语音通知", "product_name_en": "Voice Notification", "product_desc": "通过自动电话呼叫的方式向用户提供信息传递服务。语音通知服务不仅提供了更加个性化的沟通方式，还能够确保信息的即时传递和高到达率。", "product_desc_en": "The service provides information delivery to users through automated telephone calls. Voice notification service not only offers a more personalized way of communication but also ensures the immediate delivery and high delivery rate of information.", "status": "offline", "classify": 36, "classify_two": 38, "product_label": null, "product_setting": "order", "open_range": "all", "create_time": "10/12/2024 16:21:19", "update_time": "13/6/2025 18:42:52", "online_time": "18/12/2024 00:07:15", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1749811372067, "doc_id": "2412101621195719042", "en_doc_id": "2412101621197619057", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": 2, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 38, "operate_id_list": null}, {"opgw_product_key": "DSVoiceverification", "product_id": "2412101616107119001", "product_name": "语音验证码", "product_name_en": "Voice Verification Code", "product_desc": "语音验证码是一种基于语音通知能力的技术，可以通过拨打特定的手机号码，向用户发送语音形式的验证码。这项功能广泛应用于登录验证、支付验证、多因素认证等多个领域，提供了一种简单而有效的安全措施。", "product_desc_en": "Voice verification code is a technology based on voice notification capabilities, which can send voice-based verification codes to users by calling a specific phone number. This feature is widely used in various fields such as login verification, payment verification, and multi-factor authentication, providing a simple and effective security measure.", "status": "offline", "classify": 36, "classify_two": 37, "product_label": null, "product_setting": "order", "open_range": "all", "create_time": "10/12/2024 16:19:09", "update_time": "12/6/2025 20:41:02", "online_time": "18/12/2024 00:07:33", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1749732062151, "doc_id": "2412101619090369006", "en_doc_id": "2412101619091139021", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": 2, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 37, "operate_id_list": null}, {"opgw_product_key": "ElectronicFence", "product_id": "2206150930394509794", "product_name": "Geofencing（电子围栏）", "product_name_en": "default name", "product_desc": "基于运营商独有的基站定位能力，配合自定义围栏圈定和实时事件监测功能，为各行业实现对特定区域内的用户活动进行精准管控、量化解析区域用户动态分布等功能。", "product_desc_en": "default description", "status": "offline", "classify": 36, "classify_two": 39, "product_label": null, "product_setting": "order", "open_range": "all", "create_time": "26/4/2024 15:20:01", "update_time": "18/12/2024 15:39:33", "online_time": "11/12/2024 22:03:18", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1734507573266, "doc_id": "2405101017142729016", "en_doc_id": "2411211117440299466", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 39, "operate_id_list": null}, {"opgw_product_key": "Geofencing", "product_id": "2411111944144559057", "product_name": "电子围栏", "product_name_en": "Geofencing", "product_desc": "基于运营商独有的基站定位能力，配合自定义围栏园定和实时事件监测功能，为各行业实现对特定区域内的用户活动进行精准管控、量化解析区域用户动态分布等功能。", "product_desc_en": "Get geographic position changes，entering or exiting specified areas notifications. receive notifications state changes.", "status": "online", "classify": 37, "classify_two": 37, "product_label": null, "product_setting": "order", "open_range": "all", "create_time": "11/11/2024 19:47:55", "update_time": "23/6/2025 15:31:44", "online_time": "23/6/2025 15:31:44", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1750663903699, "doc_id": "2411111947548679070", "en_doc_id": "2411211117440509481", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 37, "operate_id_list": null}, {"opgw_product_key": "getRegionalUserCount", "product_id": "2505151616023339101", "product_name": "区域用户数查询", "product_name_en": "Regional User Count Inquiry", "product_desc": "查询指定区域内的各小区实时用户数、小区总数及实时用户总数。", "product_desc_en": "Querying Real-time User Counts per Cell, Total Number of Cells, and the Overall Real-time User Total within a Specified Area.", "status": "online", "classify": 44, "classify_two": 44, "product_label": null, "product_setting": "order", "open_range": "all", "create_time": "15/5/2025 16:17:33", "update_time": "23/5/2025 17:51:31", "online_time": "23/5/2025 17:51:31", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1747993891236, "doc_id": "2505151617325729107", "en_doc_id": "2505151617325849122", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 44, "operate_id_list": null}, {"opgw_product_key": "getUserByCell", "product_id": "2409121735584889161", "product_name": "基站用户数查询", "product_name_en": "getUserByCell", "product_desc": "查询基站范围内存在的用户数量的能力", "product_desc_en": "The ability to query the number of users that exist within the scope of a base station", "status": "online", "classify": 42, "classify_two": 42, "product_label": null, "product_setting": "order", "open_range": "all", "create_time": "12/9/2024 17:39:02", "update_time": "23/6/2025 10:25:50", "online_time": "23/6/2025 10:25:50", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1750645550159, "doc_id": "2409121739019889173", "en_doc_id": "2411211117440719496", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 42, "operate_id_list": null}, {"opgw_product_key": "HSSSigning", "product_id": "2303201518155153280", "product_name": "HSS业务签约查询", "product_name_en": "default name", "product_desc": "基于HSS业务签约查询能力，助力企业实现对用户通信服务状态的精准把控，为各行业促进服务策略的精细化制定管理提供数据支持。", "product_desc_en": "default description", "status": "online", "classify": 36, "classify_two": 39, "product_label": null, "product_setting": "order", "open_range": "inner", "create_time": "26/4/2024 15:20:01", "update_time": "26/11/2024 09:59:40", "online_time": "26/11/2024 09:59:40", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1732586379970, "doc_id": "2405101017145769031", "en_doc_id": "2411211117440909511", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 39, "operate_id_list": null}, {"opgw_product_key": "iackde", "product_id": "2504011437373049071", "product_name": "能力分类", "product_name_en": "d<PERSON><PERSON><PERSON><PERSON><PERSON>", "product_desc": "能力分类啊", "product_desc_en": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "status": "offline", "classify": 1, "classify_two": 10, "product_label": "101", "product_setting": "show", "open_range": "all", "create_time": "1/4/2025 14:39:31", "update_time": "23/6/2025 11:08:22", "online_time": "1/4/2025 14:40:14", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1750648101727, "doc_id": "2504011439307309076", "en_doc_id": "2504011439307389091", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 42, "operate_id_list": null}, {"opgw_product_key": "IEMessageService", "product_id": "2409271446047689290", "product_name": "短信国际服务", "product_name_en": "ShortMessageService", "product_desc": "短信验证码进行发送验证的国际服务专用能力", "product_desc_en": "SMS verification codes are sent to verify the exclusive ability of international services", "status": "online", "classify": 45, "classify_two": 45, "product_label": null, "product_setting": "order", "open_range": "all", "create_time": "27/9/2024 15:02:12", "update_time": "23/6/2025 10:29:06", "online_time": "23/6/2025 10:29:06", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1750645745932, "doc_id": "2409271502120139315", "en_doc_id": "2411211117441089526", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 45, "operate_id_list": null}, {"opgw_product_key": "IntMock1", "product_id": "2503221203168419709", "product_name": "能力测试1", "product_name_en": "IntMock1", "product_desc": "IntMock1", "product_desc_en": "IntMock1", "status": "offline", "classify": 36, "classify_two": 37, "product_label": null, "product_setting": "order", "open_range": "all", "create_time": "22/3/2025 12:04:55", "update_time": "23/6/2025 10:45:03", "online_time": "22/3/2025 19:05:26", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1750646703320, "doc_id": "2503221204548839714", "en_doc_id": "2503221204548889729", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 6, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": "", "auth_methods": null, "classify_new": 37, "operate_id_list": null}, {"opgw_product_key": "IntMock2", "product_id": "2503221204566609745", "product_name": "能力测试2", "product_name_en": "IntMock2", "product_desc": "IntMock2", "product_desc_en": "IntMock2", "status": "offline", "classify": 36, "classify_two": 37, "product_label": null, "product_setting": "order", "open_range": "all", "create_time": "22/3/2025 12:05:44", "update_time": "23/6/2025 10:45:01", "online_time": "22/3/2025 12:05:44", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1750646701069, "doc_id": "2503221205442649750", "en_doc_id": "2503221205442719765", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 37, "operate_id_list": null}, {"opgw_product_key": "IntPro1", "product_id": "2503221156475619631", "product_name": "能力生产1", "product_name_en": "pro1", "product_desc": "pro1pro1", "product_desc_en": "pro1pro1", "status": "online", "classify": 42, "classify_two": 42, "product_label": null, "product_setting": "order", "open_range": "all", "create_time": "22/3/2025 11:58:51", "update_time": "23/6/2025 10:57:56", "online_time": "23/6/2025 10:57:56", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1750647475731, "doc_id": "2503221158507489636", "en_doc_id": "2503221158507549651", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 42, "operate_id_list": null}, {"opgw_product_key": "IntPro2", "product_id": "2503221159008409667", "product_name": "能力生产2", "product_name_en": "IntPro2", "product_desc": "IntPro1", "product_desc_en": "IntPro1", "status": "online", "classify": 42, "classify_two": 42, "product_label": null, "product_setting": "order", "open_range": "all", "create_time": "22/3/2025 11:59:58", "update_time": "23/6/2025 10:58:39", "online_time": "23/6/2025 10:58:39", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1750647518807, "doc_id": "2503221159582479672", "en_doc_id": "2503221159582549687", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 1, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 42, "operate_id_list": null}, {"opgw_product_key": "JinanFB", "product_id": "2504091952267369140", "product_name": "济南前后端能力", "product_name_en": "<PERSON><PERSON> and Backend Capabilities", "product_desc": "济南前后端能力 用于测试前后端流api调测", "product_desc_en": "<PERSON><PERSON> and Backend Capabilities", "status": "online", "classify": 44, "classify_two": 44, "product_label": null, "product_setting": "order", "open_range": "outer", "create_time": "9/4/2025 19:59:03", "update_time": "9/4/2025 20:05:00", "online_time": "9/4/2025 20:05:00", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1744200299574, "doc_id": "2504091959034549145", "en_doc_id": "2504091959034629160", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 44, "operate_id_list": null}, {"opgw_product_key": "JINCapacityys", "product_id": "2503261431254569047", "product_name": "JIN生产能力", "product_name_en": "JINCapacityys", "product_desc": "JINCapacityys", "product_desc_en": "JINCapacityys", "status": "online", "classify": 42, "classify_two": 42, "product_label": null, "product_setting": "order", "open_range": "all", "create_time": "26/3/2025 14:34:28", "update_time": "27/6/2025 09:38:03", "online_time": "27/6/2025 09:38:03", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1750988283212, "doc_id": "2503261434282749052", "en_doc_id": "2503261434282889067", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 6, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 42, "operate_id_list": "ChinaTelecom,ChinaMobile,Singtel,HongkongHKT,achentest,ChinaTelecomChinaTelecomChinaTelecom,test1,test2,test11,test123,10999,home"}, {"opgw_product_key": "jinceCode", "product_id": "2410181407478999080", "product_name": "jin测试put能力", "product_name_en": "jinceCode", "product_desc": "jin测试put能力addCapacity", "product_desc_en": "addCapacity", "status": "offline", "classify": 36, "classify_two": 39, "product_label": "101", "product_setting": "order", "open_range": "all", "create_time": "18/10/2024 14:09:57", "update_time": "23/6/2025 10:45:28", "online_time": "10/1/2025 14:58:06", "product_score": null, "product_score_sub_item_json": null, "userid": 99, "username": "jiangsd3", "realname": "蒋少东", "orgid": 39, "org_name": "联通智网创新中心本部-网络数据室", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1750646727567, "doc_id": "2410181409569299092", "en_doc_id": "2411211117441249541", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 39, "operate_id_list": null}, {"opgw_product_key": "JINOIDCAuthorizati", "product_id": "2503131438586929098", "product_name": "测试类型JINOIDC授权前后", "product_name_en": "Test Type: Before and After JIN OIDC Authorization", "product_desc": "假设你在描述一个系统如何通过OIDC授权模式来测试特定的功能（如JIN）前后的情况，你可以这样写：", "product_desc_en": "Our system supports multiple test types, including Before and After JIN OIDC Authorization, which ensures that all authentication and authorization processes are functioning correctly.", "status": "offline", "classify": 36, "classify_two": 37, "product_label": null, "product_setting": "order", "open_range": "inner", "create_time": "13/3/2025 14:46:37", "update_time": "23/6/2025 10:47:28", "online_time": "11/6/2025 16:49:12", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1750646848085, "doc_id": "2503131446368819103", "en_doc_id": "2503131446368879118", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 5, "is_mock": 1, "auth_processes": "", "auth_methods": null, "classify_new": 37, "operate_id_list": null}, {"opgw_product_key": "jinput01", "product_id": "2410301911330589003", "product_name": "导入测试流程串通put", "product_name_en": "jinput01", "product_desc": "jinput01", "product_desc_en": "jinput01", "status": "offline", "classify": 1, "classify_two": 10, "product_label": null, "product_setting": "order", "open_range": "all", "create_time": "30/10/2024 19:13:29", "update_time": "28/5/2025 16:53:33", "online_time": "17/3/2025 11:01:04", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1748422413451, "doc_id": "2410301913290069005", "en_doc_id": "2411211117441449556", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 10, "is_mock": 0, "auth_processes": "1", "auth_methods": "101,102,103", "classify_new": 42, "operate_id_list": null}, {"opgw_product_key": "jintao01", "product_id": "2407151620132909151", "product_name": "呼转状态查询", "product_name_en": "call-forwarding-signal", "product_desc": "查询手机号的呼叫转移业务开通状态,以防止欺诈者使用呼叫转移服务进行诈骗。", "product_desc_en": "Query the call forwarding service activation status of a mobile phone number to prevent fraudsters from using the call forwarding service for scams.", "status": "offline", "classify": 36, "classify_two": 37, "product_label": "101", "product_setting": "order", "open_range": "all", "create_time": "15/7/2024 16:25:25", "update_time": "18/12/2024 15:42:36", "online_time": "11/11/2024 17:11:50", "product_score": null, "product_score_sub_item_json": null, "userid": 104, "username": "wangmz90", "realname": "王孟哲", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1734507756352, "doc_id": "2407151625248899115", "en_doc_id": "2411211117441649571", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 37, "operate_id_list": null}, {"opgw_product_key": "jintao02", "product_id": "2407161417182369004", "product_name": "常用联系人查询能力", "product_name_en": "Common contact queries", "product_desc": "进行常用联系人查询的能力", "product_desc_en": "Common contact queries", "status": "online", "classify": 45, "classify_two": 45, "product_label": null, "product_setting": "order", "open_range": "all", "create_time": "16/7/2024 14:19:27", "update_time": "23/6/2025 10:50:31", "online_time": "23/6/2025 10:50:31", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1750647031148, "doc_id": "2407161419272259008", "en_doc_id": "2411211117441829586", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 45, "operate_id_list": null}, {"opgw_product_key": "jintaotest02", "product_id": "2410301621577019003", "product_name": "金涛测试能力jintao-put-技术02", "product_name_en": "jintaotest02", "product_desc": "jintaotest02", "product_desc_en": "jintaotest02", "status": "online", "classify": 45, "classify_two": 45, "product_label": null, "product_setting": "order", "open_range": "all", "create_time": "30/10/2024 16:24:05", "update_time": "23/6/2025 11:04:10", "online_time": "23/6/2025 11:04:10", "product_score": null, "product_score_sub_item_json": null, "userid": 104, "username": "wangmz90", "realname": "王孟哲", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1750647849700, "doc_id": "2410301624049559010", "en_doc_id": "2411211117442069601", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 45, "operate_id_list": null}, {"opgw_product_key": "JINtest", "product_id": "2411071416425239011", "product_name": "号码验证", "product_name_en": "number-verification", "product_desc": "获取设备中当前使用的手机号码", "product_desc_en": "Get the phone number currently being used on the device.", "status": "deleted", "classify": 36, "classify_two": 38, "product_label": "101", "product_setting": "order", "open_range": "all", "create_time": "7/11/2024 14:19:17", "update_time": "23/6/2025 11:41:13", "online_time": "11/11/2024 17:03:19", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1750650072940, "doc_id": "2411071419170769022", "en_doc_id": "2411211117442339616", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 38, "operate_id_list": null}, {"opgw_product_key": "jlkadflakdf", "product_id": "2504011440204149109", "product_name": "测试啊", "product_name_en": "csaj<PERSON><PERSON><PERSON><PERSON><PERSON>", "product_desc": "测试测试测试测试测试", "product_desc_en": "ceshiodjafladflkjdl", "status": "online", "classify": 42, "classify_two": 42, "product_label": "101", "product_setting": "show", "open_range": "inner", "create_time": "1/4/2025 14:41:46", "update_time": "3/4/2025 14:17:41", "online_time": "3/4/2025 14:17:41", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1743661061330, "doc_id": "2504011441460249114", "en_doc_id": "2504011441460349129", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 42, "operate_id_list": null}, {"opgw_product_key": "jnqhdnl", "product_id": "2504091553192209074", "product_name": "济南前后端测试能力  接口vv03 jkMockvv1", "product_name_en": "jnqhdnljnqhdnl", "product_desc": "济南前后端测试能力", "product_desc_en": "jnqhdnljnqhdnljnqhdnl", "status": "offline", "classify": 38, "classify_two": 38, "product_label": null, "product_setting": "order", "open_range": "outer", "create_time": "9/4/2025 15:55:30", "update_time": "23/6/2025 10:44:56", "online_time": "9/4/2025 15:55:30", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1750646695882, "doc_id": "2504091555297749079", "en_doc_id": "2504091555297869094", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 38, "operate_id_list": null}, {"opgw_product_key": "jsd", "product_id": "2406051614505709083", "product_name": "大赛测试能力3", "product_name_en": "match test product3", "product_desc": "sdcsadc删除定时测试多次是端茶倒水擦上档次的删除定时彻底删除筛掉测试测试测试打车上档次是端茶倒水擦上档次山地车三多次山地车三多次山地车三多次的删除定时测试多次山地车三多次筛掉测试的", "product_desc_en": "default description scscscsdczxadcasdcxscwcscscxsdxcs dccwvscxds rgbfdfshdgbvrdgfbvcsdfsvcdfsvcsdfasvdcsdasfdvsdfcvcsdfs dvacxvcesd dcsadvrgcdfbrtgdf dgfdcsdzxc", "status": "offline", "classify": 36, "classify_two": 37, "product_label": null, "product_setting": "order", "open_range": "all", "create_time": "5/6/2024 16:17:54", "update_time": "23/6/2025 10:46:51", "online_time": "18/11/2024 10:04:02", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1750646811310, "doc_id": "2406051617543059096", "en_doc_id": "2411211117442609631", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 37, "operate_id_list": null}, {"opgw_product_key": "jzsnb", "product_id": "2406051636420979129", "product_name": "仅展示能力-内部", "product_name_en": "default name", "product_desc": "仅供展示的能力，开放范围为仅内部", "product_desc_en": "default description", "status": "online", "classify": 42, "classify_two": 42, "product_label": null, "product_setting": "show", "open_range": "inner", "create_time": "5/6/2024 16:43:33", "update_time": "23/6/2025 11:06:29", "online_time": "23/6/2025 11:06:29", "product_score": null, "product_score_sub_item_json": null, "userid": 99, "username": "jiangsd3", "realname": "蒋少东", "orgid": 39, "org_name": "联通智网创新中心本部-网络数据室", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1750647989347, "doc_id": "2406051643332479147", "en_doc_id": "2411211117442839646", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 42, "operate_id_list": null}, {"opgw_product_key": "jzsqb", "product_id": "2406051631516929094", "product_name": "仅展示能力-全体", "product_name_en": "default name", "product_desc": "仅展示能力，开放全体用户", "product_desc_en": "default description", "status": "online", "classify": 42, "classify_two": 42, "product_label": null, "product_setting": "show", "open_range": "all", "create_time": "5/6/2024 16:35:50", "update_time": "23/6/2025 11:07:55", "online_time": "23/6/2025 11:07:55", "product_score": null, "product_score_sub_item_json": null, "userid": 98, "username": "huzy39", "realname": "胡泽妍", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1750648075432, "doc_id": "2406051635495879107", "en_doc_id": "2411211117443229661", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 42, "operate_id_list": null}, {"opgw_product_key": "jzswb", "product_id": "2406051643536489127", "product_name": "仅展示-外部", "product_name_en": "default name", "product_desc": "仅展示能力，开放范围是仅外部", "product_desc_en": "default description", "status": "offline", "classify": 1, "classify_two": 10, "product_label": null, "product_setting": "order", "open_range": "all", "create_time": "5/6/2024 16:46:16", "update_time": "5/7/2024 15:29:28", "online_time": "9/6/2024 18:21:29", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1720164568365, "doc_id": "2406051646162309133", "en_doc_id": "2411211117443449676", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 42, "operate_id_list": null}, {"opgw_product_key": "kdgnb", "product_id": "2406051650368659201", "product_name": "可订购-内部123", "product_name_en": "default name", "product_desc": "可以进行订购的能力，开发范围为内部用户可以进行订购的能力，开发范围为内部用户可以进行订购的能力，开发范围为内部用户可以进行订购的能力，开发范围为内部用户可以进行", "product_desc_en": "default description", "status": "offline", "classify": 2, "classify_two": 13, "product_label": "101", "product_setting": "order", "open_range": "inner", "create_time": "5/6/2024 16:53:19", "update_time": "15/7/2024 11:49:43", "online_time": "15/7/2024 11:48:38", "product_score": null, "product_score_sub_item_json": null, "userid": 99, "username": "jiangsd3", "realname": "蒋少东", "orgid": 39, "org_name": "联通智网创新中心本部-网络数据室", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1721015382787, "doc_id": "2406051653187529207", "en_doc_id": "2411211117443669691", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 45, "operate_id_list": null}, {"opgw_product_key": "kdgqt", "product_id": "2406051646359689192", "product_name": "可订购-全体123", "product_name_en": "default name", "product_desc": "可以进行订购的能力，开发范围为全部用户", "product_desc_en": "default description", "status": "offline", "classify": 2, "classify_two": 11, "product_label": null, "product_setting": "order", "open_range": "all", "create_time": "5/6/2024 16:48:43", "update_time": "15/7/2024 11:50:00", "online_time": "9/6/2024 18:37:51", "product_score": null, "product_score_sub_item_json": null, "userid": 98, "username": "huzy39", "realname": "胡泽妍", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1721015400396, "doc_id": "2406051648432239157", "en_doc_id": "2411211117443909706", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 44, "operate_id_list": null}, {"opgw_product_key": "kdgwb", "product_id": "2406051648596219173", "product_name": "可订购-仅外部", "product_name_en": "default name", "product_desc": "可以进行订购的能力，开发范围为外部用户", "product_desc_en": "default description", "status": "offline", "classify": 2, "classify_two": 12, "product_label": null, "product_setting": "order", "open_range": "outer", "create_time": "5/6/2024 16:50:27", "update_time": "5/7/2024 15:29:20", "online_time": "9/6/2024 18:37:31", "product_score": null, "product_score_sub_item_json": null, "userid": 98, "username": "huzy39", "realname": "胡泽妍", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1720164560004, "doc_id": "2406051650266579188", "en_doc_id": "2411211117444169721", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 43, "operate_id_list": null}, {"opgw_product_key": "KKK0605", "product_id": "2406051624101449089", "product_name": "验证能力新建0605", "product_name_en": "default name", "product_desc": "验证能力新建0605验证能力新建0605验证能力新建0605验证能力新建0605", "product_desc_en": "default description", "status": "deleted", "classify": 1, "classify_two": 10, "product_label": "101", "product_setting": "order", "open_range": "all", "create_time": "5/6/2024 16:45:30", "update_time": "5/6/2024 17:45:07", "online_time": null, "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1717580707329, "doc_id": "2406051645304199170", "en_doc_id": "2411211117444379736", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 42, "operate_id_list": null}, {"opgw_product_key": "KKK999", "product_id": "2406051645594979132", "product_name": "验证新建能力", "product_name_en": "default name", "product_desc": "验证新建能力0605验证新建能力0605验证新建能力0605验证新建能力0605", "product_desc_en": "default description", "status": "offline", "classify": 2, "classify_two": 13, "product_label": null, "product_setting": "order", "open_range": "all", "create_time": "5/6/2024 16:53:04", "update_time": "23/6/2025 11:36:01", "online_time": "23/9/2024 09:44:43", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1750649760574, "doc_id": "2406051653042659210", "en_doc_id": "2411211117444549751", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 45, "operate_id_list": null}, {"opgw_product_key": "LawRectify", "product_id": "2303311439161595378", "product_name": "司法数据服务能力", "product_name_en": "default name", "product_desc": "基于中国联通的通信数据洞察以及建模分析能力打造面向社区矫正监管机构提供电子围栏用户状态查询能力行业短信等功能助力监管机构通过大数据对被矫正人员进行分析及预警功能", "product_desc_en": "default description", "status": "online", "classify": 36, "classify_two": 38, "product_label": null, "product_setting": "order", "open_range": "inner", "create_time": "26/4/2024 15:20:01", "update_time": "26/11/2024 10:55:33", "online_time": "26/11/2024 10:55:33", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1732589733075, "doc_id": "2405101017148809046", "en_doc_id": "2411211117444739766", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 38, "operate_id_list": null}, {"opgw_product_key": "listNetwork", "product_id": "2503211758312109560", "product_name": "大客户网管", "product_name_en": "Large Customer Network Management (LCNM)", "product_desc": "大客户网管业务查询", "product_desc_en": "Large Customer Network Management Business Query", "status": "online", "classify": 45, "classify_two": 45, "product_label": null, "product_setting": "order", "open_range": "all", "create_time": "21/3/2025 18:01:49", "update_time": "23/6/2025 10:21:43", "online_time": "23/6/2025 10:21:43", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1750645302904, "doc_id": "2503211801492179566", "en_doc_id": "2503211801492229581", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 45, "operate_id_list": null}, {"opgw_product_key": "LocalJINTAO", "product_id": "2503202022446159506", "product_name": "位置查询-生产-JINTAO", "product_name_en": "locationquery", "product_desc": "位置查询-生产-JINTAO", "product_desc_en": "locationquery", "status": "online", "classify": 44, "classify_two": 44, "product_label": "101", "product_setting": "order", "open_range": "all", "create_time": "20/3/2025 20:24:33", "update_time": "23/6/2025 09:58:28", "online_time": "23/6/2025 09:58:28", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1750643907518, "doc_id": "2503202024325759511", "en_doc_id": "2503202024325849526", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 44, "operate_id_list": null}, {"opgw_product_key": "<PERSON><PERSON><PERSON><PERSON>", "product_id": "2506131633169909036", "product_name": "Location Retrieval（位置检索）", "product_name_en": "Location Retrieval", "product_desc": "API提供了检索设备位置的能力。所获取的区域范围取决于订阅者所在位置的网络状况", "product_desc_en": "This API provides the ability to retrieve a device location. The retrieved area depends on the network conditions at the subscriber’s locati", "status": "offline", "classify": 44, "classify_two": 44, "product_label": "101", "product_setting": "order", "open_range": "all", "create_time": "13/6/2025 16:36:38", "update_time": "16/6/2025 23:39:29", "online_time": "13/6/2025 17:28:34", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1750088369297, "doc_id": "2506131636383189041", "en_doc_id": "2506131636384329056", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 44, "operate_id_list": null}, {"opgw_product_key": "LocationVerification", "product_id": "2505151620186529144", "product_name": "Location Verification（位置验证）", "product_name_en": "Location Verification", "product_desc": "API通过坐标或邮政/行政区划代码验证移动设备与指定位置的临近程度，精度范围在2至200公里内。", "product_desc_en": "API verifies proximity of mobile device to a location within given accuracy range (2-200km) via coordinates or postal/administrative code.", "status": "online", "classify": 44, "classify_two": 44, "product_label": "101", "product_setting": "order", "open_range": "all", "create_time": "15/5/2025 16:21:58", "update_time": "18/7/2025 17:38:26", "online_time": "18/7/2025 17:38:26", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 1, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1752831505716, "doc_id": "2505151621578829152", "en_doc_id": "2505151621578869167", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 44, "operate_id_list": ""}, {"opgw_product_key": "Mecinstance", "product_id": "2501101623189899005", "product_name": "Mec实例管控", "product_name_en": "Mec instance control", "product_desc": "Mec实例管控，包括进行增加，修改的操作", "product_desc_en": "Mec instance control, including adding, modifying operations", "status": "offline", "classify": 36, "classify_two": 37, "product_label": "101", "product_setting": "order", "open_range": "all", "create_time": "10/1/2025 16:26:30", "update_time": "23/6/2025 11:01:33", "online_time": "18/2/2025 15:24:05", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1750647692871, "doc_id": "2501101626300989010", "en_doc_id": "2501101626301169025", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 4, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 37, "operate_id_list": null}, {"opgw_product_key": "mockCallforwarding", "product_id": "2506121405192789858", "product_name": "mock Call Forwarding Signal（呼转状态查询）", "product_name_en": "Call Forwarding Signal", "product_desc": "能够查询手机号的呼叫转移业务开通状态，该能力主要用来防止欺诈者使用呼叫转移服务进行诈骗行为。", "product_desc_en": "The ability to query the call forwarding service activation status of a phone number is mainly used to prevent fraudsters from using call forwarding services for scams.", "status": "online", "classify": 37, "classify_two": 37, "product_label": "101", "product_setting": "order", "open_range": "all", "create_time": "12/6/2025 14:07:01", "update_time": "18/7/2025 17:28:37", "online_time": "18/7/2025 17:28:37", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1752830917491, "doc_id": "2506121407011919863", "en_doc_id": "2506121407011969878", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 64, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 37, "operate_id_list": ""}, {"opgw_product_key": "mockDeviceIdentifier", "product_id": "2506121418235329944", "product_name": "mockDevice Identifier（用户设备信息查询）", "product_name_en": "<PERSON><PERSON>", "product_desc": "基于Device Identifier查询IMEI号的能力，实现了对移动设备唯一身份的精确识别与追踪，助力防范网络犯罪，增强用户数字生活安全感。", "product_desc_en": "The capability to query IMEI numbers based on Device Identifier enables precise identification and tracking of unique mobile device identities, enhancing protection against cybercrime and bolstering users' sense of security in their digital lives.", "status": "online", "classify": 37, "classify_two": 37, "product_label": "101", "product_setting": "order", "open_range": "all", "create_time": "12/6/2025 14:20:31", "update_time": "18/7/2025 17:25:47", "online_time": "18/7/2025 17:25:47", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1752830747340, "doc_id": "2506121420311099949", "en_doc_id": "2506121420311159964", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 63, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 37, "operate_id_list": ""}, {"opgw_product_key": "mockLoc<PERSON><PERSON><PERSON><PERSON>", "product_id": "2506131719144369078", "product_name": "mock Location Retrieval（位置检索）", "product_name_en": "<PERSON><PERSON><PERSON><PERSON>", "product_desc": "API提供了检索设备位置的能力。所获取的区域范围取决于订阅者所在位置的网络状况", "product_desc_en": "This API provides the ability to retrieve a device location. The retrieved area depends on the network conditions at the subscriber’s locati", "status": "online", "classify": 39, "classify_two": 39, "product_label": "101", "product_setting": "order", "open_range": "all", "create_time": "13/6/2025 17:21:55", "update_time": "18/7/2025 17:23:16", "online_time": "18/7/2025 17:23:16", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1752830595873, "doc_id": "2506131721553329083", "en_doc_id": "2506131721553529098", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 19, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 39, "operate_id_list": ""}, {"opgw_product_key": "mockLocationVerifica", "product_id": "2506121354446549811", "product_name": "mock Location Verification（位置验证）", "product_name_en": "Location Verification", "product_desc": "API通过坐标或邮政/行政区划代码验证移动设备与指定位置的临近程度，精度范围在2至200公里内。", "product_desc_en": "API verifies proximity of mobile device to a location within given accuracy range (2-200km) via coordinates or postal/administrative code.", "status": "online", "classify": 39, "classify_two": 39, "product_label": "101", "product_setting": "order", "open_range": "all", "create_time": "12/6/2025 13:56:34", "update_time": "23/6/2025 14:14:44", "online_time": "23/6/2025 14:14:44", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1750659283833, "doc_id": "2506121356340359817", "en_doc_id": "2506121356340399832", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 58, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 39, "operate_id_list": null}, {"opgw_product_key": "mockNumberVerificat", "product_id": "2506121348420569771", "product_name": "mock Number Verification（号码验证）", "product_name_en": "Number Verification", "product_desc": "获取设备中当前使用的手机号码，并验证用户提供的手机号码是否与设备上实际使用的号码一致。这项功能广泛应用于身份验证、账户安全、防欺诈和个性化服务等多个领域，提供可靠的手机号码验证服务。", "product_desc_en": "Obtain the current phone number being used on the device and verify if the phone number provided by the user matches the actual number in use on the device. This feature is widely used in various fields such as identity verification, account security, fraud prevention, and personalized services, providing a reliable phone number verification service.", "status": "online", "classify": 37, "classify_two": 37, "product_label": "101", "product_setting": "order", "open_range": "all", "create_time": "12/6/2025 13:50:32", "update_time": "18/7/2025 17:30:22", "online_time": "18/7/2025 17:30:22", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1752831022192, "doc_id": "2506121350318049776", "en_doc_id": "2506121350318089791", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 58, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 37, "operate_id_list": ""}, {"opgw_product_key": "mockOTPvalidation", "product_id": "2506121434015760019", "product_name": "OTPvalidation（短信验证码能力）", "product_name_en": "OTPvalidation API", "product_desc": "基于OTPValidation API的即时验证技术，助力企业快速、准确完成用户鉴权，确保交易安全，促进了企业服务策略的精准制定与风险管理优化。", "product_desc_en": "Instant verification technology powered by OTPValidation API facilitates rapid and accurate user authentication for businesses, ensuring transaction security and promoting the precise formulation of service strategies and risk management optimization.", "status": "online", "classify": 37, "classify_two": 37, "product_label": null, "product_setting": "order", "open_range": "all", "create_time": "12/6/2025 14:35:23", "update_time": "3/7/2025 21:20:53", "online_time": "3/7/2025 21:20:53", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1751548853262, "doc_id": "2506121435230730025", "en_doc_id": "2506121435230780040", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 92, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 37, "operate_id_list": null}, {"opgw_product_key": "mockRegionUserCount", "product_id": "2506121430236949983", "product_name": "mockRegion Device Count（区域设备数）", "product_name_en": "Region Device Count", "product_desc": "Region Device Count（区域设备数）", "product_desc_en": "Region Device Count", "status": "online", "classify": 39, "classify_two": 39, "product_label": null, "product_setting": "order", "open_range": "all", "create_time": "12/6/2025 14:31:54", "update_time": "13/6/2025 19:22:01", "online_time": "13/6/2025 19:22:01", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1749813721233, "doc_id": "2506121431541849988", "en_doc_id": "2506121431541880003", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 68, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 39, "operate_id_list": null}, {"opgw_product_key": "mockShortMessageServ", "product_id": "2506121411274049900", "product_name": "mock短信服务能力", "product_name_en": "Short Message Service", "product_desc": "向用户发送短信通知、验证码、告警信息等。提供简单、高效、可靠的接口调用方式来实现短信的发送功能。", "product_desc_en": "Send SMS notifications, verification codes, alert messages, etc., to users. Offers a simple, efficient, and reliable API invocation method to implement SMS sending functionality.", "status": "online", "classify": 40, "classify_two": 40, "product_label": null, "product_setting": "order", "open_range": "all", "create_time": "12/6/2025 14:13:09", "update_time": "18/7/2025 17:48:06", "online_time": "18/7/2025 17:48:06", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1752832085633, "doc_id": "2506121413088969905", "en_doc_id": "2506121413090669920", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 56, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 40, "operate_id_list": ""}, {"opgw_product_key": "mockVoiceCode", "product_id": "2506121445437850067", "product_name": "mock语音验证码", "product_name_en": "Voice Verification Code", "product_desc": "是一种安全、便捷且高效的身份验证方式，广泛应用于各种需要验证用户身份的场景。", "product_desc_en": "It is a secure, convenient, and efficient method of identity authentication, extensively applied in various scenarios requiring user identity verification.", "status": "online", "classify": 37, "classify_two": 37, "product_label": null, "product_setting": "order", "open_range": "all", "create_time": "12/6/2025 14:47:07", "update_time": "13/6/2025 19:15:09", "online_time": "13/6/2025 19:15:09", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1749813308636, "doc_id": "2506121447067180072", "en_doc_id": "2506121447067230087", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 30, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 37, "operate_id_list": null}, {"opgw_product_key": "mockVoiceNotifySw", "product_id": "2506121121514439714", "product_name": "mock语音通知", "product_name_en": "voiceNotifySw", "product_desc": "进行语音通知内容选择的能力", "product_desc_en": "mockVoiceNotifySw", "status": "online", "classify": 37, "classify_two": 37, "product_label": null, "product_setting": "order", "open_range": "all", "create_time": "12/6/2025 11:23:54", "update_time": "13/6/2025 18:20:17", "online_time": "13/6/2025 18:20:17", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1749810016593, "doc_id": "2506121123537489719", "en_doc_id": "2506121123537529734", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 33, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 37, "operate_id_list": null}, {"opgw_product_key": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "product_id": "2503261743272919004", "product_name": "neng<PERSON> xinxi", "product_name_en": "neng<PERSON> xinxi", "product_desc": "nengli<PERSON><PERSON> nengliinxi nengliinxi", "product_desc_en": "nengliin<PERSON> nengliinxi nengliinxi nengliinxi", "status": "online", "classify": 12, "classify_two": 12, "product_label": null, "product_setting": "order", "open_range": "all", "create_time": "26/3/2025 17:44:45", "update_time": "23/6/2025 11:04:58", "online_time": "23/6/2025 11:04:58", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1750647897732, "doc_id": "2503261744450449009", "en_doc_id": "2503261744450669024", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 12, "operate_id_list": null}, {"opgw_product_key": "nlbtest", "product_id": "2506111421576159549", "product_name": "尼林丙测试", "product_name_en": "nlbtest", "product_desc": "尼林丙测试尼林丙测试尼林丙测试", "product_desc_en": "sdfdsfdsdfsdfsdfsfdsdfsdfsdfdsf", "status": "offline", "classify": 42, "classify_two": 42, "product_label": "101", "product_setting": "order", "open_range": "all", "create_time": "11/6/2025 17:47:34", "update_time": "16/6/2025 17:42:09", "online_time": "14/6/2025 03:01:21", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1750066928644, "doc_id": "2506111747335459676", "en_doc_id": "2506111747335499691", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 42, "operate_id_list": null}, {"opgw_product_key": "<PERSON><PERSON><PERSON>", "product_id": "2502071721562869002", "product_name": "反欺诈分类下的能力，非carmer的", "product_name_en": "<PERSON><PERSON><PERSON>", "product_desc": "反欺诈分类下的能力，非carmer的", "product_desc_en": "<PERSON><PERSON><PERSON>", "status": "online", "classify": 37, "classify_two": 37, "product_label": null, "product_setting": "order", "open_range": "all", "create_time": "7/2/2025 17:24:01", "update_time": "23/6/2025 11:27:35", "online_time": "23/6/2025 11:27:35", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1750649254500, "doc_id": "2502071724011029256", "en_doc_id": "2502071724011279271", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 4, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 37, "operate_id_list": null}, {"opgw_product_key": "Nonlinkage", "product_id": "2412161042390889032", "product_name": "大赛-不联动能力调用", "product_name_en": "Competition - Non linkage Ability Call", "product_desc": "大赛-不联动能力调用", "product_desc_en": "Competition - Non linkage Ability Call", "status": "online", "classify": 37, "classify_two": 37, "product_label": null, "product_setting": "order", "open_range": "all", "create_time": "16/12/2024 10:47:12", "update_time": "23/6/2025 14:33:08", "online_time": "23/6/2025 14:33:08", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1750660387609, "doc_id": "2412161047119469043", "en_doc_id": "2412161047119559058", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 5, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 37, "operate_id_list": null}, {"opgw_product_key": "NumberVerification", "product_id": "2505151607121479057", "product_name": "Number Verification（号码验证）", "product_name_en": "Number Verification", "product_desc": "获取设备中当前使用的手机号码，12并验证用户提供的手机号码是否与设备上实际使用的号码一致。这项功能广泛应用于身份验证、账户安全、防欺诈和个性化服务等多个领域，提供可靠的手机号码验证服务。1", "product_desc_en": "Obtain the current phone number being used on the device and verify if the phone number provided by the user matches the actual number in use on the device. This feature is widely used in various fields such as identity verification, account security, fraud prevention, and personalized services, providing a reliable phone number verification service.", "status": "online", "classify": 42, "classify_two": 42, "product_label": "101", "product_setting": "order", "open_range": "all", "create_time": "15/5/2025 16:08:33", "update_time": "20/6/2025 02:33:09", "online_time": "20/6/2025 02:33:09", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 1, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1750357989440, "doc_id": "2505151608328519064", "en_doc_id": "2505151608328989079", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 1, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 42, "operate_id_list": null}, {"opgw_product_key": "OIDCJINTAO", "product_id": "2503131353488389061", "product_name": "生产OIDC授权JINTAO能力（前，客户端）外部", "product_name_en": "OIDC Authorization for JINTAO Capability", "product_desc": "OIDC授权JINTAO能力 能力概述(中文)", "product_desc_en": "The system uses OIDC Authorization for JINTAO Capability to securely manage access and permissions.", "status": "online", "classify": 43, "classify_two": 43, "product_label": null, "product_setting": "order", "open_range": "all", "create_time": "13/3/2025 14:01:41", "update_time": "23/6/2025 10:59:36", "online_time": "23/6/2025 10:59:36", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1750647575522, "doc_id": "2503131401405749066", "en_doc_id": "2503131401405879081", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 43, "operate_id_list": null}, {"opgw_product_key": "opgwJtG2X0", "product_id": "2406051523292449031", "product_name": "极多能力（数字乡村建设是乡村振兴的战略方向，也是推进数字中国建设的重要内容。难点何在，怎样突破？）", "product_name_en": "default name", "product_desc": "智慧乡村建设湖北省宜昌市秭归县茅坪镇陈家坝村，76岁的村民鲁有朋打开IP电视上本村的云平台，在便民服务菜单中下单送煤气上门服务，很快便解决了家里的煤气更换问题。", "product_desc_en": "default description", "status": "offline", "classify": 36, "classify_two": 39, "product_label": "101", "product_setting": "order", "open_range": "all", "create_time": "5/6/2024 16:08:39", "update_time": "23/6/2025 11:01:52", "online_time": "26/11/2024 09:59:01", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1750647712116, "doc_id": "2406051608389119067", "en_doc_id": "2411211117445039781", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 39, "operate_id_list": null}, {"opgw_product_key": "opgwNZC", "product_id": "2411041715251849038", "product_name": "数科大数据（30接口）Anti Fraud 类能力", "product_name_en": "opgwNZC", "product_desc": "通过输入用户手机号，证件类型，证件号查询该手机号证件验证信息是否一致。", "product_desc_en": "aa1/base/business/capacity/addCapacity", "status": "online", "classify": 38, "classify_two": 38, "product_label": null, "product_setting": "order", "open_range": "all", "create_time": "4/11/2024 17:34:35", "update_time": "23/6/2025 15:43:14", "online_time": "23/6/2025 15:43:14", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1750664593853, "doc_id": "2411041734347559032", "en_doc_id": "2411211117445269796", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 38, "operate_id_list": null}, {"opgw_product_key": "OTPvalidation", "product_id": "2405111917056969017", "product_name": "mockOne Time Password Sms（一次性短信验证码）-真", "product_name_en": "default name", "product_desc": "基于OTPValidation API的即时验证技术，助力企业快速、准确完成用户鉴权，确保交易安全，促进了企业服务策略的精准制定与风险管理优化。", "product_desc_en": "default description", "status": "online", "classify": 42, "classify_two": 42, "product_label": "101", "product_setting": "order", "open_range": "all", "create_time": "11/5/2024 19:17:06", "update_time": "23/6/2025 09:49:51", "online_time": "23/6/2025 09:49:51", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 1, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1750643390813, "doc_id": "2405111922582109016", "en_doc_id": "2411211117445539811", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 42, "operate_id_list": null}, {"opgw_product_key": "postMockJINTAOmore", "product_id": "2503201353414949453", "product_name": "生产能力JINTAO多id前段流，后端流", "product_name_en": "http://10.186.253.178:15000/postMock", "product_desc": "http://10.186.253.178:15000/postMock", "product_desc_en": "http://10.186.253.178:15000/postMock", "status": "online", "classify": 42, "classify_two": 42, "product_label": null, "product_setting": "order", "open_range": "all", "create_time": "20/3/2025 13:54:56", "update_time": "23/6/2025 10:57:03", "online_time": "23/6/2025 10:57:03", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1750647422584, "doc_id": "2503201354561279458", "en_doc_id": "2503201354561349473", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 42, "operate_id_list": null}, {"opgw_product_key": "postmuck", "product_id": "2504170959008679029", "product_name": "济南postmuck能力", "product_name_en": "postmuck", "product_desc": "济南postmuck能力概述", "product_desc_en": "postmuck  capacity", "status": "online", "classify": 46, "classify_two": 46, "product_label": null, "product_setting": "order", "open_range": "all", "create_time": "17/4/2025 10:00:35", "update_time": "17/4/2025 10:00:35", "online_time": "17/4/2025 10:00:35", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1744855235105, "doc_id": "2504171000351079035", "en_doc_id": "2504171000352039050", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 22, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 46, "operate_id_list": null}, {"opgw_product_key": "QOD", "product_id": "2411111948537569089", "product_name": "QOD-差异化网络质量保障能力", "product_name_en": "QOD - Quality of Differentiation Network Assurance Capability", "product_desc": "为政府、企事业单位、移动互联网等客户提供的一种流量增值服务，是针对特定客户和特定应用提供的基于QoS的差异化网络服务的产品", "product_desc_en": "The QOD capability leverages advanced mobile communication technologies, such as 4G/5G core networks, to provide differentiated network quality assurance services in terms of bandwidth, latency, jitter, bit error rate, and other metrics for various users through policy control and capability openness. This capability ensures network stability and service quality across various application scenarios through precise network resource management and optimization.", "status": "offline", "classify": 36, "classify_two": 38, "product_label": null, "product_setting": "order", "open_range": "all", "create_time": "11/11/2024 19:54:07", "update_time": "18/12/2024 15:37:49", "online_time": "11/11/2024 20:21:28", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1734507469089, "doc_id": "2411111954073049101", "en_doc_id": "2411211117445859826", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 38, "operate_id_list": null}, {"opgw_product_key": "QODCapacity", "product_id": "2502260944460609013", "product_name": "QOD节电资源信息系统能力", "product_name_en": "QODCapacity", "product_desc": "QOD节电资源信息系统能力能力概述", "product_desc_en": "QODCapacity", "status": "offline", "classify": 36, "classify_two": 40, "product_label": "101", "product_setting": "order", "open_range": "all", "create_time": "26/2/2025 09:47:38", "update_time": "23/6/2025 11:12:26", "online_time": "26/2/2025 10:58:09", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1750648345729, "doc_id": "2502260947376619018", "en_doc_id": "2502260947376779033", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 40, "operate_id_list": null}, {"opgw_product_key": "qqqqqqqq", "product_id": "2503121019103139013", "product_name": "测试前端流", "product_name_en": "qqqqqq", "product_desc": "测试前端流测试前端流测试前端流", "product_desc_en": "qqqqqqqqqqqq", "status": "offline", "classify": 1, "classify_two": 10, "product_label": "101", "product_setting": "order", "open_range": "all", "create_time": "12/3/2025 10:21:29", "update_time": "12/3/2025 11:12:49", "online_time": "12/3/2025 10:21:29", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1741749169499, "doc_id": "2503121021287369019", "en_doc_id": "2503121021287539034", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": "1", "auth_methods": "101", "classify_new": 42, "operate_id_list": null}, {"opgw_product_key": "qqqqqqqqqq", "product_id": "2411181012262889210", "product_name": "qqqqqqqqqq", "product_name_en": "qqqqqqqqqq", "product_desc": "qqqqqqqqqq", "product_desc_en": "qqqqqqqqqq", "status": "deleted", "classify": 36, "classify_two": 37, "product_label": "101", "product_setting": "show", "open_range": "all", "create_time": "18/11/2024 10:12:52", "update_time": "23/6/2025 11:17:28", "online_time": "18/11/2024 10:12:52", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1750648647701, "doc_id": "2411181012519499215", "en_doc_id": "2411211117446369841", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 37, "operate_id_list": null}, {"opgw_product_key": "qqqqqqqqqqq", "product_id": "2503132024554969001", "product_name": "测试能力（后端流）", "product_name_en": "qqqqqq", "product_desc": "qqqq<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>qqq", "product_desc_en": "qqqqqqqqqqqq", "status": "offline", "classify": 36, "classify_two": 37, "product_label": "101", "product_setting": "order", "open_range": "all", "create_time": "13/3/2025 20:26:21", "update_time": "23/6/2025 10:47:26", "online_time": "13/3/2025 20:26:21", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1750646845958, "doc_id": "2503132026212459008", "en_doc_id": "2503132026212659023", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": "1", "auth_methods": "101", "classify_new": 37, "operate_id_list": null}, {"opgw_product_key": "qqqq<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>qqq", "product_id": "2503121023381299086", "product_name": "测试客户端上架", "product_name_en": "qqqqqq", "product_desc": "测试客户端测试客户端测试客户端测试客户端", "product_desc_en": "wwwwwwwwwwwwwwwwww", "status": "online", "classify": 42, "classify_two": 42, "product_label": "101", "product_setting": "order", "open_range": "all", "create_time": "12/3/2025 10:24:29", "update_time": "23/6/2025 10:55:09", "online_time": "23/6/2025 10:55:09", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1750647308791, "doc_id": "2503121024287409091", "en_doc_id": "2503121024287479106", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 6, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 42, "operate_id_list": null}, {"opgw_product_key": "QualityOnDemand", "product_id": "2405111922587389031", "product_name": "QualityOnDemand（差异化网络质量保障能力）", "product_name_en": "default name", "product_desc": "基于Quality On Demand API的按需优化能力，实现网络资源动态调配，为高要求应用场景提供定制化网络保障，显著提升用户体验与服务质量。", "product_desc_en": "default description", "status": "online", "classify": 37, "classify_two": 37, "product_label": "101", "product_setting": "order", "open_range": "all", "create_time": "11/5/2024 19:22:59", "update_time": "23/6/2025 09:56:20", "online_time": "23/6/2025 09:56:20", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1750643779807, "doc_id": "2405111922587409032", "en_doc_id": "2411211117446649856", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 37, "operate_id_list": null}, {"opgw_product_key": "RealtimeLocation", "product_id": "2205131527593869341", "product_name": "Device Location（实时位置能力）", "product_name_en": "default name", "product_desc": "基于运营商独有的基站定位能力，打造出基站用户数查询、批量基站用户数及用户详单查询、区域用户查询等一系列用户位置分析功能，深度赋能各行业实现精准定位", "product_desc_en": "default description", "status": "online", "classify": 39, "classify_two": 39, "product_label": "101", "product_setting": "order", "open_range": "all", "create_time": "26/4/2024 15:20:01", "update_time": "23/6/2025 15:38:26", "online_time": "23/6/2025 15:38:26", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1750664305672, "doc_id": "2405101017151679061", "en_doc_id": "2411211117446869871", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 39, "operate_id_list": null}, {"opgw_product_key": "RealtimeUserState", "product_id": "2206010914466389004", "product_name": "实时用户状态", "product_name_en": "default name", "product_desc": "提供精准识别设备状态能力，有效支撑应急、反诈等关键业务场景，为各类紧急与安全相关应用提供强有力的状态判断依据", "product_desc_en": "default description", "status": "online", "classify": 38, "classify_two": 38, "product_label": null, "product_setting": "order", "open_range": "all", "create_time": "26/4/2024 15:20:01", "update_time": "23/6/2025 15:49:48", "online_time": "23/6/2025 15:49:48", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1750664988337, "doc_id": "2405101017154469076", "en_doc_id": "2411211117447039886", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 38, "operate_id_list": null}, {"opgw_product_key": "RegionUserCount", "product_id": "2405111922597629047", "product_name": "mockRegionDeviceCount（区域设备数）", "product_name_en": "default name", "product_desc": "基于地理位置围栏技术，实现特定区域内设备数量的精准统计，极大地优化了救援资源部署和营销策略的精准度，提升了响应速度与效益。", "product_desc_en": "default description", "status": "online", "classify": 42, "classify_two": 42, "product_label": "101", "product_setting": "order", "open_range": "all", "create_time": "11/5/2024 19:23:00", "update_time": "23/6/2025 09:53:38", "online_time": "23/6/2025 09:53:38", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 1, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1750643618103, "doc_id": "2405111922597639048", "en_doc_id": "2411211117447329901", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 15, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 42, "operate_id_list": null}, {"opgw_product_key": "Riskvalidation", "product_id": "2410211127077219139", "product_name": "手机号风险验证能力", "product_name_en": "phonenumberdange", "product_desc": "通过验证手机号，判断用户是否存在潜在风险", "product_desc_en": "Through the verification of mobile phone number, to determine whether there is a potential risk to users", "status": "online", "classify": 45, "classify_two": 45, "product_label": null, "product_setting": "order", "open_range": "all", "create_time": "21/10/2024 11:34:55", "update_time": "23/6/2025 10:27:26", "online_time": "23/6/2025 10:27:26", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1750645645517, "doc_id": "2410211134546829150", "en_doc_id": "2411211117447739916", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 1, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 45, "operate_id_list": null}, {"opgw_product_key": "saaaaaaaaaa", "product_id": "2411181012533989231", "product_name": "saaaaaaaaaa", "product_name_en": "saaaaaaaaaa", "product_desc": "5G虚拟专网产品通过网络切片、无线增强、融合大规模使用UPF/MEC等技术，针对时延敏感、算力需求大、需快速部署、但物理隔离/私密性要求不苛刻的场景，面向多个客户提供\"多租户“业务隔离服务,满足低时延和业务隔离的诉求5G虚拟专网产品通过网络切片、无线增强、融合大规模使用UPF/MEC等技术，针对时延敏感、算力需求大、需快速部署、但物理隔离/私密性要求不苛刻的场景，面向多个客户提供\"多租户“业务隔离", "product_desc_en": "saaaaaaaaaa", "status": "deleted", "classify": 36, "classify_two": 37, "product_label": null, "product_setting": "show", "open_range": "all", "create_time": "18/11/2024 10:13:18", "update_time": "23/6/2025 11:17:25", "online_time": "18/11/2024 10:15:54", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1750648645496, "doc_id": "2411181013184129236", "en_doc_id": "2411211117448049931", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 37, "operate_id_list": null}, {"opgw_product_key": "sasaaa", "product_id": "2411211451002340299", "product_name": "测试能力名字长换行测试能力名字长换行（测试能力名字长换行）", "product_name_en": "sasaaa", "product_desc": "sasaaa", "product_desc_en": "sasaaa", "status": "deleted", "classify": 36, "classify_two": 37, "product_label": "101", "product_setting": "show", "open_range": "all", "create_time": "21/11/2024 14:51:41", "update_time": "23/6/2025 11:16:26", "online_time": "4/12/2024 19:53:27", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1750648585690, "doc_id": "2411211451408880304", "en_doc_id": "2411211451408970319", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 37, "operate_id_list": null}, {"opgw_product_key": "SCDSsimswap", "product_id": "2503181517287679337", "product_name": "SIM交换-生产类型能力", "product_name_en": "DSsimswap", "product_desc": "SIM交换", "product_desc_en": "DSsimswap", "status": "online", "classify": 44, "classify_two": 44, "product_label": "101", "product_setting": "order", "open_range": "all", "create_time": "18/3/2025 15:19:25", "update_time": "23/6/2025 10:11:18", "online_time": "23/6/2025 10:11:18", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1750644678116, "doc_id": "2503181519254649344", "en_doc_id": "2503181519254749359", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 44, "operate_id_list": null}, {"opgw_product_key": "sendMessageBackend", "product_id": "2506162034019299473", "product_name": "后端测试-发送短信", "product_name_en": "Back-end testing - Sending text messages", "product_desc": "后端测试-发送短信", "product_desc_en": "Back-end testing - Sending text messages", "status": "offline", "classify": 45, "classify_two": 45, "product_label": null, "product_setting": "order", "open_range": "all", "create_time": "16/6/2025 20:41:44", "update_time": "23/6/2025 15:02:04", "online_time": "23/6/2025 10:18:16", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1750662123652, "doc_id": "2506162041438919478", "en_doc_id": "2506162041439459493", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 45, "operate_id_list": null}, {"opgw_product_key": "ShortMessageService", "product_id": "2409121733153109133", "product_name": "短信通知服务", "product_name_en": "ShortMessageService", "product_desc": "短信通知文本模版的选择", "product_desc_en": "ShortMessageService", "status": "online", "classify": 42, "classify_two": 42, "product_label": null, "product_setting": "order", "open_range": "all", "create_time": "12/9/2024 17:35:54", "update_time": "18/7/2025 17:35:35", "online_time": "18/7/2025 17:35:35", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 1, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1752831335142, "doc_id": "2409121735544039145", "en_doc_id": "2411211117448309946", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 42, "operate_id_list": ""}, {"opgw_product_key": "skcode", "product_id": "2505302110267159369", "product_name": "三网短信", "product_name_en": "skcode", "product_desc": "skcodeskcodeskcode", "product_desc_en": "skcodeskcodeskcode", "status": "online", "classify": 45, "classify_two": 45, "product_label": "101", "product_setting": "order", "open_range": "all", "create_time": "30/5/2025 21:11:29", "update_time": "3/6/2025 12:06:30", "online_time": "3/6/2025 12:06:30", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1748923590499, "doc_id": "2505302111290609374", "en_doc_id": "2505302111291209389", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 77, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 45, "operate_id_list": null}, {"opgw_product_key": "ssssssssss", "product_id": "2503261704043359008", "product_name": "测试111", "product_name_en": "ssssssssss", "product_desc": "ssssssssss", "product_desc_en": "ssssssssss", "status": "online", "classify": 42, "classify_two": 42, "product_label": null, "product_setting": "order", "open_range": "all", "create_time": "26/3/2025 17:04:46", "update_time": "23/6/2025 11:37:43", "online_time": "23/6/2025 11:37:43", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1750649863343, "doc_id": "2503261704455359013", "en_doc_id": "2503261704455559028", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 42, "operate_id_list": null}, {"opgw_product_key": "ssssssssssssss", "product_id": "2411181007146019083", "product_name": "ssssssssssssss", "product_name_en": "ssssssssssssss", "product_desc": "5G虚拟专网产品通过网络切片、无线增强、融合大规模使用UPF/MEC等技术，针对时延敏感、算力需求大、需快速部署、但物理隔离/私密性要求不苛刻的场景，面向多个客户提供\"多租户“业务隔离服务,满足低时延和业务隔离的诉求5G虚拟专网产品通过网络切片、无线增强、融合大规模使用UPF/MEC等技术，针对时延敏感、算力需求大、需快速部署、但物理隔离/私密性要求不苛刻的场景，面向多个客户提供\"多租户“业务隔离", "product_desc_en": "ssssssssssssss", "status": "deleted", "classify": 36, "classify_two": 37, "product_label": "101", "product_setting": "show", "open_range": "all", "create_time": "18/11/2024 10:07:41", "update_time": "23/6/2025 11:16:40", "online_time": "18/11/2024 10:16:31", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1750648600240, "doc_id": "2411181007405889088", "en_doc_id": "2411211117448569961", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 37, "operate_id_list": null}, {"opgw_product_key": "sssssssssssssss", "product_id": "2411181007421559104", "product_name": "sssssssssssssss", "product_name_en": "sssssssssssssss", "product_desc": "5G虚拟专网产品通过网络切片、无线增强、融合大规模使用UPF/MEC等技术，针对时延敏感、算力需求大、需快速部署、但物理隔离/私密性要求不苛刻的场景，面向多个客户提供\"多租户“业务隔离服务,满足低时延和业务隔离的诉求5G虚拟专网产品通过网络切片、无线增强、融合大规模使用UPF/MEC等技术，针对时延敏感、算力需求大、需快速部署、但物理隔离/私密性要求不苛刻的场景，面向多个客户提供\"多租户“业务隔离", "product_desc_en": "ssssssssssssss", "status": "deleted", "classify": 36, "classify_two": 37, "product_label": "101", "product_setting": "show", "open_range": "all", "create_time": "18/11/2024 10:08:09", "update_time": "23/6/2025 11:16:38", "online_time": "18/11/2024 10:16:26", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1750648598210, "doc_id": "2411181008088549109", "en_doc_id": "2411211117448769976", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 37, "operate_id_list": null}, {"opgw_product_key": "<PERSON><PERSON><PERSON>", "product_id": "2411141625174159096", "product_name": "测试接口", "product_name_en": "tset api", "product_desc": "进行一个测试", "product_desc_en": "Conduct a test", "status": "offline", "classify": 36, "classify_two": 37, "product_label": null, "product_setting": "order", "open_range": "all", "create_time": "14/11/2024 16:30:06", "update_time": "23/6/2025 10:45:25", "online_time": "16/11/2024 21:58:15", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1750646725480, "doc_id": "2411141630065719005", "en_doc_id": "2411211117449009991", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 37, "operate_id_list": null}, {"opgw_product_key": "test0925test0925", "product_id": "2409251137029979005", "product_name": "验证0925", "product_name_en": "test0925", "product_desc": "验证0925验证0925验证0925验证0925", "product_desc_en": "test0925test0925", "status": "offline", "classify": 36, "classify_two": 39, "product_label": null, "product_setting": "order", "open_range": "all", "create_time": "25/9/2024 11:39:34", "update_time": "23/6/2025 10:52:27", "online_time": "16/1/2025 14:05:59", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1750647147296, "doc_id": "2409251139341209010", "en_doc_id": "2411211117449190006", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 39, "operate_id_list": null}, {"opgw_product_key": "testcreatemock", "product_id": "2506111649314329568", "product_name": "测试自动创建mock应用", "product_name_en": "Test the automatic creation of mock applications", "product_desc": "测试自动创建mock应用", "product_desc_en": "Test the automatic creation of mock applications", "status": "deleted", "classify": 37, "classify_two": 37, "product_label": null, "product_setting": "order", "open_range": "all", "create_time": "11/6/2025 16:52:01", "update_time": "11/6/2025 16:59:26", "online_time": "11/6/2025 16:52:01", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1749632365520, "doc_id": "2506111652008259633", "en_doc_id": "2506111652008339648", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 37, "operate_id_list": null}, {"opgw_product_key": "testlianbanestlian", "product_id": "2506251734219529113", "product_name": "测试能力联邦4", "product_name_en": "testlianbang", "product_desc": "testlianbangtestlianbangtestlianbang", "product_desc_en": "testlianbangtestlianbangtestlianbang", "status": "online", "classify": 42, "classify_two": 42, "product_label": "101", "product_setting": "show", "open_range": "all", "create_time": "25/6/2025 17:46:33", "update_time": "25/6/2025 18:49:43", "online_time": "25/6/2025 18:49:43", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1750848583125, "doc_id": "2506251746330579274", "en_doc_id": "2506251746330659289", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 42, "operate_id_list": "ChinaTelecom,ChinaMobile,Singtel,HongkongHKT,test1,test2"}, {"opgw_product_key": "testlianbang", "product_id": "2506251640188669007", "product_name": "测试能力联邦", "product_name_en": "testlianbang", "product_desc": "测试能力联邦测试能力联邦测试能力联邦", "product_desc_en": "testlianbangtestlianbangtestlianbang", "status": "offline", "classify": 42, "classify_two": 42, "product_label": "101", "product_setting": "show", "open_range": "all", "create_time": "25/6/2025 16:50:05", "update_time": "26/6/2025 10:51:58", "online_time": "25/6/2025 17:21:07", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1750906317951, "doc_id": "2506251650047589012", "en_doc_id": "2506251650048349027", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 42, "operate_id_list": "ChinaTelecom,ChinaMobile,Singtel"}, {"opgw_product_key": "testlianbangtestlian", "product_id": "2506251733193699077", "product_name": "测试能力联邦1", "product_name_en": "testlianbangtestlianbang", "product_desc": "testlianbangtestlianbangtestlianbangtestlianbang", "product_desc_en": "testlianbangtestlianbangtestlianbangtestlianbangtestlianbang", "status": "online", "classify": 42, "classify_two": 42, "product_label": "101", "product_setting": "show", "open_range": "all", "create_time": "25/6/2025 17:33:59", "update_time": "25/6/2025 17:33:59", "online_time": "25/6/2025 17:33:59", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1750844039318, "doc_id": "2506251733593189082", "en_doc_id": "2506251733593369097", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 42, "operate_id_list": "ChinaTelecom"}, {"opgw_product_key": "testMockBindApp", "product_id": "2506122022387940164", "product_name": "测试mock能力创建绑定应用", "product_name_en": "testMockBindApp", "product_desc": "测试mock能力创建绑定应用", "product_desc_en": "testMockBindApp", "status": "online", "classify": 44, "classify_two": 44, "product_label": null, "product_setting": "order", "open_range": "inner", "create_time": "12/6/2025 20:24:35", "update_time": "12/6/2025 20:24:54", "online_time": "12/6/2025 20:24:54", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1749731094044, "doc_id": "2506122024345980169", "en_doc_id": "2506122024346010184", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 44, "operate_id_list": null}, {"opgw_product_key": "testMockProductSync", "product_id": "2506121743591650116", "product_name": "测试创建mock能力自动同步应用", "product_name_en": "Test the ability to create mocks and automatically synchronize applications", "product_desc": "测试创建mock能力自动同步应用", "product_desc_en": "Test the ability to create mocks and automatically synchronize applications", "status": "deleted", "classify": 44, "classify_two": 44, "product_label": null, "product_setting": "order", "open_range": "all", "create_time": "12/6/2025 18:19:20", "update_time": "23/6/2025 11:02:30", "online_time": "12/6/2025 18:19:20", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1750647749802, "doc_id": "2506121819196070122", "en_doc_id": "2506121819196230137", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 1, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 44, "operate_id_list": null}, {"opgw_product_key": "testtesttest", "product_id": "2504031447024899002", "product_name": "济南获取token能力", "product_name_en": "test", "product_desc": "testtesttesttest", "product_desc_en": "testtesttesttest", "status": "online", "classify": 42, "classify_two": 42, "product_label": "101", "product_setting": "order", "open_range": "outer", "create_time": "3/4/2025 14:47:48", "update_time": "8/4/2025 15:10:39", "online_time": "8/4/2025 15:10:39", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1744096238679, "doc_id": "2504031447479399007", "en_doc_id": "2504031447486609022", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 27, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 42, "operate_id_list": null}, {"opgw_product_key": "testtesttesttest", "product_id": "2504031447582869039", "product_name": "ceshi济南put", "product_name_en": "testtesttesttest", "product_desc": "testtesttest", "product_desc_en": "testtesttesttesttest", "status": "online", "classify": 44, "classify_two": 44, "product_label": null, "product_setting": "order", "open_range": "all", "create_time": "3/4/2025 14:48:34", "update_time": "4/6/2025 20:37:39", "online_time": "4/6/2025 20:37:39", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1749040659285, "doc_id": "2504031448338509044", "en_doc_id": "2504031448339619059", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 247, "white_list_config": -1, "recommend_index": 4, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 44, "operate_id_list": null}, {"opgw_product_key": "trrrr", "product_id": "2411142116350249015", "product_name": "电风扇放松放松发顺丰沙发沙发沙发沙发沙发上", "product_name_en": "trrrr", "product_desc": "撒滴答滴答滴答滴答滴滴答答大大大", "product_desc_en": "sdaddddddddddddddddddd", "status": "deleted", "classify": 36, "classify_two": 37, "product_label": null, "product_setting": "show", "open_range": "all", "create_time": "14/11/2024 21:17:31", "update_time": "23/6/2025 11:16:51", "online_time": "14/11/2024 21:17:31", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1750648611123, "doc_id": "2411142117313909020", "en_doc_id": "2411211117449360021", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 37, "operate_id_list": null}, {"opgw_product_key": "Ucfsi", "product_id": "2411131146593049018", "product_name": "用户呼转签约查询（联合）", "product_name_en": "User Call Forwarding Subscription Inquiry (Unified)", "product_desc": "用户呼转签约查询（联合）实现了第三方应用通过输入用户手机号码即可查询该用户是否签约业务", "product_desc_en": "User Call Forwarding Subscription Inquiry (Unified) enables third-party applications to query whether a user has subscribed to the service by inputting the user's phone number.", "status": "online", "classify": 38, "classify_two": 38, "product_label": null, "product_setting": "order", "open_range": "all", "create_time": "13/11/2024 12:55:52", "update_time": "23/6/2025 15:39:11", "online_time": "23/6/2025 15:39:11", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1750664351299, "doc_id": "2411131255519209031", "en_doc_id": "2411211117449540036", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 38, "operate_id_list": null}, {"opgw_product_key": "UDMSigning", "product_id": "2207121555345619129", "product_name": "UDM业务签约查询", "product_name_en": "default name", "product_desc": "基于UDM业务签约查询能力，提供2/3G漫游限制业务查询、2/3G短信业务查询等一系列UDM业务签约能力，为企业提供全面、准确的用户服务信息。", "product_desc_en": "default description", "status": "online", "classify": 36, "classify_two": 39, "product_label": null, "product_setting": "order", "open_range": "inner", "create_time": "26/4/2024 15:20:01", "update_time": "26/11/2024 10:56:48", "online_time": "26/11/2024 10:56:48", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1732589808190, "doc_id": "2405101017157639091", "en_doc_id": "2411211117449750051", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 70, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 39, "operate_id_list": null}, {"opgw_product_key": "voiceCode", "product_id": "2409121726224979076", "product_name": "语音验证码", "product_name_en": "voiceCode", "product_desc": "进行语音验证的能力", "product_desc_en": "Please enter verification code", "status": "online", "classify": 42, "classify_two": 42, "product_label": null, "product_setting": "order", "open_range": "all", "create_time": "12/9/2024 17:29:47", "update_time": "23/6/2025 10:17:16", "online_time": "23/6/2025 10:17:16", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 1, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1750645035673, "doc_id": "2409121729469349088", "en_doc_id": "2411211117449970066", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 13, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 42, "operate_id_list": null}, {"opgw_product_key": "voiceNotifySw", "product_id": "2409121729564579104", "product_name": "语音通知", "product_name_en": "voiceNotifySw", "product_desc": "进行语音通知内容选择的能力", "product_desc_en": "voiceNotifySwvoiceNotifySw", "status": "online", "classify": 42, "classify_two": 42, "product_label": null, "product_setting": "order", "open_range": "all", "create_time": "12/9/2024 17:33:10", "update_time": "23/6/2025 10:19:17", "online_time": "23/6/2025 10:19:17", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 1, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1750645157017, "doc_id": "2409121733100109117", "en_doc_id": "2411211117450180081", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 8, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 42, "operate_id_list": null}, {"opgw_product_key": "VolteasSigning", "product_id": "2206061707376370000", "product_name": "VoLTEAS业务签约查询", "product_name_en": "default name", "product_desc": "基于VoLTEAS业务签约查询能力，提供无条件呼叫转移、遇忙呼叫转移、无应答呼叫转移等一系列VoLTEAS业务签约能力，帮助企业实现对用户通信服务状态的精准把控", "product_desc_en": "default description", "status": "online", "classify": 36, "classify_two": 39, "product_label": null, "product_setting": "order", "open_range": "inner", "create_time": "26/4/2024 15:20:01", "update_time": "26/11/2024 10:54:17", "online_time": "26/11/2024 10:54:17", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1732589656809, "doc_id": "2405101017160789106", "en_doc_id": "2411211117450350096", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 39, "operate_id_list": null}, {"opgw_product_key": "wmzTestEnglish1", "product_id": "2406131711088259005", "product_name": "王孟哲测试回调能力1王孟哲测试回调能力1王孟哲测试回调能力1王孟哲测试回调能力1王孟哲测试回调能力1王孟哲测试回调能力1王孟哲测试回调能力1", "product_name_en": "wmz Test english 1 modify", "product_desc": "时速多少多所多所多所", "product_desc_en": "<PERSON>z<PERSON> test English 1 Modify", "status": "online", "classify": 45, "classify_two": 45, "product_label": "101", "product_setting": "order", "open_range": "all", "create_time": "13/6/2024 17:14:20", "update_time": "26/6/2025 18:52:18", "online_time": "26/6/2025 18:52:18", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1750935137800, "doc_id": "2406131714199229016", "en_doc_id": "2411211117450530111", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 45, "operate_id_list": "test1,test2,test3,test4"}, {"opgw_product_key": "www", "product_id": "2503121022392789050", "product_name": "测试后端流", "product_name_en": "www", "product_desc": "测试后端流测试后端流测试后端流", "product_desc_en": "wwwwwwwwwwwwwwwwwww", "status": "offline", "classify": 1, "classify_two": 10, "product_label": "101", "product_setting": "order", "open_range": "all", "create_time": "12/3/2025 10:23:36", "update_time": "12/3/2025 11:42:37", "online_time": "12/3/2025 10:23:36", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": null, "is_gateway_check": null, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": null, "is_need_south_control_platform_audit": null, "south_control_platform_id": null, "call_num6_month": null, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": null, "read_use_notice_time": null, "is_data_up_report": null, "last_call_time": null, "is_inalive": null, "version": 1741750957396, "doc_id": "2503121023363299055", "en_doc_id": "2503121023363429070", "is_home_show": null, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": "1", "auth_methods": "102", "classify_new": 42, "operate_id_list": null}, {"opgw_product_key": "wwwwwwwwwwwwww", "product_id": "2411181008292769125", "product_name": "wwwwwwwwwwwwww", "product_name_en": "wwwwwwwwwwwwww", "product_desc": "5G虚拟专网产品通过网络切片、无线增强、融合大规模使用UPF/MEC等技术，针对时延敏感、算力需求大、需快速部署、但物理隔离/私密性要求不苛刻的场景，面向多个客户提供\"多租户“业务隔离服务,满足低时延和业务隔离的诉求5G虚拟专网产品通过网络切片、无线增强、融合大规模使用UPF/MEC等技术，针对时延敏感、算力需求大、需快速部署、但物理隔离/私密性要求不苛刻的场景，面向多个客户提供\"多租户“业务隔离", "product_desc_en": "wwwwwwwwwwwwww", "status": "deleted", "classify": 36, "classify_two": 37, "product_label": "101", "product_setting": "show", "open_range": "all", "create_time": "18/11/2024 10:09:04", "update_time": "23/6/2025 11:16:36", "online_time": "18/11/2024 10:16:20", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1750648596389, "doc_id": "2411181009038889130", "en_doc_id": "2411211117450750126", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 37, "operate_id_list": null}, {"opgw_product_key": "xin", "product_id": "2407160933290529011", "product_name": "新建能力123", "product_name_en": "x<PERSON><PERSON><PERSON><PERSON><PERSON>", "product_desc": "这是一个能力123", "product_desc_en": "this is a 123111", "status": "deleted", "classify": 1, "classify_two": 10, "product_label": null, "product_setting": "show", "open_range": "all", "create_time": "16/7/2024 09:35:39", "update_time": "16/7/2024 15:38:04", "online_time": "16/7/2024 09:35:39", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1721115484396, "doc_id": "2407160935388609007", "en_doc_id": "2411211117450970141", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 42, "operate_id_list": null}, {"opgw_product_key": "xxxxxxxxxxx", "product_id": "2411181013197929252", "product_name": "xxxxxxxxxxx", "product_name_en": "xxxxxxxxxxx", "product_desc": "5G虚拟专网产品通过网络切片、无线增强、融合大规模使用UPF/MEC等技术，针对时延敏感、算力需求大、需快速部署、但物理隔离/私密性要求不苛刻的场景，面向多个客户提供\"多租户“业务隔离服务,满足低时延和业务隔离的诉求5G虚拟专网产品通过网络切片、无线增强、融合大规模使用UPF/MEC等技术，针对时延敏感、算力需求大、需快速部署、但物理隔离/私密性要求不苛刻的场景，面向多个客户提供\"多租户“业务隔离", "product_desc_en": "xxxxxxxxxxx", "status": "deleted", "classify": 36, "classify_two": 37, "product_label": "101", "product_setting": "show", "open_range": "all", "create_time": "18/11/2024 10:13:46", "update_time": "23/6/2025 11:16:28", "online_time": "18/11/2024 10:15:46", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1750648587960, "doc_id": "2411181013460229257", "en_doc_id": "2411211117451140156", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 37, "operate_id_list": null}, {"opgw_product_key": "xxxxxxxxxxxxxxxx", "product_id": "2411181010289219168", "product_name": "xxxxxxxxxxxxxxxx", "product_name_en": "xxxxxxxxxxxxxxxx", "product_desc": "5G虚拟专网产品通过网络切片、无线增强、融合大规模使用UPF/MEC等技术，针对时延敏感、算力需求大、需快速部署、但物理隔离/私密性要求不苛刻的场景，面向多个客户提供\"多租户“业务隔离服务,满足低时延和业务隔离的诉求5G虚拟专网产品通过网络切片、无线增强、融合大规模使用UPF/MEC等技术，针对时延敏感、算力需求大、需快速部署、但物理隔离/私密性要求不苛刻的场景，面向多个客户提供\"多租户“业务隔离", "product_desc_en": "xxxxxxxxxxxxxxxx", "status": "deleted", "classify": 36, "classify_two": 37, "product_label": "101", "product_setting": "show", "open_range": "all", "create_time": "18/11/2024 10:10:53", "update_time": "23/6/2025 11:16:35", "online_time": "18/11/2024 10:16:12", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1750648594509, "doc_id": "2411181010533189173", "en_doc_id": "2411211117451350171", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 37, "operate_id_list": null}, {"opgw_product_key": "xxxxxxxxxxxxxxxxss", "product_id": "2411181011010409189", "product_name": "xxxxxxxxxxxxxxxx", "product_name_en": "xxxxxxxxxxxxxxxx", "product_desc": "xxxxxxxxxxxxxxxx", "product_desc_en": "xxxxxxxxxxxxxxxx", "status": "deleted", "classify": 36, "classify_two": 37, "product_label": null, "product_setting": "show", "open_range": "all", "create_time": "18/11/2024 10:11:27", "update_time": "23/6/2025 11:16:32", "online_time": "18/11/2024 10:11:27", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1750648592423, "doc_id": "2411181011270559194", "en_doc_id": "2411211117451630186", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 37, "operate_id_list": null}, {"opgw_product_key": "xyk", "product_id": "2411071416425239012", "product_name": "信用卡办理", "product_name_en": "JINtest", "product_desc": "JINtest", "product_desc_en": "JINtest", "status": "deleted", "classify": 36, "classify_two": 38, "product_label": null, "product_setting": "order", "open_range": "all", "create_time": "7/11/2024 14:19:17", "update_time": "8/11/2024 21:03:38", "online_time": "7/11/2024 14:19:17", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1731071018099, "doc_id": "2411071419170769022", "en_doc_id": "2411211117451820201", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 38, "operate_id_list": null}, {"opgw_product_key": "yhwkes", "product_id": "2406081542347239015", "product_name": "大赛测试能力5", "product_name_en": "match test 5", "product_desc": "能力概述能力概述能力概述能力概述", "product_desc_en": "xcscscsccadvscxzcsfdz svcdxsd sdvcsdzxcsd sdvcsdzvcsdcx", "status": "offline", "classify": 36, "classify_two": 38, "product_label": "101", "product_setting": "order", "open_range": "all", "create_time": "8/6/2024 18:44:18", "update_time": "23/6/2025 10:45:32", "online_time": "8/1/2025 11:39:00", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1750646732482, "doc_id": "2406081844177049016", "en_doc_id": "2411211117451990216", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 1, "auth_processes": null, "auth_methods": null, "classify_new": 38, "operate_id_list": null}, {"opgw_product_key": "yunxiCloudposition", "product_id": "2410251530141289002", "product_name": "云定位", "product_name_en": "CloudRhino - Cloud Location", "product_desc": "“云定位”通过移动通信网络获取终端的位置信息，结合地图信息，为政企客户提供工作手机号的位置信息服务。", "product_desc_en": "\"China Unicom CloudRhino\" is committed to driving the digital transformation of fundamental voice services, continuously rolling out applications tailored to the enterprise market. It supplies businesses with visual operation and digital management of communication behaviors and digital assets during the production process. The \"Cloud Positioning\" feature obtains terminal location information via mobile communication networks, integrating it with map data to provide location information services for government and enterprise customers using work mobile phones.", "status": "online", "classify": 44, "classify_two": 44, "product_label": null, "product_setting": "zhengqiCenter", "open_range": "all", "create_time": "25/10/2024 15:33:31", "update_time": "23/6/2025 10:51:51", "online_time": "23/6/2025 10:51:51", "product_score": null, "product_score_sub_item_json": null, "userid": 116, "username": "yangf94", "realname": "杨帆", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1750647110696, "doc_id": "2410251533312169007", "en_doc_id": "2411211117452160231", "is_home_show": 0, "home_show_order": null, "outer_id": "200552926442295296", "outer_info": "{\"businessCode\":\"YDW\",\"businessName\":\"云定位\",\"productId\":200552926442295296}", "call_num": 76, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 44, "operate_id_list": null}, {"opgw_product_key": "yunxiCloudRecording", "product_id": "2410251524237139001", "product_name": "云录音推送", "product_name_en": "CloudRhino - Cloud Recording", "product_desc": "“云录音”可通过企业客户授权，实现对客户全国范围的工作手机卡的录音功能，员工无需进行任何额外操作。", "product_desc_en": "\"China Unicom CloudRhino\" is devoted to promoting the digital transformation of basic voice services, continually releasing applications tailored to the enterprise market. It equips businesses with visual operation and digital management of communication activities and digital assets during production. The \"Cloud Recording\" feature, upon authorization from enterprise clients, enables the recording function for work mobile SIM cards nationwide, without requiring any additional actions from employees.", "status": "online", "classify": 45, "classify_two": 45, "product_label": null, "product_setting": "zhengqiCenter", "open_range": "all", "create_time": "25/10/2024 15:30:07", "update_time": "23/6/2025 10:53:07", "online_time": "23/6/2025 10:53:07", "product_score": null, "product_score_sub_item_json": null, "userid": 116, "username": "yangf94", "realname": "杨帆", "orgid": 38, "org_name": "联通智网创新中心本部-网络中台研发室", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1750647187436, "doc_id": "2410251530068859014", "en_doc_id": "2411211117452420246", "is_home_show": 0, "home_show_order": null, "outer_id": "244750413185847296", "outer_info": "{\"businessCode\":\"YLY\",\"businessName\":\"云录音\",\"productId\":244750413185847296}", "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 45, "operate_id_list": null}, {"opgw_product_key": "y<PERSON><PERSON><PERSON>zheng", "product_id": "2409121714481839021", "product_name": "三要素查询能力", "product_name_en": "Three-factor query", "product_desc": "进行姓名+手机号+身份证是否匹配的能力", "product_desc_en": "Please enter verification code", "status": "online", "classify": 45, "classify_two": 45, "product_label": null, "product_setting": "order", "open_range": "all", "create_time": "12/9/2024 17:18:37", "update_time": "23/6/2025 10:46:23", "online_time": "23/6/2025 10:46:23", "product_score": null, "product_score_sub_item_json": null, "userid": 1, "username": "operator", "realname": "operator", "orgid": 3, "org_name": "运营组织", "is_oneclick": null, "attempt_type": 0, "attempt_time": null, "auditor_type": null, "is_need_specification": 0, "is_gateway_check": -1, "specification": null, "protocol_type": "HTTP", "specification_param": null, "db_specification_param": null, "call_num30": 0, "is_need_south_control_platform_audit": 0, "south_control_platform_id": null, "call_num6_month": 0, "disorder_task_id": null, "is_test_data": 0, "order_success_notify_users": null, "self_define_auditors": null, "need_read_use_notice": 0, "read_use_notice_time": null, "is_data_up_report": 0, "last_call_time": null, "is_inalive": 0, "version": 1750646782976, "doc_id": "2409121718370769036", "en_doc_id": "2411211117452610261", "is_home_show": 0, "home_show_order": null, "outer_id": null, "outer_info": null, "call_num": 0, "white_list_config": -1, "recommend_index": 1, "is_mock": 0, "auth_processes": null, "auth_methods": null, "classify_new": 45, "operate_id_list": null}]