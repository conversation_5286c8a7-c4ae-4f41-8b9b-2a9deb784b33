#!/usr/bin/env python3
import json

def analyze_query_result():
    # 读取四个表的数据
    with open('db-schemas/database/i_product.json', 'r', encoding='utf-8') as f:
        products = json.load(f)
    
    with open('db-schemas/database/i_product_capability.json', 'r', encoding='utf-8') as f:
        product_capabilities = json.load(f)
    
    with open('db-schemas/database/i_capability.json', 'r', encoding='utf-8') as f:
        capabilities = json.load(f)
    
    with open('db-schemas/database/i_version.json', 'r', encoding='utf-8') as f:
        versions = json.load(f)
    
    print("=== 数据统计 ===")
    print(f"i_product 总记录数: {len(products)}")
    print(f"i_product_capability 总记录数: {len(product_capabilities)}")
    print(f"i_capability 总记录数: {len(capabilities)}")
    print(f"i_version 总记录数: {len(versions)}")
    
    # 统计各表的状态分布
    print("\n=== i_product 状态分布 ===")
    product_status_count = {}
    for p in products:
        status = p.get('status', 'null')
        product_status_count[status] = product_status_count.get(status, 0) + 1
    for status, count in sorted(product_status_count.items()):
        print(f"{status}: {count}")
    
    print("\n=== i_capability 状态分布 ===")
    capability_status_count = {}
    for c in capabilities:
        status = c.get('status', 'null')
        capability_status_count[status] = capability_status_count.get(status, 0) + 1
    for status, count in sorted(capability_status_count.items()):
        print(f"{status}: {count}")
    
    print("\n=== i_version 状态分布 ===")
    version_status_count = {}
    for v in versions:
        status = v.get('status', 'null')
        version_status_count[status] = version_status_count.get(status, 0) + 1
    for status, count in sorted(version_status_count.items()):
        print(f"{status}: {count}")
    
    # 模拟当前的查询逻辑
    print("\n=== 查询逻辑分析 ===")
    
    # 1. 筛选状态不是 'deleted' 的产品
    valid_products = [p for p in products if p.get('status') != 'deleted']
    print(f"状态不是 'deleted' 的产品数量: {len(valid_products)}")
    
    # 2. 构建关联关系映射
    # 产品 -> 接口的映射
    product_to_capabilities = {}
    for pc in product_capabilities:
        product_key = pc.get('opgw_product_key')
        capability_key = pc.get('opgw_capability_key')
        if product_key not in product_to_capabilities:
            product_to_capabilities[product_key] = []
        product_to_capabilities[product_key].append(capability_key)
    
    # 接口 -> 版本的映射
    capability_to_versions = {}
    for v in versions:
        capability_key = v.get('opgw_capability_key')
        if capability_key not in capability_to_versions:
            capability_to_versions[capability_key] = []
        capability_to_versions[capability_key].append(v)
    
    # 构建接口状态映射
    capability_status_map = {}
    for c in capabilities:
        capability_status_map[c.get('opgw_capability_key')] = c.get('status')
    
    # 3. 应用当前的查询条件
    valid_product_keys = set()
    
    for product in valid_products:
        product_key = product.get('opgw_product_key')
        
        # 检查是否存在符合条件的关联数据
        has_valid_relation = False
        
        # 获取该产品关联的接口
        related_capabilities = product_to_capabilities.get(product_key, [])
        
        for capability_key in related_capabilities:
            # 检查接口状态
            capability_status = capability_status_map.get(capability_key)
            if capability_status == 'deleted':
                continue
                
            # 检查该接口是否有非deleted状态的版本
            related_versions = capability_to_versions.get(capability_key, [])
            has_valid_version = any(v.get('status') != 'deleted' for v in related_versions)
            
            if has_valid_version:
                has_valid_relation = True
                break
        
        if has_valid_relation:
            valid_product_keys.add(product_key)
    
    print(f"符合当前查询条件的产品数量: {len(valid_product_keys)}")
    
    # 4. 详细分析被过滤掉的产品
    print("\n=== 被过滤掉的产品分析 ===")
    filtered_out_count = len(valid_products) - len(valid_product_keys)
    print(f"被过滤掉的产品数量: {filtered_out_count}")
    
    no_capability_count = 0
    no_valid_capability_count = 0
    no_valid_version_count = 0
    
    for product in valid_products:
        product_key = product.get('opgw_product_key')
        if product_key in valid_product_keys:
            continue
            
        # 分析被过滤的原因
        related_capabilities = product_to_capabilities.get(product_key, [])
        
        if not related_capabilities:
            no_capability_count += 1
            print(f"  产品 {product_key} ({product.get('product_name')}) - 没有关联接口")
        else:
            has_valid_capability = False
            has_valid_version_for_any_capability = False
            
            for capability_key in related_capabilities:
                capability_status = capability_status_map.get(capability_key)
                if capability_status != 'deleted':
                    has_valid_capability = True
                    related_versions = capability_to_versions.get(capability_key, [])
                    has_valid_version = any(v.get('status') != 'deleted' for v in related_versions)
                    if has_valid_version:
                        has_valid_version_for_any_capability = True
                        break
            
            if not has_valid_capability:
                no_valid_capability_count += 1
                print(f"  产品 {product_key} ({product.get('product_name')}) - 所有关联接口状态都是deleted")
            elif not has_valid_version_for_any_capability:
                no_valid_version_count += 1
                print(f"  产品 {product_key} ({product.get('product_name')}) - 所有关联版本状态都是deleted")
    
    print(f"\n过滤原因统计:")
    print(f"  没有关联接口: {no_capability_count}")
    print(f"  所有关联接口都是deleted: {no_valid_capability_count}")
    print(f"  所有关联版本都是deleted: {no_valid_version_count}")

if __name__ == "__main__":
    analyze_query_result()
